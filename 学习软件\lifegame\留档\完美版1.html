<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>人生游戏计划</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- 引入 Chart.js 图表库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      /* 默认采用"宁静自然"主题 */
      --primary-color: #588157;
      --background-gradient: linear-gradient(135deg, #E9F5DB, #CFD7C7);
      --text-color: #3A5A40;
    }
    /* 基础重置 */
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body {
      font-family: 'Segoe UI', 'Microsoft YaHei', system-ui;
      background: var(--background-gradient);
      color: var(--text-color);
      line-height: 1.6;
      padding: 20px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255,255,255,0.95);
      padding: 30px 40px;
      border-radius: 16px;
      box-shadow: 0 12px 24px rgba(0,0,0,0.1);
      backdrop-filter: blur(8px);
    }
    header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid rgba(79,70,229,0.1);
    }
    header h1 {
      font-size: 2.8em;
      margin-bottom: 15px;
      color: var(--primary-color);
      letter-spacing: -0.03em;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.05);
    }
    .date-info { font-size: 1.1em; color: #4a5568; margin-bottom: 15px; }
    .theme-selector { margin-bottom: 15px; }
    .theme-selector label { font-size: 1em; margin-right: 6px; }
    /* 新增全局控制按钮 */
    .global-controls { margin-bottom: 15px; }
    .global-controls .btn { margin: 0 5px; }
    section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid rgba(79,70,229,0.15);
      border-radius: 12px;
      background: linear-gradient(145deg, #ffffff, #f8f9ff);
      transition: transform 0.2s;
    }
    section:hover { transform: translateY(-2px); }
    section h2 {
      font-size: 1.8em;
      margin-bottom: 15px;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    button.info-btn {
      background: var(--primary-color);
      color: #fff;
      border: none;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      font-size: 0.9em;
      cursor: pointer;
      transition: all 0.2s;
    }
    button.info-btn:hover { background: #4338ca; transform: scale(1.1); }
    .info-box {
      display: none;
      background: #f0f4ff;
      border: 1px solid #c7d2fe;
      padding: 15px;
      margin-top: 15px;
      border-radius: 8px;
      font-size: 0.95em;
      line-height: 1.6;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    th, td { padding: 12px; text-align: center; border: 1px solid #e2e8f0; }
    th { background: var(--primary-color); color: white; font-weight: 600; }
    tr:nth-child(even) { background-color: #f8fafc; }
    input[type="number"], input[type="time"], input[type="text"], input[type="date"] {
      width: 90px;
      padding: 8px;
      border: 1px solid #cbd5e0;
      border-radius: 6px;
      transition: all 0.2s;
    }
    input[type="number"]:focus, input[type="time"]:focus, input[type="text"]:focus, input[type="date"]:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(79,70,229,0.1);
    }
    textarea {
      width: 100%;
      height: 150px;
      padding: 12px;
      border: 1px solid #cbd5e0;
      border-radius: 8px;
      resize: vertical;
      transition: all 0.2s;
    }
    /* 确保编辑时输入框宽度自适应单元格 */
    table td input {
      width: 100%;
      box-sizing: border-box;
    }
    .chart-container {
      margin: 20px 0;
      position: relative;
      height: 350px;
      background: white;
      border-radius: 12px;
      padding: 15px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
      overflow: hidden;
    }
    .btn {
      padding: 10px 24px;
      background: linear-gradient(135deg, var(--primary-color), #6366f1);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s;
    }
    .btn:hover { transform: translateY(-1px); box-shadow: 0 4px 6px rgba(79,70,229,0.15); }
    .form-group { margin: 8px 0; }
    label { margin-right: 6px; }
    .progress-container {
      height: 24px;
      background: #e2e8f0;
      border-radius: 12px;
      overflow: hidden;
      position: relative;
    }
    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, var(--primary-color), #818cf8);
      position: relative;
      transition: width 0.5s ease;
    }
    .progress-bar::after {
      content: "";
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      background-image: linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent);
      background-size: 40px 40px;
      animation: progress-stripes 2s linear infinite;
    }
    @keyframes progress-stripes {
      from { background-position: 40px 0; }
      to { background-position: 0 0; }
    }
    .op-btn {
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 0.85em;
      transition: all 0.2s;
    }
    .edit-btn { background: rgba(255,193,7,0.1); color: #d97706; border: 1px solid #f59e0b; }
    .delete-btn { background: rgba(220,53,69,0.1); color: #dc3545; border: 1px solid #f87171; }
    .dawn-progress {
      margin-top: 10px;
      font-size: 1em;
      background: #e9f7ef;
      padding: 10px;
      border: 1px solid #c3e6cb;
      border-radius: 5px;
    }
    .data-manager {
      margin-top: 20px;
      padding: 10px;
      background: #fff3cd;
      border: 1px solid #ffeeba;
      border-radius: 5px;
    }
    .data-manager textarea { height: 100px; }
    /* 历史记录与分页容器 */
    .history-container { margin-top: 10px; }
    @media (max-width: 768px) {
      .container { padding: 20px; }
      section { padding: 15px; }
      table { display: block; overflow-x: auto; }
    }
    /* 调整阶段性总结部分的UI显示 */
    #periodic-summary {
      box-sizing: border-box;
      border: 1px solid rgba(79,70,229,0.15);
      border-radius: 12px;
      background: linear-gradient(145deg, #ffffff, #f8f9ff);
      padding: 20px 20px 50px 20px;
      margin-bottom: 20px;
    }
    #periodic-summary h2 {
      text-align: center;
      margin-bottom: 10px;
    }
    #summary-date {
      text-align: center;
      font-size: 1.1em;
      margin-bottom: 20px;
    }
    /* 将三个图表部分并排显示 */
    .charts-row {
      display: flex;
      justify-content: space-between;
      gap: 20px;
    }
    .charts-row .chart-container {
      flex: 1;
      max-width: 32%;
      margin: 0;
      overflow: visible;
    }
    /* 幻构师经验记录图表样式 */
    #phantom-experience-chart-container {
      margin-top: 20px;
      width: 100%;
      overflow: visible;
    }
    #phantom-experience-chart-container h3 {
      text-align: center;
      margin-bottom: 10px;
      color: #80CBC4;
    }
    #phantom-chart {
      width: 100%;
      height: 350px;
    }
    
    /* 称号系统样式 */
    .title-status {
      background: rgba(255,255,255,0.8);
      padding: 15px;
      border-radius: 8px;
      margin: 15px 0;
      border: 1px solid var(--primary-color);
    }
    
    .title-status p {
      margin: 5px 0;
      font-size: 1.1em;
    }
    
    .title-name {
      color: var(--primary-color);
      font-weight: bold;
    }
    
    #title-rewards table {
      margin: 15px 0;
    }
    
    #title-rewards .progress-container {
      width: 200px;
    }
    
    #title-rewards h3 {
      margin-top: 20px;
      color: var(--primary-color);
      border-bottom: 2px solid rgba(88,129,87,0.2);
      padding-bottom: 5px;
    }
  </style>
  <script>
    // 新增主题切换函数，根据选项设置对应CSS变量
    function changeTheme(theme) {
      let root = document.documentElement;
      switch(theme) {
        case "xiaohong-1":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #E9F5DB, #CFD7C7)");
          root.style.setProperty("--primary-color", "#588157");
          root.style.setProperty("--text-color", "#3A5A40");
          break;
        case "xiaohong-2":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #F0F4F8, #B9D7EA)");
          root.style.setProperty("--primary-color", "#537DA6");
          root.style.setProperty("--text-color", "#2C3E50");
          break;
        case "xiaohong-3":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #FFF5E4, #FFD1D1)");
          root.style.setProperty("--primary-color", "#E14D2A");
          root.style.setProperty("--text-color", "#6B2737");
          break;
        case "xiaohong-4":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #F8F9FA, #E9ECEF)");
          root.style.setProperty("--primary-color", "#6C757D");
          root.style.setProperty("--text-color", "#212529");
          break;
        case "xiaohong-5":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #2D3E40, #387373)");
          root.style.setProperty("--primary-color", "#D9B70D");
          root.style.setProperty("--text-color", "#F2F2F2");
          break;
        case "xiaohong-6":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #F8F9FD, #E2E7FF)");
          root.style.setProperty("--primary-color", "#9AA5FF");
          root.style.setProperty("--text-color", "#6B7AA1");
          break;
        case "xiaohong-7":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #FFEEE5, #FFBFA3)");
          root.style.setProperty("--primary-color", "#004E89");
          root.style.setProperty("--text-color", "#001C38");
          break;
        case "xiaohong-8":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #F5E6E8, #E2C4D4)");
          root.style.setProperty("--primary-color", "#705861");
          root.style.setProperty("--text-color", "#3A2E39");
          break;
        case "xiaohong-9":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #E3F2EF, #B2DFDB)");
          root.style.setProperty("--primary-color", "#4DB6AC");
          root.style.setProperty("--text-color", "#00695C");
          break;
        case "xiaohong-10":
          root.style.setProperty("--background-gradient", "linear-gradient(135deg, #0F0F0F, #232D3F)");
          root.style.setProperty("--primary-color", "#4A6670");
          root.style.setProperty("--text-color", "#D9D9D9");
          break;
      }
    }

    // 全局变量及历史记录
    let attributes = {
      intelligence: 0,
      knowledge: 0,
      stamina: 0,
      willpower: 0,
      charisma: 0
    };
    let attributeHistory = [];
    let dawnTotalDays = 0;
    let dawnSuccessCount = 0;
    let dawnConsecutiveSuccess = 0;
    let attributeActiveCount = 0;
    let attributeLazyCount = 0;
    let phantomExperience = 1180;
    let phantomLazyCount = 0;
    const thresholds = {
      intelligence: [150, 500, 1500, 4000, 10000],
      knowledge: [150, 500, 1500, 4000, 10000],
      stamina: [10, 30, 60, 100],
      willpower: [10, 50, 100, 200, 300, 500, 800, 1000],
      charisma: [10, 50, 100, 200, 300, 500, 800, 1000],
      phantomExp: [1500, 3000, 5000, 8000, 12000, 18000, 26000, 36000]
    };

    // 全局图表变量及图表类型控制变量
    let pastChart, monthlyChart, yearlyChart, phantomMonthlyChart, phantomYearlyChart, phantomPastChart;
    let pastChartType = "line";
    let monthlyChartType = "line";
    let annualChartType = "line";
    let phantomMonthlyChartType = "line";
    let phantomYearlyChartType = "line";
    let phantomPastChartType = "line";

    // 数据过滤及聚合函数
    function filterRecordsByCurrentMonth(records) {
      const now = new Date();
      return records.filter(record => {
        let d = new Date(record.date);
        return d.getFullYear() === now.getFullYear() && d.getMonth() === now.getMonth();
      });
    }
    function filterRecordsByCurrentYear(records) {
      const now = new Date();
      return records.filter(record => {
        let d = new Date(record.date);
        return d.getFullYear() === now.getFullYear();
      });
    }
    function aggregateRecords(records, mode) {
      let grouped = {};
      records.forEach(record => {
        let d = new Date(record.date);
        let key = (mode === "month") ? (d.getFullYear() + "-" + (d.getMonth()+1)) : d.getFullYear().toString();
        if (!grouped[key] || new Date(record.date) > new Date(grouped[key].date)) {
          grouped[key] = record;
        }
      });
      let sortedKeys = Object.keys(grouped).sort((a, b) => new Date(grouped[a].date) - new Date(grouped[b].date));
      let labels = sortedKeys;
      let aggregatedRecords = sortedKeys.map(key => grouped[key]);
      let datasets = createDatasets(aggregatedRecords);
      return { labels, datasets };
    }
    function aggregateRecordsByMonth(records) {
      return aggregateRecords(records, "month");
    }
    function createDatasets(records) {
      return [
        { label: '智力', data: records.map(r => r.intelligence), borderColor: '#588157', fill: false },
        { label: '知识', data: records.map(r => r.knowledge), borderColor: '#3B82F6', fill: false },
        { label: '体力', data: records.map(r => r.stamina), borderColor: '#F97316', fill: false },
        { label: '意志', data: records.map(r => r.willpower), borderColor: '#DB2777', fill: false },
        { label: '魅力', data: records.map(r => r.charisma), borderColor: '#6D28D9', fill: false },
        { label: '幻构师经验', data: records.map(r => r.phantomExp), borderColor: '#80CBC4', fill: false }
      ];
    }
    // 更新图表的通用函数，增加 chartType 参数；当为条形图时设置背景色
    function updateChart(chart, labels, datasets, canvasId, chartType) {
      if(chart) { chart.destroy(); }
      if(chartType === "bar") {
        datasets.forEach(dataset => {
          dataset.backgroundColor = dataset.borderColor;
          dataset.borderWidth = 1;
        });
      }
      const ctx = document.getElementById(canvasId).getContext("2d");
      return new Chart(ctx, {
        type: chartType,
        data: { labels: labels, datasets: datasets },
        options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
    }
    // 更新【阶段性总结】图表：过往、每月、每年（单位切换已在之前代码中处理）
    function updateStageCharts() {
      let pastData = aggregateRecords(attributeHistory, pastAggregationMode);
      pastChart = updateChart(pastChart, pastData.labels, pastData.datasets, 'past-chart', pastChartType);
      let monthlyData = filterRecordsByCurrentMonth(attributeHistory);
      monthlyChart = updateChart(monthlyChart, monthlyData.map(r => r.date), createDatasets(monthlyData), 'monthly-chart', monthlyChartType);
      let currentYearRecords = filterRecordsByCurrentYear(attributeHistory);
      let annualData;
      if(annualAggregationMode === "day") {
        annualData = { labels: currentYearRecords.map(r => r.date), datasets: createDatasets(currentYearRecords) };
      } else {
        annualData = aggregateRecordsByMonth(currentYearRecords);
      }
      yearlyChart = updateChart(yearlyChart, annualData.labels, annualData.datasets, 'yearly-chart', annualChartType);
    }
    // 更新【幻构师经验记录】图表：每月、每年、过往
    function updatePhantomRecordCharts() {
      let phantomMonthlyData = filterRecordsByCurrentMonth(attributeHistory);
      phantomMonthlyChart = updateChart(phantomMonthlyChart, phantomMonthlyData.map(r => r.date), [
        { label: '幻构师经验', data: phantomMonthlyData.map(r => r.phantomExp), borderColor: '#80CBC4', fill: false }
      ], 'phantom-monthly-chart', phantomMonthlyChartType);
      let currentYearRecords = filterRecordsByCurrentYear(attributeHistory);
      let phantomAnnualData;
      if(phantomAnnualAggregationMode === "day") {
        phantomAnnualData = { labels: currentYearRecords.map(r => r.date), datasets: [
          { label: '幻构师经验', data: currentYearRecords.map(r => r.phantomExp), borderColor: '#80CBC4', fill: false }
        ] };
      } else {
        phantomAnnualData = aggregateRecordsByMonth(currentYearRecords);
        phantomAnnualData.datasets = [{
          label: '幻构师经验', data: phantomAnnualData.datasets[5].data, borderColor: '#80CBC4', fill: false
        }];
      }
      phantomYearlyChart = updateChart(phantomYearlyChart, phantomAnnualData.labels, phantomAnnualData.datasets, 'phantom-yearly-chart', phantomYearlyChartType);
      let phantomPastData = aggregateRecords(attributeHistory, phantomPastAggregationMode);
      phantomPastData.datasets = [{
          label: '幻构师经验', data: phantomPastData.datasets[5].data, borderColor: '#80CBC4', fill: false
      }];
      phantomPastChart = updateChart(phantomPastChart, phantomPastData.labels, phantomPastData.datasets, 'phantom-past-chart', phantomPastChartType);
    }
    // 单位切换函数（保持原有不变）
    let pastAggregationMode = "month";
    let annualAggregationMode = "day";
    let phantomAnnualAggregationMode = "day";
    let phantomPastAggregationMode = "month";
    function switchPastMode(value) {
      pastAggregationMode = value;
      updateStageCharts();
    }
    function switchAnnualMode(value) {
      annualAggregationMode = value;
      updateStageCharts();
    }
    function switchPhantomAnnualMode(value) {
      phantomAnnualAggregationMode = value;
      updatePhantomRecordCharts();
    }
    function switchPhantomPastMode(value) {
      phantomPastAggregationMode = value;
      updatePhantomRecordCharts();
    }
    // 新增图表类型切换函数
    function switchPastChartType(value) {
      pastChartType = value;
      updateStageCharts();
    }
    function switchMonthlyChartType(value) {
      monthlyChartType = value;
      updateStageCharts();
    }
    function switchAnnualChartType(value) {
      annualChartType = value;
      updateStageCharts();
    }
    function switchPhantomMonthlyChartType(value) {
      phantomMonthlyChartType = value;
      updatePhantomRecordCharts();
    }
    function switchPhantomYearlyChartType(value) {
      phantomYearlyChartType = value;
      updatePhantomRecordCharts();
    }
    function switchPhantomPastChartType(value) {
      phantomPastChartType = value;
      updatePhantomRecordCharts();
    }
    // 其它函数保持原有逻辑不变
    function getNextRequirement(current, arr) {
      for (let i = 0; i < arr.length; i++) {
        if (current < arr[i]) {
          return arr[i] - current;
        }
      }
      return "已达到最高等级";
    }
    // 新增：根据当前意志、魅力计算对应称号
    function updateWillpowerTitle() {
      let wp = attributes.willpower;
      let title = "无";
      if (wp >= 1000) title = "至尊意志";
      else if (wp >= 800) title = "巅峰勇者";
      else if (wp >= 500) title = "无畏先锋";
      else if (wp >= 300) title = "永恒决心";
      else if (wp >= 200) title = "不屈之魂";
      else if (wp >= 100) title = "钢铁意志";
      else if (wp >= 50) title = "坚石之心";
      else if (wp >= 10) title = "晨曦之志";
      return title;
    }
    function updateCharismaTitle() {
      let ch = attributes.charisma;
      let title = "无";
      if (ch >= 1000) title = "至高魅力";
      else if (ch >= 800) title = "魔力之源";
      else if (ch >= 500) title = "魅力无双";
      else if (ch >= 300) title = "无尽魅力";
      else if (ch >= 200) title = "卓越风度";
      else if (ch >= 100) title = "风采卓然";
      else if (ch >= 50) title = "星辉之光";
      else if (ch >= 10) title = "魅影初现";
      return title;
    }
    // 更新称号进度条和显示
    function updateTitleSystem() {
      // 更新意志称号进度
      const willTitles = [
        { req: 10, name: "晨曦微志", bonus: 5 },
        { req: 50, name: "坚石守心", bonus: 10 },
        { req: 100, name: "荆棘先锋", bonus: 15 },
        { req: 200, name: "钢铁铸意", bonus: 20 },
        { req: 300, name: "风暴不屈", bonus: 25 },
        { req: 500, name: "星辰恒志", bonus: 30 },
        { req: 800, name: "炽魂永燃", bonus: 40 },
        { req: 1000, name: "不朽意志", bonus: 50 }
      ];

      const chaTitles = [
        { req: 10, name: "萤火微光", bonus: 5 },
        { req: 50, name: "晨露流辉", bonus: 10 },
        { req: 100, name: "星芒初绽", bonus: 20 },
        { req: 200, name: "银月颂光", bonus: 25 },
        { req: 300, name: "日冕凝华", bonus: 30 },
        { req: 500, name: "虹彩冠冕", bonus: 35 },
        { req: 800, name: "天穹律光", bonus: 40 },
        { req: 1000, name: "万象圣辉", bonus: 50 }
      ];

      // 更新意志称号进度条
      willTitles.forEach(title => {
        const progress = Math.min(100, (attributes.willpower / title.req) * 100);
        const progressBar = document.getElementById(`progress-will-${title.req}`);
        if (progressBar) {
          progressBar.style.width = progress + "%";
          progressBar.textContent = Math.floor(progress) + "%";
        }
      });

      // 更新魅力称号进度条
      chaTitles.forEach(title => {
        const progress = Math.min(100, (attributes.charisma / title.req) * 100);
        const progressBar = document.getElementById(`progress-cha-${title.req}`);
        if (progressBar) {
          progressBar.style.width = progress + "%";
          progressBar.textContent = Math.floor(progress) + "%";
        }
      });

      // 更新当前称号显示
      const currentWillTitle = updateWillpowerTitle();
      const currentChaTitle = updateCharismaTitle();
      
      document.getElementById("current-will-title").textContent = currentWillTitle;
      document.getElementById("current-cha-title").textContent = currentChaTitle;

      // 计算并显示称号加成
      let willBonus = 0;
      let chaBonus = 0;

      for (const title of willTitles) {
        if (attributes.willpower >= title.req) {
          willBonus = title.bonus;
        } else {
          break;
        }
      }

      for (const title of chaTitles) {
        if (attributes.charisma >= title.req) {
          chaBonus = title.bonus;
        } else {
          break;
        }
      }

      document.getElementById("will-title-bonus").textContent = `+${willBonus}%`;
      document.getElementById("cha-title-bonus").textContent = `+${chaBonus}%`;

      return { willBonus, chaBonus };
    }

    // 在属性更新时调用称号系统更新
    function updateAttributesDisplay() {
      document.getElementById("intelligence").textContent = attributes.intelligence.toFixed(1);
      document.getElementById("knowledge").textContent = attributes.knowledge.toFixed(1);
      document.getElementById("stamina").textContent = attributes.stamina.toFixed(1);
      document.getElementById("willpower").textContent = attributes.willpower.toFixed(1);
      document.getElementById("charisma").textContent = attributes.charisma.toFixed(1);
      
      // 更新称号系统
      const { willBonus, chaBonus } = updateTitleSystem();
      
      // 在这里可以使用称号加成来调整属性增长
      // willBonus 影响魅力增长
      // chaBonus 影响全属性增长
    }
    function makeRowEditable(row) {
      row.dataset.originalHtml = row.innerHTML;
      let cells = row.cells;
      for (let i = 0; i < cells.length - 1; i++) {
        let cellText = cells[i].textContent;
        cells[i].innerHTML = "<input type='text' value='" + cellText.trim() + "'>";
      }
      cells[cells.length - 1].innerHTML = "<button class='op-btn save-btn' onclick='saveRow(this)'>保存</button> <button class='op-btn cancel-btn' onclick='cancelEdit(this)'>取消</button>";
    }
    // 修改后的 saveRow 函数：保存后更新对应data属性，并联动更新全局数据
    function saveRow(btn) {
      let row = btn.parentElement.parentElement;
      let cells = row.cells;
      for (let i = 0; i < cells.length - 1; i++) {
        let input = cells[i].querySelector("input");
        if (input) {
          cells[i].textContent = input.value;
        }
      }
      let tableId = row.parentElement.parentElement.id;
      if(tableId === "attribute-records-table") {
        cells[cells.length - 1].innerHTML = "<button class='op-btn edit-btn' onclick='editDailyAttributeRecord(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDailyAttributeRecord(this)'>删除</button>";
        // 更新当前行的 data-* 属性（注意：列序需与表头保持一致）
        // 列序：0-日期, 1-学习时长, 2-阅读页数, 3-绘画时长, 4-幻构师经验增量, 5-智力增量, 6-知识增量, 7-体力增量, 8-意志增量, 9-魅力增量, 10-备注
        row.setAttribute("data-phantomExp", cells[4].textContent.trim());
        row.setAttribute("data-intelligence", cells[5].textContent.trim());
        row.setAttribute("data-knowledge", cells[6].textContent.trim());
        row.setAttribute("data-stamina", cells[7].textContent.trim());
        row.setAttribute("data-willpower", cells[8].textContent.trim());
        row.setAttribute("data-charisma", cells[9].textContent.trim());
        // 重新计算属性总和并更新图表
        recalcAttributeRecords();
        updateStageCharts();
        updatePhantomRecordCharts();
      } else if(tableId === "dawn-records-table") {
        cells[cells.length - 1].innerHTML = "<button class='op-btn edit-btn' onclick='editDawnRecord(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDawnRecord(this)'>删除</button>";
        // 对于晨曦记录，假设编辑后调用重新计算晨曦打卡统计
        recalcDawnCounters();
        updateAttributesDisplay();
      } else if(tableId === "daily-summary-table") {
        cells[cells.length - 1].innerHTML = "<button class='op-btn edit-btn' onclick='editDailySummary(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDailySummary(this)'>删除</button>";
      } else if(tableId === "daily-records-table") {
        cells[cells.length - 1].innerHTML = "<button class='op-btn edit-btn' onclick='editDailyTotalRecord(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDailyTotalRecord(this)'>删除</button>";
        updateStageCharts();
        updatePhantomRecordCharts();
      }
    }
    function cancelEdit(btn) {
      let row = btn.parentElement.parentElement;
      row.innerHTML = row.dataset.originalHtml;
    }
    function editDailyAttributeRecord(btn) {
      let row = btn.parentElement.parentElement;
      makeRowEditable(row);
    }
    function editDawnRecord(btn) {
      let row = btn.parentElement.parentElement;
      makeRowEditable(row);
    }
    function editDailySummary(btn) {
      let row = btn.parentElement.parentElement;
      makeRowEditable(row);
    }
    function editDailyTotalRecord(btn) {
      let row = btn.parentElement.parentElement;
      makeRowEditable(row);
    }
    function deleteDailySummary(btn) {
      let row = btn.parentElement.parentElement;
      row.remove();
    }
    function submitDailySummary() {
      var summaryContent = document.getElementById("daily-input").value.trim();
      if(summaryContent === "") {
        alert("请填写每日个人总结");
        return;
      }
      var now = new Date().toLocaleDateString();
      var tableBody = document.querySelector("#daily-summary-table tbody");
      var newRow = document.createElement("tr");
      newRow.innerHTML = "<td>" + now + "</td>" +
                         "<td>" + summaryContent + "</td>" +
                         "<td><button class='op-btn edit-btn' onclick='editDailySummary(this)'>编辑</button> " +
                         "<button class='op-btn delete-btn' onclick='deleteDailySummary(this)'>删除</button></td>";
      tableBody.appendChild(newRow);
      document.getElementById("daily-input").value = "";
    }
    function toggleInfo(id) {
      var box = document.getElementById(id);
      box.style.display = (box.style.display === "block") ? "none" : "block";
    }
    function toggleHistory(id) {
      var historyBox = document.getElementById(id + "-history");
      if(historyBox.style.display === "block") {
         historyBox.style.display = "none";
      } else {
         historyBox.style.display = "block";
         var mapping = {
             "attributeRecords": "attribute-records-table",
             "dawnRecords": "dawn-records-table",
             "dailySummary": "daily-summary-table",
             "dailyRecords": "daily-records-table"
         };
         updateHistory(mapping[id], id + "-history");
      }
    }
    var historyData = {};
    function updateHistory(tableId, historyContainerId) {
      let today = new Date().toLocaleDateString();
      let tableBody = document.querySelector("#" + tableId + " tbody");
      let rows = Array.from(tableBody.querySelectorAll("tr"));
      let todaysRows = [];
      let historicalRows = [];
      rows.forEach(function(row) {
           let rowDate = row.cells[0].textContent.trim();
           if (rowDate === today) {
                todaysRows.push(row);
           } else {
                historicalRows.push(row);
           }
      });
      tableBody.innerHTML = "";
      todaysRows.forEach(function(row) {
           tableBody.appendChild(row);
      });
      historyData[historyContainerId] = {
           rows: historicalRows,
           currentPage: 1
      };
      if (document.getElementById(historyContainerId).style.display !== "none") {
           renderHistory(historyContainerId, tableId);
      }
    }
    function renderHistory(historyContainerId, tableId) {
       let container = document.getElementById(historyContainerId);
       container.innerHTML = "";
       let headerElem = document.querySelector("#" + tableId + " thead");
       let headerHTML = headerElem ? headerElem.outerHTML : "";
       let data = historyData[historyContainerId];
       if (!data) return;
       let rows = data.rows;
       let currentPage = data.currentPage;
       let totalPages = Math.ceil(rows.length / 10) || 1;
       let start = (currentPage - 1) * 10;
       let end = start + 10;
       let pageRows = rows.slice(start, end);
       let newTable = document.createElement("table");
       newTable.innerHTML = headerHTML;
       let newTbody = document.createElement("tbody");
       pageRows.forEach(function(row) {
          newTbody.appendChild(row.cloneNode(true));
       });
       newTable.appendChild(newTbody);
       container.appendChild(newTable);
       if (totalPages > 1) {
           let paginationDiv = document.createElement("div");
           paginationDiv.style.textAlign = "center";
           paginationDiv.style.marginTop = "10px";
           let prevBtn = document.createElement("button");
           prevBtn.className = "btn";
           prevBtn.textContent = "上一页";
           prevBtn.onclick = function() { changeHistoryPage(historyContainerId, -1, tableId); };
           let nextBtn = document.createElement("button");
           nextBtn.className = "btn";
           nextBtn.textContent = "下一页";
           nextBtn.onclick = function() { changeHistoryPage(historyContainerId, 1, tableId); };
           let pageInfo = document.createElement("span");
           pageInfo.textContent = " 第 " + currentPage + " 页 / 共 " + totalPages + " 页 ";
           paginationDiv.appendChild(prevBtn);
           paginationDiv.appendChild(pageInfo);
           paginationDiv.appendChild(nextBtn);
           container.appendChild(paginationDiv);
       }
    }
    function changeHistoryPage(historyContainerId, delta, tableId) {
        let data = historyData[historyContainerId];
        if (!data) return;
        let totalPages = Math.ceil(data.rows.length / 10) || 1;
        data.currentPage += delta;
        if (data.currentPage < 1) data.currentPage = 1;
        if (data.currentPage > totalPages) data.currentPage = totalPages;
        renderHistory(historyContainerId, tableId);
    }
    function submitDailyAttributeRecord() {
      let studyHours = parseFloat(document.getElementById("study-hours").value) || 0;
      let readingPages = parseFloat(document.getElementById("reading-pages").value) || 0;
      let paintingHours = parseFloat(document.getElementById("painting-hours").value) || 0;
      let incIntelligence = studyHours;
      let incKnowledge = Math.floor(readingPages / 10);
      let incStamina = 0; // 体力暂不记录训练数据
      let incWillpower = 0;
      let incPhantomExp = 0;
      if(paintingHours > 0) {
        incPhantomExp = paintingHours * 10;
        phantomExperience += incPhantomExp;
        phantomLazyCount = 0;
      } else {
        phantomLazyCount++;
        incPhantomExp = - ((phantomLazyCount >= 7) ? 100 : (phantomLazyCount >= 3 ? 80 : 50));
        phantomExperience = Math.max(phantomExperience + incPhantomExp, 0);
      }
      let feedbackMsg = "";
      if(studyHours === 0 && readingPages === 0 && paintingHours === 0) {
        attributeLazyCount++;
        attributeActiveCount = 0;
        let penaltyWillpower = (attributeLazyCount >= 7) ? 3 : (attributeLazyCount >= 3 ? 2 : 1);
        let penaltyKnowledge = (attributeLazyCount >= 7) ? 1 : (attributeLazyCount >= 3 ? 0.3 : 0.1);
        let penaltyIntelligence = (attributeLazyCount >= 7) ? 5 : (attributeLazyCount >= 3 ? 1.5 : 0.5);
        incWillpower -= penaltyWillpower;
        attributes.knowledge -= penaltyKnowledge;
        attributes.intelligence -= penaltyIntelligence;
        feedbackMsg += `懈怠惩罚: -${penaltyWillpower}意志, -${penaltyKnowledge}知识, -${penaltyIntelligence}智力. `;
      } else {
        attributeLazyCount = 0;
        attributeActiveCount++;
        let bonusWillpower = (attributeActiveCount >= 30) ? 3 : (attributeActiveCount >= 7 ? 2 : (attributeActiveCount >= 3 ? 1 : 0));
        incWillpower += bonusWillpower;
        feedbackMsg += `连续执行奖励: +${bonusWillpower}意志. `;
      }
      let totalOther = incIntelligence + incKnowledge + incStamina + incWillpower;
      let incCharisma = parseFloat((totalOther * 0.1).toFixed(1));
      attributes.intelligence += incIntelligence;
      attributes.knowledge += incKnowledge;
      attributes.stamina += incStamina;
      attributes.willpower += incWillpower;
      attributes.charisma += incCharisma;
      updateAttributesDisplay();
      let cumFeedback = "累计属性：";
      cumFeedback += `智力 ${attributes.intelligence} (还差 ${getNextRequirement(attributes.intelligence, thresholds.intelligence)}点), `;
      cumFeedback += `知识 ${attributes.knowledge} (还差 ${getNextRequirement(attributes.knowledge, thresholds.knowledge)}点), `;
      cumFeedback += `体力 ${attributes.stamina} (还差 ${getNextRequirement(attributes.stamina, thresholds.stamina)}点), `;
      cumFeedback += `意志 ${attributes.willpower} (还差 ${getNextRequirement(attributes.willpower, thresholds.willpower)}点), `;
      cumFeedback += `魅力 ${attributes.charisma.toFixed(1)} (还差 ${getNextRequirement(attributes.charisma, thresholds.charisma)}点), `;
      cumFeedback += `幻构师经验 ${phantomExperience} (还差 ${getNextRequirement(phantomExperience, thresholds.phantomExp)}经验)`;
      let tableBody = document.querySelector("#attribute-records-table tbody");
      let tr = document.createElement("tr");
      tr.setAttribute("data-intelligence", incIntelligence);
      tr.setAttribute("data-knowledge", incKnowledge);
      tr.setAttribute("data-stamina", incStamina);
      tr.setAttribute("data-willpower", incWillpower);
      tr.setAttribute("data-charisma", incCharisma);
      tr.setAttribute("data-phantomExp", incPhantomExp);
      let now = new Date().toLocaleDateString();
      tr.innerHTML = `<td>${now}</td>
                      <td>${studyHours}</td>
                      <td>${readingPages}</td>
                      <td>${paintingHours}</td>
                      <td>${incPhantomExp}</td>
                      <td>${incIntelligence}</td>
                      <td>${incKnowledge}</td>
                      <td>${incStamina}</td>
                      <td>${incWillpower}</td>
                      <td>${incCharisma}</td>
                      <td>${feedbackMsg}</td>
                      <td><button class='op-btn edit-btn' onclick='editDailyAttributeRecord(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDailyAttributeRecord(this)'>删除</button></td>`;
      tableBody.appendChild(tr);
      let recordCount = tableBody.rows.length;
      let feedback = document.getElementById("daily-attribute-feedback");
      feedback.textContent = `今日记录：智力 +${incIntelligence}, 知识 +${incKnowledge}, 幻构师经验 ${incPhantomExp>=0? "+"+incPhantomExp: incPhantomExp}, 意志 ${incWillpower>=0? "+"+incWillpower: incWillpower}, 魅力 +${incCharisma}. ${cumFeedback}. 你已累计记录 ${recordCount} 天。 ${feedbackMsg}`;
      document.getElementById("study-hours").value = "";
      document.getElementById("reading-pages").value = "";
      document.getElementById("painting-hours").value = "";
      updateStageCharts();
      updatePhantomRecordCharts();
    }
    function recalcAttributeRecords() {
      let tableBody = document.querySelector("#attribute-records-table tbody");
      let rows = tableBody.querySelectorAll("tr");
      let totalIntelligence = 0, totalKnowledge = 0, totalStamina = 0, totalWillpower = 0, totalCharisma = 0, totalPhantomExp = 0;
      rows.forEach(function(r) {
        totalIntelligence += parseFloat(r.getAttribute("data-intelligence")) || 0;
        totalKnowledge += parseFloat(r.getAttribute("data-knowledge")) || 0;
        totalStamina += parseFloat(r.getAttribute("data-stamina")) || 0;
        totalWillpower += parseFloat(r.getAttribute("data-willpower")) || 0;
        totalCharisma += parseFloat(r.getAttribute("data-charisma")) || 0;
        totalPhantomExp += parseFloat(r.getAttribute("data-phantomExp")) || 0;
      });
      attributes.intelligence = totalIntelligence;
      attributes.knowledge = totalKnowledge;
      attributes.stamina = totalStamina;
      attributes.willpower = totalWillpower;
      attributes.charisma = totalCharisma;
      phantomExperience = totalPhantomExp;
      updateAttributesDisplay();
    }
    function deleteDailyAttributeRecord(btn) {
      let row = btn.parentElement.parentElement;
      row.remove();
      recalcAttributeRecords();
      updateStageCharts();
      updatePhantomRecordCharts();
    }
    function deleteDawnRecord(btn) {
      let row = btn.parentElement.parentElement;
      let recWillpower = parseFloat(row.getAttribute("data-willpower")) || 0;
      let recStamina = parseFloat(row.getAttribute("data-stamina")) || 0;
      attributes.willpower -= recWillpower;
      attributes.stamina -= recStamina;
      updateAttributesDisplay();
      row.remove();
      recalcDawnCounters();
    }
    function recalcDawnCounters() {
      let tableBody = document.querySelector("#dawn-records-table tbody");
      let rows = tableBody.getElementsByTagName("tr");
      let total = rows.length;
      let success = 0;
      let consecutive = 0;
      let maxConsecutive = 0;
      for(let i = 0; i < rows.length; i++){
        let isSuccess = rows[i].getAttribute("data-isSuccess") === "true";
        if(isSuccess) {
          success++;
          consecutive++;
        } else {
          consecutive = 0;
        }
        if(consecutive > maxConsecutive) { maxConsecutive = consecutive; }
      }
      dawnTotalDays = total;
      dawnSuccessCount = success;
      dawnConsecutiveSuccess = maxConsecutive;
      let successRate = (total > 0) ? ((success / total) * 100).toFixed(1) : "0";
      let progressInfo = `打卡总天数: ${total} 天, 成功打卡: ${success} 天, 连续成功打卡: ${maxConsecutive} 天, 成功率: ${successRate}%`;
      document.getElementById("dawn-progress-info").textContent = progressInfo;
    }
    function submitDawnCheckin(event) {
      event.preventDefault();
      let sleepDate = document.getElementById("sleep-date").value;
      let wakeDate = document.getElementById("wake-date").value;
      let sleepTime = document.getElementById("sleep-time").value;
      let wakeTime = document.getElementById("wake-time").value;
      let specialCase = document.getElementById("special-case").checked;
      function timeToMinutes(t) {
        let parts = t.split(":");
        return parseInt(parts[0]) * 60 + parseInt(parts[1]);
      }
      let sleepMinutes = timeToMinutes(sleepTime);
      let wakeMinutes = timeToMinutes(wakeTime);
      let sleepDateObj = new Date(sleepDate);
      let wakeDateObj = new Date(wakeDate);
      let dayDiff = wakeDateObj.getTime() - sleepDateObj.getTime();
      dawnTotalDays++;
      let isSuccess = false;
      if (specialCase) {
        isSuccess = true;
      } else if (dayDiff === 86400000 && sleepMinutes < 1440 && wakeMinutes < 510) {
        isSuccess = true;
      }
      let rewardWillpower = 0;
      let rewardStamina = 0;
      if (isSuccess) {
        dawnSuccessCount++;
        dawnConsecutiveSuccess++;
        // 根据连续成功天数奖励：
        if(dawnConsecutiveSuccess >= 30) {
          rewardWillpower = 2;
          rewardStamina = 1;
        } else if(dawnConsecutiveSuccess >= 7) {
          rewardWillpower = 1;
          rewardStamina = 0.5;
        } else if(dawnConsecutiveSuccess >= 3) {
          rewardWillpower = 0.5;
          rewardStamina = 0.2;
        }
        // 如果睡前早于 23:30 且起床早于 8:00，额外增加 0.5 意志
        if (timeToMinutes(sleepTime) < (23 * 60 + 30) && wakeMinutes < (8 * 60)) {
          rewardWillpower += 0.5;
        }
      } else {
        dawnConsecutiveSuccess = 0;
      }
      if (!specialCase) {
        if (sleepMinutes >= 1440) { rewardWillpower -= 1; }
        if (wakeMinutes >= 510) { rewardStamina -= 0.5; }
      }
      attributes.willpower += rewardWillpower;
      attributes.stamina += rewardStamina;
      updateAttributesDisplay();
      let recordDate = wakeDate;
      let successRate = (dawnTotalDays > 0) ? ((dawnSuccessCount / dawnTotalDays) * 100).toFixed(1) : "0";
      let nextRewardTarget = "";
      if (dawnConsecutiveSuccess < 3) {
        nextRewardTarget = `还差 ${3 - dawnConsecutiveSuccess} 天达到连续 3 天奖励`;
      } else if (dawnConsecutiveSuccess < 7) {
        nextRewardTarget = `已达连续 3 天奖励，距离连续 7 天奖励还差 ${7 - dawnConsecutiveSuccess} 天`;
      } else if (dawnConsecutiveSuccess < 30) {
        nextRewardTarget = `已达连续 7 天奖励，距离连续 30 天奖励还差 ${30 - dawnConsecutiveSuccess} 天`;
      } else {
        nextRewardTarget = "已达到最高连续奖励";
      }
      let dawnPrompt = `【${recordDate}】 晨曦计划：累计打卡 ${dawnTotalDays} 天，成功 ${dawnSuccessCount} 天，连续成功 ${dawnConsecutiveSuccess} 天，成功率 ${successRate}%。${nextRewardTarget}。`;
      document.getElementById("dawn-progress-info").textContent = dawnPrompt;
      let statusText = isSuccess ? "成功" : "失败";
      let tableBody = document.querySelector("#dawn-records-table tbody");
      let tr = document.createElement("tr");
      tr.setAttribute("data-willpower", rewardWillpower);
      tr.setAttribute("data-stamina", rewardStamina);
      tr.setAttribute("data-isSuccess", isSuccess ? "true" : "false");
      tr.setAttribute("data-sleep-date", sleepDate);
      tr.setAttribute("data-wake-date", wakeDate);
      tr.innerHTML = `<td>${sleepDate}</td>
                      <td>${wakeDate}</td>
                      <td>${sleepTime}</td>
                      <td>${wakeTime}</td>
                      <td>${statusText}</td>
                      <td>${specialCase ? "是" : "否"}</td>
                      <td><button class='op-btn edit-btn' onclick='editDawnRecord(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDawnRecord(this)'>删除</button></td>`;
      tableBody.appendChild(tr);
      document.getElementById("sleep-date").value = "";
      document.getElementById("sleep-time").value = "";
      document.getElementById("wake-date").value = "";
      document.getElementById("wake-time").value = "";
      document.getElementById("special-case").checked = false;
      updateAttributesDisplay();
      return false;
    }
    function submitDailyRecord() {
      let tableBody = document.querySelector("#daily-records-table tbody");
      let tr = document.createElement("tr");
      let now = new Date().toLocaleDateString();
      let phantomProgress = document.getElementById("phantom-progress").textContent || "0%";
      let phantomStage = document.getElementById("phantom-stage").textContent || "未开启";
      let truthKnowledgeProgress = document.getElementById("truth-knowledge-progress").textContent || "0%";
      let truthKnowledgeStage = document.getElementById("truth-knowledge-stage").textContent || "未开启";
      let truthIntelligenceProgress = document.getElementById("truth-intelligence-progress").textContent || "0%";
      let truthIntelligenceStage = document.getElementById("truth-intelligence-stage").textContent || "未开启";
      let dawnTable = document.querySelector("#dawn-records-table tbody");
      let dawnStatus = (dawnTable.rows.length > 0) ? dawnTable.rows[dawnTable.rows.length - 1].cells[4].textContent : "未打卡";
      let summaryTable = document.querySelector("#daily-summary-table tbody");
      let dailySummary = (summaryTable.rows.length > 0) ? summaryTable.rows[summaryTable.rows.length - 1].cells[1].textContent : "无";
      tr.innerHTML = `<td>${now}</td>
                      <td>${phantomStage} (${phantomProgress})</td>
                      <td>${truthKnowledgeStage} (${truthKnowledgeProgress})</td>
                      <td>${truthIntelligenceStage} (${truthIntelligenceProgress})</td>
                      <td>${dawnStatus}</td>
                      <td>${dailySummary}</td>
                      <td><button class='op-btn edit-btn' onclick='editDailyTotalRecord(this)'>编辑</button> <button class='op-btn delete-btn' onclick='deleteDailyTotalRecord(this)'>删除</button></td>`;
      attributeHistory.push({
        date: now,
        intelligence: attributes.intelligence,
        knowledge: attributes.knowledge,
        stamina: attributes.stamina,
        willpower: attributes.willpower,
        charisma: attributes.charisma,
        phantomExp: phantomExperience
      });
      let index = attributeHistory.length - 1;
      tr.setAttribute("data-index", index);
      tableBody.appendChild(tr);
      updateStageCharts();
      updatePhantomRecordCharts();
    }
    function deleteDailyTotalRecord(btn) {
      let row = btn.parentElement.parentElement;
      let index = parseInt(row.getAttribute("data-index"));
      if(!isNaN(index)) {
        attributeHistory.splice(index, 1);
      }
      row.remove();
      let rows = document.querySelectorAll("#daily-records-table tbody tr");
      rows.forEach((r, i) => {
        r.setAttribute("data-index", i);
      });
      updateStageCharts();
      updatePhantomRecordCharts();
    }
    function updatePhantomProgress() {
      let currentExp = parseFloat(document.getElementById("phantom-current").value) || 0;
      let totalExp = 1500;
      let percent = Math.min((currentExp / totalExp) * 100, 100);
      document.getElementById("phantom-progress").style.width = percent + "%";
      document.getElementById("phantom-progress").textContent = Math.floor(percent) + "%";
      let stage = "";
      if (percent < 33) { stage = "初级"; }
      else if (percent < 66) { stage = "中级"; }
      else { stage = "高级"; }
      document.getElementById("phantom-stage").textContent = stage;
    }
    function updateTruthProgress() {
      let currentKnowledge = attributes.knowledge;
      let totalKnowledge = 150;
      let percentKnowledge = Math.min((currentKnowledge / totalKnowledge) * 100, 100);
      document.getElementById("truth-knowledge-progress").style.width = percentKnowledge + "%";
      document.getElementById("truth-knowledge-progress").textContent = Math.floor(percentKnowledge) + "%";
      document.getElementById("truth-knowledge-current").value = currentKnowledge;
      let currentIntelligence = attributes.intelligence;
      let totalIntelligence = 150;
      let percentIntelligence = Math.min((currentIntelligence / totalIntelligence) * 100, 100);
      document.getElementById("truth-intelligence-progress").style.width = percentIntelligence + "%";
      document.getElementById("truth-intelligence-progress").textContent = Math.floor(percentIntelligence) + "%";
      document.getElementById("truth-intelligence-current").value = currentIntelligence;
      let stageKnowledge = "";
      if (percentKnowledge < 33) { stageKnowledge = "初级"; }
      else if (percentKnowledge < 66) { stageKnowledge = "中级"; }
      else { stageKnowledge = "高级"; }
      document.getElementById("truth-knowledge-stage").textContent = stageKnowledge;
      let stageIntelligence = "";
      if (percentIntelligence < 33) { stageIntelligence = "初级"; }
      else if (percentIntelligence < 66) { stageIntelligence = "中级"; }
      else { stageIntelligence = "高级"; }
      document.getElementById("truth-intelligence-stage").textContent = stageIntelligence;
    }
    function initializeCharts() {
      const ctxMonthly = document.getElementById("monthly-chart").getContext("2d");
      monthlyChart = new Chart(ctxMonthly, {
        type: monthlyChartType,
        data: { labels: [], datasets: [] },
        options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
      const ctxYearly = document.getElementById("yearly-chart").getContext("2d");
      yearlyChart = new Chart(ctxYearly, {
        type: annualChartType,
        data: { labels: [], datasets: [] },
        options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
      const ctxPast = document.getElementById("past-chart").getContext("2d");
      pastChart = new Chart(ctxPast, {
        type: pastChartType,
        data: { labels: [], datasets: [] },
        options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
      const ctxPhantomMonthly = document.getElementById("phantom-monthly-chart").getContext("2d");
      phantomMonthlyChart = new Chart(ctxPhantomMonthly, {
         type: phantomMonthlyChartType,
         data: { labels: [], datasets: [] },
         options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
      const ctxPhantomYearly = document.getElementById("phantom-yearly-chart").getContext("2d");
      phantomYearlyChart = new Chart(ctxPhantomYearly, {
         type: phantomYearlyChartType,
         data: { labels: [], datasets: [] },
         options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
      const ctxPhantomPast = document.getElementById("phantom-past-chart").getContext("2d");
      phantomPastChart = new Chart(ctxPhantomPast, {
         type: phantomPastChartType,
         data: { labels: [], datasets: [] },
         options: { responsive: true, maintainAspectRatio: false, scales: { x: { display: true }, y: { beginAtZero: true } } }
      });
    }
    // 新增全局【保存】与【编辑】功能：将页面状态保存至 localStorage 或从中加载
    function savePageState() {
      let state = {
        attributes: attributes,
        attributeHistory: attributeHistory,
        attributeActiveCount: attributeActiveCount,
        attributeLazyCount: attributeLazyCount,
        attributeRecords: document.querySelector("#attribute-records-table tbody").innerHTML,
        dawnRecords: document.querySelector("#dawn-records-table tbody").innerHTML,
        dawnTotalDays: dawnTotalDays,
        dawnSuccessCount: dawnSuccessCount,
        dawnConsecutiveSuccess: dawnConsecutiveSuccess,
        phantomExperience: phantomExperience,
        phantomLazyCount: phantomLazyCount,
        // 保存称号相关状态
        titles: {
          currentWillTitle: document.getElementById("current-will-title").textContent,
          currentChaTitle: document.getElementById("current-cha-title").textContent,
          willBonus: document.getElementById("will-title-bonus").textContent,
          chaBonus: document.getElementById("cha-title-bonus").textContent
        }
      };
      localStorage.setItem("gamePlanState", JSON.stringify(state));
    }

    function loadPageState() {
      let dataStr = localStorage.getItem("gamePlanState");
      if(dataStr) {
        let data = JSON.parse(dataStr);
        attributes = data.attributes || { intelligence: 0, knowledge: 0, stamina: 0, willpower: 0, charisma: 0 };
        attributeHistory = data.attributeHistory || [];
        attributeActiveCount = data.attributeActiveCount || 0;
        attributeLazyCount = data.attributeLazyCount || 0;
        dawnTotalDays = data.dawnTotalDays || 0;
        dawnSuccessCount = data.dawnSuccessCount || 0;
        dawnConsecutiveSuccess = data.dawnConsecutiveSuccess || 0;
        phantomExperience = data.phantomExperience || 0;
        phantomLazyCount = data.phantomLazyCount || 0;

        // 恢复表格数据
        document.querySelector("#attribute-records-table tbody").innerHTML = data.attributeRecords || "";
        document.querySelector("#dawn-records-table tbody").innerHTML = data.dawnRecords || "";

        // 更新显示
        updateAttributesDisplay();
        updateTitleSystem();
        updateStageCharts();
        alert("状态已加载！");
      } else {
        alert("无保存状态！");
      }
    }
    function exportData() {
      let data = {
        attributes: attributes,
        attributeRecords: document.querySelector("#attribute-records-table tbody").innerHTML,
        dawnRecords: document.querySelector("#dawn-records-table tbody").innerHTML,
        dailySummaries: document.querySelector("#daily-summary-table tbody").innerHTML,
        dailyRecords: document.querySelector("#daily-records-table tbody").innerHTML,
        attributeHistory: attributeHistory,
        dawnTotalDays: dawnTotalDays,
        dawnSuccessCount: dawnSuccessCount,
        dawnConsecutiveSuccess: dawnConsecutiveSuccess,
        attributeActiveCount: attributeActiveCount,
        attributeLazyCount: attributeLazyCount,
        phantomExperience: phantomExperience
      };
      let dataStr = JSON.stringify(data);
      let blob = new Blob([dataStr], {type: "application/json"});
      let url = URL.createObjectURL(blob);
      let a = document.createElement("a");
      a.href = url;
      a.download = "gamePlanData.json";
      a.click();
      URL.revokeObjectURL(url);
    }
    function clearData() {
      if(confirm("确定要清空所有数据吗？")) {
        localStorage.removeItem("gamePlanState");
        location.reload();
      }
    }
    function importData() {
      let dataStr = document.getElementById("import-data").value;
      try {
        let data = JSON.parse(dataStr);
        attributeHistory = data.attributeHistory || [];
        updateStageCharts();
        updatePhantomRecordCharts();
        alert("数据导入成功");
      } catch (e) {
        alert("数据格式错误，请检查后重试");
      }
    }
    function generateQuickSummary() {
      let summary = `当前属性：智力 ${attributes.intelligence}，知识 ${attributes.knowledge}，体力 ${attributes.stamina}，意志 ${attributes.willpower}，魅力 ${attributes.charisma.toFixed(1)}。`;
      document.getElementById("quick-summary-output").value = summary;
    }
    function updateTimeInfo() {
      let now = new Date();
      document.getElementById("current-time").textContent = "当前时间: " + now.toLocaleString();
      let startDate = new Date("2025-03-01");
      let diffTime = now.getTime() - startDate.getTime();
      let diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
      document.getElementById("start-days").textContent = diffDays;
    }
    setInterval(updateTimeInfo, 1000);
    window.onload = function() {
      if(localStorage.getItem("gamePlanState")) {
        loadPageState();
      } else {
        updateAttributesDisplay();
        updatePhantomProgress();
        updateTruthProgress();
        initializeCharts();
      }
      if(attributeHistory.length === 0) {
        attributeHistory.push({
          date: "2025-03-01",
          intelligence: attributes.intelligence,
          knowledge: attributes.knowledge,
          stamina: attributes.stamina,
          willpower: attributes.willpower,
          charisma: attributes.charisma,
          phantomExp: phantomExperience
        });
      }
      document.getElementById("summary-date").textContent = "记录起始日期：" + attributeHistory[0].date;
      var mapping = {
           "attributeRecords": "attribute-records-table",
           "dawnRecords": "dawn-records-table",
           "dailySummary": "daily-summary-table",
           "dailyRecords": "daily-records-table"
      };
      for (var key in mapping) {
           updateHistory(mapping[key], key + "-history");
      }
      updateStageCharts();
      updatePhantomRecordCharts();
    }
    function submitAttributeRecord() {
      var attributeType = document.getElementById("attribute-type").value;
      var attributeValue = parseFloat(document.getElementById("attribute-value").value);
      var attributeTime = document.getElementById("attribute-time").value;
      var attributeDate = document.getElementById("attribute-date").value;
      var attributeNote = document.getElementById("attribute-note").value;

      if (isNaN(attributeValue) || attributeValue <= 0) {
        alert("请输入有效的属性值");
        return;
      }

      // 获取称号加成
      const { willBonus, chaBonus } = updateTitleSystem();
      
      // 应用称号加成
      if (attributeType === "charisma") {
        // 魅力受到意志称号加成
        attributeValue *= (1 + willBonus / 100);
      }
      // 所有属性都受到魅力称号加成
      attributeValue *= (1 + chaBonus / 100);

      // 更新属性值
      attributes[attributeType] += attributeValue;
      attributeActiveCount++;
      attributeLazyCount = 0;

      var tableBody = document.querySelector("#attribute-records-table tbody");
      var newRow = document.createElement("tr");
      newRow.innerHTML = "<td>" + attributeDate + "</td>" +
                        "<td>" + attributeTime + "</td>" +
                        "<td>" + attributeType + "</td>" +
                        "<td>" + attributeValue.toFixed(1) + "</td>" +
                        "<td>" + attributeNote + "</td>" +
                        "<td><button class='op-btn edit-btn' onclick='editAttributeRecord(this)'>编辑</button> " +
                        "<button class='op-btn delete-btn' onclick='deleteAttributeRecord(this)'>删除</button></td>";
      tableBody.appendChild(newRow);

      // 清空输入
      document.getElementById("attribute-value").value = "";
      document.getElementById("attribute-note").value = "";

      // 更新显示
      updateAttributesDisplay();
      savePageState();
    }
  </script>
</head>
<body>
  <div class="container">
    <!-- 头部信息 -->
    <header>
      <h1>人生游戏计划</h1>
      <div class="date-info">
        <span id="current-time">当前时间: --</span> |
        计划启动天数: <span id="start-days">0</span>
      </div>
      <div class="theme-selector">
        <label for="theme-selector">主题颜色：</label>
        <select id="theme-selector" onchange="changeTheme(this.value)">
          <option value="xiaohong-1" selected>宁静自然</option>
          <option value="xiaohong-2">海洋微风</option>
          <option value="xiaohong-3">暖沙暮色</option>
          <option value="xiaohong-4">科技极简</option>
          <option value="xiaohong-5">森系深调</option>
          <option value="xiaohong-6">柔灰渐变</option>
          <option value="xiaohong-7">活力橙蓝</option>
          <option value="xiaohong-8">莫兰迪紫</option>
          <option value="xiaohong-9">薄荷清新</option>
          <option value="xiaohong-10">暗夜模式</option>
        </select>
      </div>
      <div class="global-controls">
        <button class="btn" onclick="savePageState()">保存</button>
        <button class="btn" onclick="loadPageState()">编辑</button>
      </div>
    </header>
    <!-- 当前属性显示区域 -->
    <section id="attributes-display">
      <h2>当前属性</h2>
      <p>智力: <span id="intelligence">0</span></p>
      <p>知识: <span id="knowledge">0</span></p>
      <p>体力: <span id="stamina">0</span></p>
      <p>意志: <span id="willpower">0</span></p>
      <p>魅力: <span id="charisma">0.0</span></p>
      <p>幻构师经验: <span id="phantom-exp">1180</span></p>
      <p>意志称号: <span id="willpower-title">无</span></p>
      <p>魅力称号: <span id="charisma-title">无</span></p>
    </section>
    <!-- 每日记录板块 -->
    <div id="daily-inputs">
      <!-- 每日属性记录 -->
      <section id="daily-attribute-record">
        <h2>每日记录 <button class="info-btn" onclick="toggleInfo('daily-attribute-info')">?</button></h2>
        <div id="daily-attribute-info" class="info-box">
          <strong>基础属性提升方法：</strong>
          <ul>
            <li><strong>智力：</strong> 每学习 1 小时，增加 1 智力；</li>
            <li><strong>知识：</strong> 每阅读 10 页书籍，增加 1 知识；</li>
            <li><strong>体力：</strong> 每次训练或运动 1 小时，增加 1 体力（暂不记录，每坚持3/7/30天分别增加额外10%/20%/30%）；</li>
            <li><strong>意志：</strong> 连续执行奖励：连续 3 天以上，每天增加 1 意志；7 天以上，每天增加 2 意志；30 天以上，每天增加 3 意志；懈怠惩罚参照真理之路计划；</li>
            <li><strong>魅力：</strong> 其它属性每提升 1 点，增加 0.1 魅力；</li>
            <li><strong>幻构师经验：</strong> 每绘画 1 小时，增长 10 经验；未绘画则视为懈怠，依规则扣除经验。</li>
          </ul>
        </div>
        <form id="daily-attribute-form">
          <div class="form-group">
            <label for="study-hours">学习时长 (小时):</label>
            <input type="number" id="study-hours" min="0" step="0.1" required>
          </div>
          <div class="form-group">
            <label for="reading-pages">阅读页数:</label>
            <input type="number" id="reading-pages" min="0" required>
          </div>
          <div class="form-group">
            <label for="painting-hours">绘画时长 (小时):</label>
            <input type="number" id="painting-hours" min="0" step="0.1" required>
          </div>
          <button type="button" class="btn" onclick="submitDailyAttributeRecord()">提交每日记录</button>
        </form>
        <div id="daily-attribute-feedback" style="margin-top: 10px; font-weight: bold;"></div>
        <table id="attribute-records-table">
          <thead>
            <tr>
              <th>日期</th>
              <th>学习时长</th>
              <th>阅读页数</th>
              <th>绘画时长</th>
              <th>幻构师经验增量</th>
              <th>智力增量</th>
              <th>知识增量</th>
              <th>体力增量</th>
              <th>意志增量</th>
              <th>魅力增量</th>
              <th>备注</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody><!-- 当天记录 --></tbody>
        </table>
        <button class="btn" onclick="toggleHistory('attributeRecords')">显示历史记录</button>
        <div id="attributeRecords-history" class="history-container" style="display:none;"></div>
      </section>
      <!-- 晨曦之约计划 -->
      <section id="dawn-plan">
        <h2>晨曦之约计划 <button class="info-btn" onclick="toggleInfo('dawn-plan-info')">?</button></h2>
        <div id="dawn-status">
          <p>目标：前一日入睡时间在 24:00 前 &amp; 当日起床时间在 8:30 前</p>
          <p>当前打卡状态：<span id="dawn-status-text">未打卡</span></p>
        </div>
        <form id="dawn-form" onsubmit="return submitDawnCheckin(event)">
          <div class="form-group">
            <label for="sleep-date">入睡日期:</label>
            <input type="date" id="sleep-date" required>
          </div>
          <div class="form-group">
            <label for="sleep-time">睡觉时间 (HH:MM):</label>
            <input type="time" id="sleep-time" required>
          </div>
          <div class="form-group">
            <label for="wake-date">起床日期:</label>
            <input type="date" id="wake-date" required>
          </div>
          <div class="form-group">
            <label for="wake-time">起床时间 (HH:MM):</label>
            <input type="time" id="wake-time" required>
          </div>
          <div class="form-group">
            <label for="special-case">
              <input type="checkbox" id="special-case">
              特殊情况（即使未达标也算成功）
            </label>
          </div>
          <button type="submit" class="btn">提交晨曦打卡</button>
        </form>
        <table id="dawn-records-table">
          <thead>
            <tr>
              <th>入睡日期</th>
              <th>起床日期</th>
              <th>睡觉时间</th>
              <th>起床时间</th>
              <th>状态</th>
              <th>特殊情况</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody><!-- 当天记录 --></tbody>
        </table>
        <button class="btn" onclick="toggleHistory('dawnRecords')">显示历史记录</button>
        <div id="dawnRecords-history" class="history-container" style="display:none;"></div>
        <div id="dawn-plan-info" class="info-box">
          <strong>晨曦之约规则：</strong>
          <ul>
            <li>只有当【前一日入睡日期】与【当日起床日期】正好相差 1 天，且入睡时间早于 24:00 与起床时间早于 8:30 时，才计为打卡成功。（特殊情况勾选后视为成功）</li>
            <li>惩罚：未按要求则扣 1 意志（未 24:00 入睡）或 0.5 体力（未 8:30 起床）；</li>
            <li>奖励：连续坚持 3 天以上，每天获得 0.5 意志，增加 0.2 体力；连续坚持 7 天以上，每天获得 1 意志，增加 0.5 体力（同时提升意志提升效率 5%）；连续坚持 30 天以上，每天获得 2 意志，增加 1 体力（同时提升意志提升效率 10%）；如 23:30 前入睡且 8:00 前起床，则额外增加 0.5 意志。</li>
          </ul>
        </div>
        <div class="dawn-progress" id="dawn-progress-info"></div>
      </section>
      <!-- 每日个人总结 -->
      <section id="daily-summary">
        <h2>每日个人总结</h2>
        <form onsubmit="submitDailySummary(); return false;">
          <div class="form-group">
            <label for="daily-input">总结内容:</label>
            <textarea id="daily-input" required placeholder="请填写每日个人总结"></textarea>
          </div>
          <button type="submit" class="btn">提交每日总结</button>
        </form>
        <table id="daily-summary-table">
          <thead>
            <tr>
              <th>日期</th>
              <th>总结内容</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody><!-- 当天总结 --></tbody>
        </table>
        <button class="btn" onclick="toggleHistory('dailySummary')">显示历史记录</button>
        <div id="dailySummary-history" class="history-container" style="display:none;"></div>
      </section>
      <!-- 每日总记录 -->
      <section id="daily-records">
        <h2>每日总记录</h2>
        <button class="btn" onclick="submitDailyRecord()">提交今日记录</button>
        <table id="daily-records-table">
          <thead>
            <tr>
              <th>日期</th>
              <th>幻构师进度</th>
              <th>知识侧进度</th>
              <th>智力侧进度</th>
              <th>晨曦打卡</th>
              <th>每日总结</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody><!-- 当天记录 --></tbody>
        </table>
        <button class="btn" onclick="toggleHistory('dailyRecords')">显示历史记录</button>
        <div id="dailyRecords-history" class="history-container" style="display:none;"></div>
      </section>
    </div>
    <!-- 幻构师计划 -->
    <section id="phantom-plan">
      <h2>幻构师计划 <button class="info-btn" onclick="toggleInfo('phantom-plan-info')">?</button></h2>
      <table id="phantom-table">
        <thead>
          <tr>
            <th>等级</th>
            <th>总需求经验</th>
            <th>阶段划分</th>
            <th>阶段</th>
            <th>当前经验</th>
            <th>进度</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>描形学徒 Lv.3</td>
            <td>1500</td>
            <td>450 + 600 + 450</td>
            <td id="phantom-stage">初级</td>
            <td>
              <input type="number" id="phantom-current" value="1180" min="0" max="1500" onchange="updatePhantomProgress()">
            </td>
            <td>
              <div class="progress-container">
                <div class="progress-bar" id="phantom-progress">0%</div>
              </div>
            </td>
          </tr>
          <tr>
            <td>构素学者 Lv.4</td>
            <td>3000</td>
            <td>900 + 1200 + 900</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>灵绘使徒 Lv.5</td>
            <td>5000</td>
            <td>1500 + 2000 + 1500</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>影纹术士 Lv.6</td>
            <td>8000</td>
            <td>2400 + 3200 + 2400</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>心象织者 Lv.7</td>
            <td>12000</td>
            <td>3600 + 4800 + 3600</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>空境画匠 Lv.8</td>
            <td>18000</td>
            <td>5400 + 7200 + 5400</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>律令绘爵 Lv.9</td>
            <td>26000</td>
            <td>7800 + 10400 + 7800</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>幻构师 Lv.10</td>
            <td>36000</td>
            <td>10800 + 14400 + 10800</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
        </tbody>
      </table>
      <div id="phantom-plan-info" class="info-box">
        <strong>幻构师计划逆水行舟法则：</strong>
        <ul>
          <li>废退法则：单日懈怠 -50 经验；连续懈怠 ≥3 天 -80；连续懈怠 ≥7 天 -100。</li>
          <li>行舟法则：连续训练 ≥3 天：+10% 经验；≥7 天：+25%；≥30 天：+40% 经验。</li>
        </ul>
      </div>
    </section>
    <!-- 真理之路计划 -->
    <section id="truth-plan">
      <h2>真理之路计划 <button class="info-btn" onclick="toggleInfo('truth-plan-info')">?</button></h2>
      <h3>【知识侧】</h3>
      <table id="truth-knowledge-table">
        <thead>
          <tr>
            <th>阶段</th>
            <th>总需求</th>
            <th>阶段划分</th>
            <th>阶段</th>
            <th>当前属性</th>
            <th>进度</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>灰袍学士</td>
            <td>150</td>
            <td>30 + 45 + 75</td>
            <td id="truth-knowledge-stage">初级</td>
            <td>
              <input type="number" id="truth-knowledge-current" value="0" min="0" max="150">
            </td>
            <td>
              <div class="progress-container">
                <div class="progress-bar" id="truth-knowledge-progress">0%</div>
              </div>
            </td>
          </tr>
          <tr>
            <td>白袍导师</td>
            <td>500</td>
            <td>100 + 150 + 250</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>墨衣学者</td>
            <td>1500</td>
            <td>300 + 450 + 750</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>青衿院士</td>
            <td>4000</td>
            <td>800 + 1200 + 2000</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>玄冕宗师</td>
            <td>10000</td>
            <td>2000 + 3000 + 5000</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
        </tbody>
      </table>
      <h3>【智力侧】</h3>
      <table id="truth-intelligence-table">
        <thead>
          <tr>
            <th>阶段</th>
            <th>总需求</th>
            <th>阶段划分</th>
            <th>阶段</th>
            <th>当前属性</th>
            <th>进度</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>褐衣明理</td>
            <td>150</td>
            <td>30 + 45 + 75</td>
            <td id="truth-intelligence-stage">初级</td>
            <td>
              <input type="number" id="truth-intelligence-current" value="0" min="0" max="150">
            </td>
            <td>
              <div class="progress-container">
                <div class="progress-bar" id="truth-intelligence-progress">0%</div>
              </div>
            </td>
          </tr>
          <tr>
            <td>缁衣慎思</td>
            <td>500</td>
            <td>100 + 150 + 250</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>朱衣审辩</td>
            <td>1500</td>
            <td>300 + 450 + 750</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>紫绶格物</td>
            <td>4000</td>
            <td>800 + 1200 + 2000</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
          <tr>
            <td>金章弘道</td>
            <td>10000</td>
            <td>2000 + 3000 + 5000</td>
            <td>未开启</td>
            <td colspan="2">未开启</td>
          </tr>
        </tbody>
      </table>
    </section>
    <!-- 称号系统 -->
    <section id="title-rewards">
      <h2>称号奖励体系 <button class="info-btn" onclick="toggleInfo('title-rewards-info')">?</button></h2>
      <div id="title-display" class="title-status">
        <p>当前意志称号: <span class="title-name" id="current-will-title">无</span> (魅力增益: <span id="will-title-bonus">+0%</span>)</p>
        <p>当前魅力称号: <span class="title-name" id="current-cha-title">无</span> (全属性增益: <span id="cha-title-bonus">+0%</span>)</p>
      </div>
      <h3>【意志称号奖励体系】</h3>
      <table>
        <thead>
          <tr>
            <th>称号</th>
            <th>所需意志值</th>
            <th>奖励</th>
            <th>进度</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>晨曦微志</td>
            <td>10</td>
            <td>增加魅力增长效率 +5%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-10">0%</div></div></td>
          </tr>
          <tr>
            <td>坚石守心</td>
            <td>50</td>
            <td>增加魅力增长效率 +10%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-50">0%</div></div></td>
          </tr>
          <tr>
            <td>荆棘先锋</td>
            <td>100</td>
            <td>增加魅力增长效率 +15%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-100">0%</div></div></td>
          </tr>
          <tr>
            <td>钢铁铸意</td>
            <td>200</td>
            <td>增加魅力增长效率 +20%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-200">0%</div></div></td>
          </tr>
          <tr>
            <td>风暴不屈</td>
            <td>300</td>
            <td>增加魅力增长效率 +25%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-300">0%</div></div></td>
          </tr>
          <tr>
            <td>星辰恒志</td>
            <td>500</td>
            <td>增加魅力增长效率 +30%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-500">0%</div></div></td>
          </tr>
          <tr>
            <td>炽魂永燃</td>
            <td>800</td>
            <td>增加魅力增长效率 +40%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-800">0%</div></div></td>
          </tr>
          <tr>
            <td>不朽意志</td>
            <td>1000</td>
            <td>增加魅力增长效率 +50%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-will-1000">0%</div></div></td>
          </tr>
        </tbody>
      </table>
      <h3>【魅力称号体系】</h3>
      <table>
        <thead>
          <tr>
            <th>称号</th>
            <th>所需魅力值</th>
            <th>奖励</th>
            <th>进度</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>萤火微光</td>
            <td>10</td>
            <td>智力提升效率 +5%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-10">0%</div></div></td>
          </tr>
          <tr>
            <td>晨露流辉</td>
            <td>50</td>
            <td>智力与知识提升效率 +10%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-50">0%</div></div></td>
          </tr>
          <tr>
            <td>星芒初绽</td>
            <td>100</td>
            <td>全体属性提升效率 +20%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-100">0%</div></div></td>
          </tr>
          <tr>
            <td>银月颂光</td>
            <td>200</td>
            <td>全体属性提升效率 +25%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-200">0%</div></div></td>
          </tr>
          <tr>
            <td>日冕凝华</td>
            <td>300</td>
            <td>全体属性提升效率 +30%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-300">0%</div></div></td>
          </tr>
          <tr>
            <td>虹彩冠冕</td>
            <td>500</td>
            <td>全体属性提升效率 +35%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-500">0%</div></div></td>
          </tr>
          <tr>
            <td>天穹律光</td>
            <td>800</td>
            <td>全体属性提升效率 +40%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-800">0%</div></div></td>
          </tr>
          <tr>
            <td>万象圣辉</td>
            <td>1000</td>
            <td>全体属性提升效率 +50%</td>
            <td><div class="progress-container"><div class="progress-bar" id="progress-cha-1000">0%</div></div></td>
          </tr>
        </tbody>
      </table>
      <div id="title-rewards-info" class="info-box">
        <strong>称号系统规则：</strong>
        <ul>
          <li>意志称号：根据意志值获得，提供魅力增长效率加成</li>
          <li>魅力称号：根据魅力值获得，提供全体属性提升效率加成</li>
          <li>称号效果自动生效，无需手动激活</li>
          <li>称号等级会随属性变化自动调整</li>
        </ul>
      </div>
    </section>
    <!-- 幻构师经验记录 -->
    <section id="phantom-experience-summary">
      <h2 style="text-align:center;">幻构师经验记录</h2>
      <div class="charts-row">
        <div class="chart-container">
           <h3>每月幻构师经验记录 
             <select id="phantom-monthly-chart-type" onchange="switchPhantomMonthlyChartType(this.value)">
               <option value="line" selected>折线图</option>
               <option value="bar">条形图</option>
             </select>
           </h3>
           <canvas id="phantom-monthly-chart"></canvas>
        </div>
        <div class="chart-container">
           <h3>每年幻构师经验记录 
             <select id="phantom-annual-select" onchange="switchPhantomAnnualMode(this.value)">
               <option value="day" selected>天</option>
               <option value="month">月</option>
             </select>
             <select id="phantom-yearly-chart-type" onchange="switchPhantomYearlyChartType(this.value)">
               <option value="line" selected>折线图</option>
               <option value="bar">条形图</option>
             </select>
           </h3>
           <canvas id="phantom-yearly-chart"></canvas>
        </div>
        <div class="chart-container">
           <h3>过往幻构师经验记录 
             <select id="phantom-past-select" onchange="switchPhantomPastMode(this.value)">
               <option value="month" selected>月</option>
               <option value="year">年</option>
             </select>
             <select id="phantom-past-chart-type" onchange="switchPhantomPastChartType(this.value)">
               <option value="line" selected>折线图</option>
               <option value="bar">条形图</option>
             </select>
           </h3>
           <canvas id="phantom-past-chart"></canvas>
        </div>
      </div>
    </section>
    <!-- 阶段性总结（折线图显示各属性提升趋势） -->
    <section id="periodic-summary">
      <h2>阶段性总结</h2>
      <div class="charts-row">
        <div class="chart-container">
           <h3>过往属性提升趋势 
             <select id="past-select" onchange="switchPastMode(this.value)">
               <option value="month" selected>月</option>
               <option value="year">年</option>
             </select>
             <select id="past-chart-type" onchange="switchPastChartType(this.value)">
               <option value="line" selected>折线图</option>
               <option value="bar">条形图</option>
             </select>
           </h3>
           <canvas id="past-chart"></canvas>
        </div>
        <div class="chart-container">
           <h3>每月属性提升趋势 
             <select id="monthly-chart-type" onchange="switchMonthlyChartType(this.value)">
               <option value="line" selected>折线图</option>
               <option value="bar">条形图</option>
             </select>
           </h3>
           <canvas id="monthly-chart"></canvas>
        </div>
        <div class="chart-container">
           <h3>每年属性提升趋势 
             <select id="annual-select" onchange="switchAnnualMode(this.value)">
               <option value="day" selected>天</option>
               <option value="month">月</option>
             </select>
             <select id="annual-chart-type" onchange="switchAnnualChartType(this.value)">
               <option value="line" selected>折线图</option>
               <option value="bar">条形图</option>
             </select>
           </h3>
           <canvas id="yearly-chart"></canvas>
        </div>
      </div>
    </section>
  </div>
  <script>
    // 此处保留之前所有函数代码，并增加了上述针对意志与魅力称号、晨曦之约奖励规则等的修改，上述代码已完整呈现。
  </script>
</body>
</html>
