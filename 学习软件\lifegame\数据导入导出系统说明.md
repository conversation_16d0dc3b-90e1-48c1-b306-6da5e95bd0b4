# LifeGame 数据导入导出系统 v2.0 使用说明

## 🎯 系统概述

全新的数据导入导出系统提供了完整的版本兼容性支持，确保您的数据在不同版本间能够安全、可靠地迁移。

## ✨ 主要特性

### 1. **版本兼容性管理**
- 🔄 自动版本检测和兼容性验证
- 📈 智能数据结构升级
- 🔒 向后兼容性保证
- 📋 详细的版本迁移日志

### 2. **数据安全保障**
- 💾 导入前自动备份
- 🛡️ 数据完整性验证
- 🔄 失败自动回滚
- 📊 数据结构深度验证

### 3. **错误处理机制**
- ⚠️ 详细错误信息提示
- 🔧 智能错误恢复
- 📝 完整操作日志
- 🎯 精确问题定位

### 4. **测试验证工具**
- 🧪 内置功能测试
- ✅ 版本兼容性验证
- 📈 数据完整性检查
- 🔍 导入导出流程测试

## 🚀 使用指南

### 导出数据
1. 点击页面右上角的 **"导出"** 按钮
2. 系统会自动：
   - 添加当前版本标识 (v2.0.0)
   - 验证数据完整性
   - 生成数据完整性哈希
   - 创建带时间戳的文件名
3. 下载的文件格式：`lifeGameData_v2.0.0_YYYY-MM-DD-HH-mm-ss.json`

### 导入数据
1. 点击页面右上角的 **"导入"** 按钮
2. 选择要导入的 JSON 数据文件
3. 系统会自动：
   - 创建当前数据的备份
   - 检测文件版本并验证兼容性
   - 执行必要的数据结构升级
   - 验证数据完整性
   - 在失败时自动恢复原始数据

### 重置数据
1. 点击页面右上角的 **"重置"** 按钮
2. 选择重置类型：
   - **完全重置**：清空所有数据
   - **保留设置重置**：保留开始日期和初始属性
3. 系统会在重置前自动创建备份

### 测试功能
1. 点击页面右上角的 **"🧪 测试"** 按钮
2. 系统会自动执行：
   - 导出功能测试
   - 导入功能测试
   - 版本兼容性测试
   - 数据完整性验证
3. 测试结果会显示在弹窗和控制台中

## 📋 版本兼容性

### 支持的版本
- **v2.0.0** (当前版本)
- **v1.2.0** (完全兼容)
- **v1.1.0** (完全兼容)
- **v1.0.0** (完全兼容，需要数据升级)

### 版本升级说明
从旧版本导入时，系统会自动：
- 添加缺失的数据字段
- 设置新字段的默认值
- 保持原有数据不变
- 记录升级过程

## 🔧 数据结构说明

### v2.0.0 数据结构
```json
{
  "version": "2.0.0",
  "startDate": "YYYY-MM-DD",
  "initial": { /* 初始属性 */ },
  "dawn": { /* 晨曦之约数据 */ },
  "dailyRecords": [ /* 每日记录 */ ],
  "summaries": [ /* 阶段性总结 */ ],
  "overallRecords": [ /* 综合记录 */ ],
  "tasks": [ /* 任务列表 */ ],
  "dailyTasks": [ /* 每日任务 */ ],
  "dailyTaskRecords": [ /* 每日任务记录 */ ],
  "exportDate": "ISO时间戳",
  "metadata": { /* 元数据信息 */ }
}
```

## ⚠️ 注意事项

### 导入前准备
1. **备份当前数据**：虽然系统会自动备份，但建议手动导出一份
2. **检查文件格式**：确保导入的是有效的 JSON 文件
3. **版本确认**：确认文件版本与当前系统兼容

### 常见问题解决
1. **导入失败**：
   - 检查文件是否为有效的 LifeGame 数据文件
   - 确认文件没有损坏
   - 查看错误信息获取具体原因

2. **版本不兼容**：
   - 确认文件版本是否在支持范围内
   - 联系开发者获取版本升级支持

3. **数据丢失**：
   - 系统会自动创建备份，可联系开发者恢复
   - 检查浏览器本地存储是否有备份数据

## 🛠️ 技术细节

### 备份机制
- 最多保存 5 个自动备份
- 备份存储在浏览器本地存储中
- 每次导入和重置前自动创建备份

### 数据验证
- Schema 结构验证
- 字段类型检查
- 数据完整性哈希验证
- 关键数据丢失检测

### 错误恢复
- 导入失败自动回滚
- 备份数据快速恢复
- 详细错误日志记录

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 查看浏览器控制台的详细错误信息
2. 运行测试功能检查系统状态
3. 保存错误信息和相关数据文件
4. 联系开发者获取技术支持

---

**版本**: v2.0.0  
**更新日期**: 2025-07-02  
**兼容性**: 支持所有历史版本数据
