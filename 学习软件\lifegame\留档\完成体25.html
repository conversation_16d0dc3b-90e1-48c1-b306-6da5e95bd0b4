<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>人生游戏计划</title>
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- Font Awesome 图标库 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 使用 CSS 变量实现主题色切换 */
    :root {
      --color-bg: #E9F5DB;
      --color-secondary: #CFD7C7;
      --color-accent: #A3B18A;
      --color-primary: #588157;
      --color-dark: #3A5A40;
      --color-light-accent: rgba(163, 177, 138, 0.2);
      --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      --card-radius: 12px;
      --transition: all 0.3s ease;
    }
    
    body {
      background-color: var(--color-bg);
      color: var(--color-dark);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      padding-bottom: 2rem;
    }
    
    .navbar {
      background-color: var(--color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      padding: 0.8rem 1rem;
      width: 100%;
    }

    .container-fluid {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .navbar-nav {
      flex-wrap: wrap;
      gap: 0.8rem;
      align-items: center;
      justify-content: flex-start;
      padding: 0.5rem 0;
    }
    
    .navbar-brand, .nav-link, .navbar-text {
      color: var(--color-bg) !important;
      font-weight: 500;
    }
    
    .navbar-brand {
      font-size: 1.4rem;
      font-weight: 600;
      letter-spacing: 0.5px;
      margin-right: 2rem;
    }
    
    .nav-link {
      position: relative;
      margin-right: 0;
      padding: 10px 18px !important;
      border-radius: 8px;
      transition: var(--transition);
      font-size: 1.05rem;
    }
    
    .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-1px);
    }
    
    .nav-link.active {
      background-color: rgba(255, 255, 255, 0.3) !important;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .card {
      margin-bottom: 20px;
      border: none;
      border-radius: var(--card-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
      transition: var(--transition);
    }
    
    .card:hover {
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
    
    .card-body {
      padding: 1.5rem;
    }
    
    .card-title {
      color: var(--color-primary);
      font-weight: 600;
      margin-bottom: 1rem;
      font-size: 1.35rem;
      border-bottom: 2px solid var(--color-light-accent);
      padding-bottom: 0.5rem;
      display: inline-block;
    }
    
    .progress {
      height: 0.8rem;
      border-radius: 6px;
      overflow: hidden;
      background-color: var(--color-light-accent);
      margin: 0.5rem 0;
    }
    
    .progress-bar {
      background-color: var(--color-accent);
      font-size: 0.75rem;
      font-weight: 600;
      line-height: 0.8rem;
      transition: width 0.5s ease;
    }
    
    .table {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    }
    
    .table thead th {
      background-color: var(--color-secondary);
      color: var(--color-dark);
      border: none;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;
      padding: 12px;
    }
    
    .table tbody tr {
      transition: var(--transition);
    }
    
    .table tbody tr:hover {
      background-color: var(--color-light-accent);
    }
    
    .table td {
      padding: 12px;
      vertical-align: middle;
    }
    
    .btn {
      padding: 0.375rem 1rem;
      border-radius: 6px;
      font-weight: 500;
      transition: var(--transition);
      letter-spacing: 0.3px;
    }
    
    .btn-primary {
      background-color: var(--color-primary);
      border-color: var(--color-primary);
    }
    
    .btn-primary:hover {
      background-color: var(--color-dark);
      border-color: var(--color-dark);
    }
    
    .btn-sm {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border-radius: 4px;
    }
    
    .form-control, .form-select {
      border-radius: 8px;
      border: 1px solid var(--color-secondary);
      padding: 0.5rem 0.75rem;
      transition: var(--transition);
    }
    
    .form-control:focus, .form-select:focus {
      border-color: var(--color-accent);
      box-shadow: 0 0 0 3px var(--color-light-accent);
    }
    
    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: var(--color-primary);
    }
    
    /* 属性显示的样式 */
    #basicStatus, #careerStatus, #titlesStatus, #planStatus {
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 8px;
      padding: 10px 15px;
      margin-bottom: 8px;
      font-size: 0.95rem;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
    }
    
    #basicStatus span, #careerStatus span, #titlesStatus span {
      margin-right: 10px;
      display: inline-block;
      padding: 2px 8px;
      background-color: var(--color-secondary);
      border-radius: 6px;
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    /* 标签页内容的样式 */
    .tab-content {
      padding: 15px;
      background-color: #fff;
      border-radius: 0 0 var(--card-radius) var(--card-radius);
      box-shadow: var(--box-shadow);
    }
    
    h3 {
      color: var(--color-primary);
      margin-bottom: 1.25rem;
      font-weight: 600;
      border-left: 4px solid var(--color-accent);
      padding-left: 12px;
    }
    
    h5 {
      color: var(--color-dark);
      font-weight: 600;
      margin-top: 1.5rem;
      margin-bottom: 1rem;
    }
    
    /* 逆水行舟状态显示 */
    #niShuiStatus, #niShuiStatusCheckin {
      padding: 10px 15px;
      background-color: var(--color-light-accent);
      border-left: 4px solid var(--color-accent);
      border-radius: 4px;
      font-weight: 500;
    }
    
    /* 分页样式 */
    #dailyRecordPagination, #checkinPagination, #summaryPagination, #totalRecordPagination {
      margin-top: 15px;
    }
    
    /* 动画效果 */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .tab-pane {
      animation: fadeIn 0.4s ease-out;
    }
    
    /* 主题选择器 */
    #themeSelector {
      width: auto;
      min-width: 120px;
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .navbar-nav {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
      }
      
      .nav-item {
        margin-bottom: 5px;
      }
      
      .d-flex {
        flex-wrap: wrap;
        justify-content: center;
      }
      
      #basicStatus, #careerStatus, #titlesStatus, #planStatus {
        margin-bottom: 10px;
      }
    }
    
    /* 模态框样式优化 */
    .modal-content {
      border-radius: 12px;
      border: none;
    }
    
    .modal-header {
      background-color: var(--color-primary);
      color: white;
      border-radius: 12px 12px 0 0;
      border-bottom: none;
    }
    
    .modal-title {
      font-weight: 600;
    }
    
    .modal-body {
      padding: 1.5rem;
    }
    
    .modal-footer {
      border-top: none;
    }
    
    /* 图表容器样式 */
    canvas {
      margin: 15px 0;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 10px;
    }
    
    /* 当前状态卡片特殊样式 */
    .card:first-of-type {
      background: linear-gradient(to right bottom, var(--color-secondary), white);
      border-top: 3px solid var(--color-primary);
    }
    
    .theme-option {
      cursor: pointer;
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
      <a class="navbar-brand" href="#"><i class="fas fa-gamepad me-2"></i>人生游戏计划</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto mb-2 mb-lg-0" role="tablist">
          <li class="nav-item" role="presentation">
            <a class="nav-link active" id="home-tab" data-bs-toggle="tab" href="#home" role="tab"><i class="fas fa-home me-1"></i> 首页</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="dailyRecord-tab" data-bs-toggle="tab" href="#dailyRecord" role="tab"><i class="fas fa-calendar-day me-1"></i> 每日记录</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="checkin-tab" data-bs-toggle="tab" href="#checkin" role="tab"><i class="fas fa-sun me-1"></i> 晨曦之约</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="dailySummary-tab" data-bs-toggle="tab" href="#dailySummary" role="tab"><i class="fas fa-book me-1"></i> 个人总结</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="totalRecord-tab" data-bs-toggle="tab" href="#totalRecord" role="tab"><i class="fas fa-clipboard-list me-1"></i> 每日总记录</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="huanGoushi-tab" data-bs-toggle="tab" href="#huanGoushi" role="tab"><i class="fas fa-paint-brush me-1"></i> 幻构师计划</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="truthPath-tab" data-bs-toggle="tab" href="#truthPath" role="tab"><i class="fas fa-scroll me-1"></i> 真理之路计划</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="titles-tab" data-bs-toggle="tab" href="#titles" role="tab"><i class="fas fa-award me-1"></i> 称号系统</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="phaseSummary-tab" data-bs-toggle="tab" href="#phaseSummary" role="tab"><i class="fas fa-chart-line me-1"></i> 阶段性总结</a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="tasks-tab" data-bs-toggle="tab" href="#tasks" role="tab"><i class="fas fa-tasks me-1"></i> 任务系统</a>
          </li>
        </ul>
        <div class="d-flex">
          <button class="btn btn-light me-2" id="saveButton"><i class="fas fa-save me-1"></i> 保存</button>
          <button class="btn btn-success me-2" id="exportButton"><i class="fas fa-file-export me-1"></i> 导出</button>
          <button class="btn btn-info me-2" id="importButton"><i class="fas fa-file-import me-1"></i> 导入</button>
          <button class="btn btn-danger me-2" id="resetButton"><i class="fas fa-undo me-1"></i> 重置</button>
          <select id="themeSelector" class="form-select">
            <option value="宁静自然">宁静自然</option>
            <option value="海洋微风">海洋微风</option>
            <option value="暖沙暮色">暖沙暮色</option>
            <option value="科技极简">科技极简</option>
            <option value="森系深调">森系深调</option>
            <option value="柔灰渐变">柔灰渐变</option>
            <option value="活力橙蓝">活力橙蓝</option>
            <option value="莫兰迪紫">莫兰迪紫</option>
            <option value="薄荷清新">薄荷清新</option>
            <option value="暗夜模式">暗夜模式</option>
          </select>
          <button class="btn btn-info ms-2" id="statusInfoButton"><i class="fas fa-question-circle"></i></button>
        </div>
      </div>
    </div>
  </nav>
  
  <!-- 当前时间及计划状态 -->
  <div class="container mt-4">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title"><i class="fas fa-info-circle me-2"></i>当前状态</h5>
        <p id="currentTime" class="mb-1"><i class="fas fa-clock me-2"></i>加载中...</p>
        <p id="planDays" class="mb-3"><i class="fas fa-calendar-alt me-2"></i>加载中...</p>
        <div id="basicStatus" class="mb-3">
          <strong><i class="fas fa-user-chart me-1"></i>属性:</strong>
          <span id="attrIntelligence"><i class="fas fa-brain me-1"></i>智力: 0</span>
          <span id="attrKnowledge"><i class="fas fa-book-open me-1"></i>知识: 0</span>
          <span id="attrStamina"><i class="fas fa-heartbeat me-1"></i>体力: 0</span>
          <span id="attrWillpower"><i class="fas fa-fist-raised me-1"></i>意志: 0</span>
          <span id="attrCharisma"><i class="fas fa-star me-1"></i>魅力: 0</span>
          <span id="attrExperience"><i class="fas fa-palette me-1"></i>幻构师经验: 1180</span>
        </div>
        <div id="careerStatus" class="mb-3">
          <strong><i class="fas fa-briefcase me-1"></i>职业水平:</strong>
          <span id="huanGoushiLevel"><i class="fas fa-paint-brush me-1"></i>幻构师: Lv.3 描形学徒 (初级)</span>
          <span id="truthPathKnowledgeLevel"><i class="fas fa-book me-1"></i>真理之路知识侧: LV.1 灰袍学徒 (初级)</span>
          <span id="truthPathIntelligenceLevel"><i class="fas fa-brain me-1"></i>真理之路智力侧: LV.1 褐衣明理 (初级)</span>
        </div>
        <div id="titlesStatus" class="mb-3">
          <strong><i class="fas fa-medal me-1"></i>称号:</strong>
          <span id="checkinTitle"><i class="fas fa-sun me-1"></i>晨曦之约: 无</span>
          <span id="willTitle"><i class="fas fa-fire me-1"></i>意志称号: 无</span>
          <span id="charmTitle"><i class="fas fa-magic me-1"></i>魅力称号: 无</span>
        </div>
        <div id="planStatus">
          <strong><i class="fas fa-chart-pie me-1"></i>状态:</strong> <span id="planInfo">暂无额外增益</span>
        </div>
        <div id="accumulatedStatus" class="mt-3">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title"><i class="fas fa-history me-2"></i>累计数据</h5>
              <div class="mb-4">
                <h6 class="mb-3">计划成功执行天数：</h6>
                <div class="d-flex flex-wrap gap-4">
                  <div class="plan-progress">
                    <span id="huanGoushiDays" class="d-block mb-2"><i class="fas fa-paint-brush me-1"></i>幻构师计划: 0天</span>
                    <div class="progress" style="width: 200px; height: 0.8rem;">
                      <div id="huanGoushiProgress" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                  </div>
                  <div class="plan-progress">
                    <span id="truthPathDays" class="d-block mb-2"><i class="fas fa-scroll me-1"></i>真理之路计划: 0天</span>
                    <div class="progress" style="width: 200px; height: 0.8rem;">
                      <div id="truthPathProgress" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                  </div>
                  <div class="plan-progress">
                    <span id="checkinDays" class="d-block mb-2"><i class="fas fa-sun me-1"></i>晨曦之约计划: 0天</span>
                    <div class="progress" style="width: 200px; height: 0.8rem;">
                      <div id="checkinProgress" class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h6 class="mb-3">属性累计增长：</h6>
                <div class="d-flex flex-wrap gap-3" style="background-color: var(--color-light-accent); padding: 15px; border-radius: 8px;">
                  <span id="totalIntelligence" class="badge bg-light text-dark p-2"><i class="fas fa-brain me-1"></i>智力: +0</span>
                  <span id="totalKnowledge" class="badge bg-light text-dark p-2"><i class="fas fa-book-open me-1"></i>知识: +0</span>
                  <span id="totalStamina" class="badge bg-light text-dark p-2"><i class="fas fa-heartbeat me-1"></i>体力: +0</span>
                  <span id="totalWillpower" class="badge bg-light text-dark p-2"><i class="fas fa-fist-raised me-1"></i>意志: +0</span>
                  <span id="totalCharisma" class="badge bg-light text-dark p-2"><i class="fas fa-star me-1"></i>魅力: +0</span>
                  <span id="totalExperience" class="badge bg-light text-dark p-2"><i class="fas fa-palette me-1"></i>幻构师经验: +0</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 各标签页内容 -->
  <div class="container mt-4">
    <div class="tab-content">
      <!-- 首页 -->
      <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
        <h3>首页</h3>
        <p>欢迎使用人生游戏计划程序！本程序将自动记录每日属性提升、计划打卡、任务进度及各阶段总结，助你科学规划人生。</p>
      </div>
      
      <!-- 每日记录功能 -->
      <div class="tab-pane fade" id="dailyRecord" role="tabpanel" aria-labelledby="dailyRecord-tab">
        <h3>每日记录</h3>
        <div class="card">
          <div class="card-body">
            <form id="dailyRecordForm">
              <div class="row mb-3">
                <div class="col">
                  <label for="studyHours" class="form-label">学习时长（小时）</label>
                  <input type="number" class="form-control" id="studyHours" step="0.1" min="0">
                </div>
                <div class="col">
                  <label for="readingPages" class="form-label">阅读页数</label>
                  <input type="number" class="form-control" id="readingPages" step="1" min="0">
                </div>
                <div class="col">
                  <label for="paintingHours" class="form-label">绘画时长（小时）</label>
                  <input type="number" class="form-control" id="paintingHours" step="0.1" min="0">
                </div>
              </div>
              <button type="button" class="btn btn-primary" id="addDailyRecord">添加记录</button>
            </form>
          </div>
        </div>
        <!-- 在每日记录区域新增逆水行舟状态显示 -->
        <div id="niShuiStatus" class="mt-2"></div>
        <div class="mt-3">
          <h5>今日记录</h5>
          <table class="table table-bordered" id="dailyRecordTable">
            <thead>
              <tr>
                <th>日期</th>
                <th>学习时长</th>
                <th>阅读页数</th>
                <th>绘画时长</th>
                <th>幻构师经验增量</th>
                <th>智力增量</th>
                <th>知识增量</th>
                <th>体力增量</th>
                <th>意志增量</th>
                <th>魅力增量</th>
                <th>备注</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 动态生成记录 -->
            </tbody>
          </table>
          <div id="dailyRecordPagination" class="d-flex justify-content-center"></div>
          <button class="btn btn-secondary mt-2" id="showDailyHistory">切换历史记录</button>
        </div>
      </div>
      
      <!-- 晨曦之约计划 -->
      <div class="tab-pane fade" id="checkin" role="tabpanel" aria-labelledby="checkin-tab">
        <h3>晨曦之约计划</h3>
        <div class="card">
          <div class="card-body">
            <form id="checkinForm">
              <div class="row mb-3">
                <div class="col">
                  <label for="sleepDate" class="form-label">入睡日期</label>
                  <input type="date" class="form-control" id="sleepDate">
                </div>
                <div class="col">
                  <label for="wakeDate" class="form-label">起床日期</label>
                  <input type="date" class="form-control" id="wakeDate">
                </div>
              </div>
              <div class="row mb-3">
                <div class="col">
                  <label for="sleepTime" class="form-label">睡觉时间</label>
                  <input type="time" class="form-control" id="sleepTime">
                </div>
                <div class="col">
                  <label for="wakeTime" class="form-label">起床时间</label>
                  <input type="time" class="form-control" id="wakeTime">
                </div>
              </div>
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="specialCheckin">
                <label class="form-check-label" for="specialCheckin">特殊情况（勾选后算作打卡成功）</label>
              </div>
              <button type="button" class="btn btn-primary" id="addCheckin">添加打卡记录</button>
            </form>
          </div>
        </div>
        <!-- 在晨曦之约区域新增逆水行舟状态显示 -->
        <div id="niShuiStatusCheckin" class="mt-2"></div>
        <div class="mt-3">
          <h5>今日打卡记录</h5>
          <table class="table table-bordered" id="checkinTable">
            <thead>
              <tr>
                <th>入睡日期</th>
                <th>起床日期</th>
                <th>睡觉时间</th>
                <th>起床时间</th>
                <th>状态</th>
                <th>特殊情况</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 动态生成打卡记录 -->
            </tbody>
          </table>
          <div id="checkinPagination" class="d-flex justify-content-center"></div>
          <button class="btn btn-secondary mt-2" id="showCheckinHistory">切换历史记录</button>
        </div>
      </div>
      
      <!-- 每日个人总结 -->
      <div class="tab-pane fade" id="dailySummary" role="tabpanel" aria-labelledby="dailySummary-tab">
        <h3>每日个人总结</h3>
        <div class="card">
          <div class="card-body">
            <form id="summaryForm">
              <div class="mb-3">
                <label for="summaryDate" class="form-label">日期</label>
                <input type="date" class="form-control" id="summaryDate">
              </div>
              <div class="mb-3">
                <label for="summaryContent" class="form-label">总结内容</label>
                <textarea class="form-control" id="summaryContent" rows="3"></textarea>
              </div>
              <button type="button" class="btn btn-primary" id="addSummary">添加总结</button>
            </form>
          </div>
        </div>
        <div class="mt-3">
          <h5>今日总结</h5>
          <table class="table table-bordered" id="summaryTable">
            <thead>
              <tr>
                <th>日期</th>
                <th>总结内容</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 动态生成总结记录 -->
            </tbody>
          </table>
          <div id="summaryPagination" class="d-flex justify-content-center"></div>
          <button class="btn btn-secondary mt-2" id="showSummaryHistory">切换历史记录</button>
        </div>
      </div>
      
      <!-- 每日总记录 -->
      <div class="tab-pane fade" id="totalRecord" role="tabpanel" aria-labelledby="totalRecord-tab">
        <h3>每日总记录</h3>
        <table class="table table-bordered table-hover" id="totalRecordTable">
          <thead class="table-light">
            <tr>
              <th class="text-center" style="width: 10%">日期</th>
              <th class="text-center" style="width: 20%">幻构师进度</th>
              <th class="text-center" style="width: 20%">知识侧进度</th>
              <th class="text-center" style="width: 20%">智力侧进度</th>
              <th class="text-center" style="width: 10%">晨曦打卡</th>
              <th class="text-center" style="width: 15%">每日总结</th>
              <th class="text-center" style="width: 5%">操作</th>
            </tr>
          </thead>
          <tbody>
            <!-- 动态生成总记录 -->
          </tbody>
        </table>
        <div id="totalRecordPagination" class="d-flex justify-content-center"></div>
        <button class="btn btn-secondary mt-2" id="showTotalHistory">切换历史记录</button>
      </div>
      
      <!-- 幻构师计划 -->
      <div class="tab-pane fade" id="huanGoushi" role="tabpanel" aria-labelledby="huanGoushi-tab">
        <h3>幻构师计划</h3>
        <table class="table table-bordered" id="huanGoushiTable">
          <thead>
            <tr>
              <th>等级</th>
              <th>本级需求经验</th>
              <th>称号</th>
              <th>当前经验</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <!-- 根据全局 state.huanGoushi.levels 生成数据 -->
          </tbody>
        </table>
      </div>
      
      <!-- 真理之路计划 -->
      <div class="tab-pane fade" id="truthPath" role="tabpanel" aria-labelledby="truthPath-tab">
        <h3>真理之路计划</h3>
        <h5>知识侧</h5>
        <table class="table table-bordered" id="truthKnowledgeTable">
          <thead>
            <tr>
              <th>等级</th>
              <th>本级需求值</th>
              <th>称号</th>
              <th>当前值</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <!-- 根据 state.truthPath.knowledge 生成数据 -->
          </tbody>
        </table>
        <h5>智力侧</h5>
        <table class="table table-bordered" id="truthIntelligenceTable">
          <thead>
            <tr>
              <th>等级</th>
              <th>本级需求值</th>
              <th>称号</th>
              <th>当前值</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <!-- 根据 state.truthPath.intelligence 生成数据 -->
          </tbody>
        </table>
      </div>
      
      <!-- 称号系统 -->
      <div class="tab-pane fade" id="titles" role="tabpanel" aria-labelledby="titles-tab">
        <h3>称号系统</h3>
        <h5>晨曦之约称号</h5>
        <table class="table table-bordered" id="checkinTitleTable">
          <thead>
            <tr>
              <th>等级</th>
              <th>称号</th>
              <th>所需坚持天数</th>
              <th>当前连续天数</th>
              <th>奖励</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <!-- 根据 state.titles.checkin 生成数据 -->
          </tbody>
        </table>
        <h5>意志称号</h5>
        <table class="table table-bordered" id="willTitleTable">
          <thead>
            <tr>
              <th>等级</th>
              <th>称号</th>
              <th>所需意志值</th>
              <th>当前意志值</th>
              <th>奖励</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <!-- 根据 state.titles.will 生成数据 -->
          </tbody>
        </table>
        <h5>魅力称号</h5>
        <table class="table table-bordered" id="charmTitleTable">
          <thead>
            <tr>
              <th>等级</th>
              <th>称号</th>
              <th>所需魅力值</th>
              <th>当前魅力值</th>
              <th>奖励</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <!-- 根据 state.titles.charm 生成数据 -->
          </tbody>
        </table>
      </div>
      
      <!-- 阶段性总结功能 -->
      <div class="tab-pane fade" id="phaseSummary" role="tabpanel" aria-labelledby="phaseSummary-tab">
        <h3>阶段性总结</h3>
        <!-- 基础属性记录和幻构师经验记录 -->
        <div class="charts-row mb-4">
          <div class="row g-4">
            <div class="col-md-6">
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <h5 class="card-title text-center mb-4">基础属性记录</h5>
                  <canvas id="basicAttrChart"></canvas>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card h-100 shadow-sm">
                <div class="card-body">
                  <h5 class="card-title text-center mb-4">幻构师经验记录</h5>
                  <canvas id="experienceChart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 趋势按钮组 -->
        <div class="text-center mb-4">
          <div class="btn-group" role="group" aria-label="属性提升趋势按钮组">
            <button class="btn btn-secondary" id="toggleMonthly">每月属性提升趋势</button>
            <button class="btn btn-secondary" id="toggleYearly">每年属性提升趋势</button>
            <button class="btn btn-secondary" id="togglePast">过往属性提升趋势</button>
          </div>
        </div>
        <!-- 平均增长图表 -->
        <div class="row">
          <!-- 基础属性平均增长 -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-body">
                <h5 class="card-title">基础属性平均增长</h5>
                <div class="mb-3">
                  <select id="basic-chart-type" class="form-select mb-2" onchange="updateBasicChart()">
                    <option value="line">折线图</option>
                    <option value="bar">条形图</option>
                  </select>
                  <select id="basic-time-unit" class="form-select" onchange="updateBasicChart()">
                    <option value="week">每周</option>
                    <option value="month">每月</option>
                    <option value="year">每年</option>
                  </select>
                </div>
                <div class="chart-container" style="position: relative; height: 300px;">
                  <canvas id="basic-chart"></canvas>
                </div>
              </div>
            </div>
          </div>
          <!-- 幻构师经验平均增长 -->
          <div class="col-md-6">
            <div class="card h-100">
              <div class="card-body">
                <h5 class="card-title">幻构师经验平均增长</h5>
                <div class="mb-3">
                  <select id="exp-chart-type" class="form-select mb-2" onchange="updateExpChart()">
                    <option value="line">折线图</option>
                    <option value="bar">条形图</option>
                  </select>
                  <select id="exp-time-unit" class="form-select" onchange="updateExpChart()">
                    <option value="week">每周</option>
                    <option value="month">每月</option>
                    <option value="year">每年</option>
                  </select>
                </div>
                <div class="chart-container" style="position: relative; height: 300px;">
                  <canvas id="exp-chart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 任务系统 -->
      <div class="tab-pane fade" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
        <h3>任务系统</h3>
        <div class="card">
          <div class="card-body">
            <form id="taskForm">
              <div class="mb-3">
                <label for="taskName" class="form-label">任务名称</label>
                <input type="text" class="form-control" id="taskName">
              </div>
              <div class="mb-3">
                <label for="taskDesc" class="form-label">任务描述</label>
                <textarea class="form-control" id="taskDesc" rows="3"></textarea>
              </div>
              <div class="mb-3">
                <label for="taskType" class="form-label">任务类型（所属计划）</label>
                <select class="form-select" id="taskType">
                  <option value="幻构师计划">幻构师计划</option>
                  <option value="真理之路计划">真理之路计划</option>
                  <option value="晨曦之约计划">晨曦之约计划</option>
                  <option value="其他">其他</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="taskCycle" class="form-label">任务周期</label>
                <select class="form-select" id="taskCycle">
                  <option value="短期">短期</option>
                  <option value="长期">长期</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="taskTargetType" class="form-label">目标数据类型</label>
                <select class="form-select" id="taskTargetType">
                  <option value="学习时长">学习时长（小时）</option>
                  <option value="阅读页数">阅读页数（页）</option>
                  <option value="绘画时长">绘画时长（小时）</option>
                  <option value="运动时长">运动时长（小时）</option>
                  <option value="其他">其他</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="taskTarget" class="form-label">任务目标数据</label>
                <input type="number" class="form-control" id="taskTarget" min="1" placeholder="输入目标数值">
                <small class="form-text text-muted">请输入与所选目标数据类型对应的具体数值（如：学习2小时、阅读50页等）</small>
              </div>
              <div class="mb-3">
                <label for="taskDeadline" class="form-label">任务截止日期</label>
                <input type="date" class="form-control" id="taskDeadline">
                <small class="form-text text-muted">设置任务的完成期限，在期限内完成可获得奖励，超期未完成将受到惩罚</small>
              </div>
              <div class="mb-3" id="taskPenalties">
                <label class="form-label">任务惩罚</label>
                <div id="penaltiesList">
                  <div class="penalty-item mb-2">
                    <div class="row">
                      <div class="col-md-5">
                        <select class="form-select penalty-type">
                          <option value="智力">智力</option>
                          <option value="知识">知识</option>
                          <option value="体力">体力</option>
                          <option value="意志">意志</option>
                          <option value="魅力">魅力</option>
                          <option value="幻构师经验">幻构师经验</option>
                        </select>
                      </div>
                      <div class="col-md-5">
                        <input type="number" class="form-control penalty-value" min="0" placeholder="输入惩罚数值">
                      </div>
                      <div class="col-md-2">
                        <button type="button" class="btn btn-danger btn-sm remove-penalty">删除</button>
                      </div>
                    </div>
                  </div>
                </div>
                <button type="button" class="btn btn-secondary btn-sm mt-2" id="addPenalty">添加惩罚</button>
              </div>
              <div class="mb-3" id="taskRewards">
                <label class="form-label">任务奖励</label>
                <div id="rewardsList">
                  <div class="reward-item mb-2">
                    <div class="row">
                      <div class="col-md-5">
                        <select class="form-select reward-type">
                          <option value="智力">智力</option>
                          <option value="知识">知识</option>
                          <option value="体力">体力</option>
                          <option value="意志">意志</option>
                          <option value="魅力">魅力</option>
                          <option value="幻构师经验">幻构师经验</option>
                        </select>
                      </div>
                      <div class="col-md-5">
                        <input type="number" class="form-control reward-value" min="0" placeholder="输入奖励数值">
                      </div>
                      <div class="col-md-2">
                        <button type="button" class="btn btn-danger btn-sm remove-reward">删除</button>
                      </div>
                    </div>
                  </div>
                </div>
                <button type="button" class="btn btn-secondary btn-sm mt-2" id="addReward">添加奖励</button>
              </div>
              <button type="button" class="btn btn-primary" id="addTask">添加任务</button>
            </form>
          </div>
        </div>
        <div class="mt-3">
          <h5>未完成任务</h5>
          <table class="table table-bordered" id="incompleteTasksTable">
            <thead>
              <tr>
                <th>任务名称</th>
                <th>描述</th>
                <th>类型</th>
                <th>周期</th>
                <th>进度</th>
                <th>剩余天数</th>
                <th>奖励</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 动态生成未完成任务 -->
            </tbody>
          </table>
        </div>
        <div class="mt-3">
          <h5>已完成任务</h5>
          <table class="table table-bordered" id="completedTasksTable">
            <thead>
              <tr>
                <th>任务名称</th>
                <th>描述</th>
                <th>类型</th>
                <th>周期</th>
                <th>进度</th>
                <th>剩余天数</th>
                <th>奖励</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 动态生成已完成任务 -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 状态说明模态框 -->
  <div class="modal fade" id="statusInfoModal" tabindex="-1" aria-labelledby="statusInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="statusInfoModalLabel">基础属性提升规则及称号获取规则</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
        </div>
        <div class="modal-body">
          <h6>基础属性提升方法</h6>
          <ul>
            <li><strong>智力：</strong>每学习1小时，增加1点智力。</li>
            <li><strong>知识：</strong>每阅读10页书籍，增加1点知识。（每页书籍增加0.1知识）</li>
            <li><strong>体力：</strong>每1小时训练或运动，增加1点体力。（连续3天以上增加10%，7天以上20%，30天以上30%）</li>
            <li><strong>意志：</strong>任何行动连续执行3天以上，每天增加1点；连续7天以上，每天增加2点；连续30天以上，每天增加3点。（各行动单独计算，可叠加）</li>
            <li><strong>魅力：</strong>每提升1点其它属性，增加0.1点魅力。</li>
            <li><strong>幻构师经验：</strong>初始1180，每绘画1小时增加10经验。</li>
          </ul>
          <h6>【逆水行舟·意志】</h6>
          <p>
            懈怠惩罚：<br>
            每懈怠1天，减少1意志；连续懈怠≥3天，减少2意志/天；连续懈怠≥7天，减少3意志/天。
          </p>
          <h6>【逆水行舟·幻构师】</h6>
          <p>
            废退法则：<br>
            单日懈怠：每日经验-50；连续懈怠≥3天：每日经验-80；连续懈怠≥7天：每日经验-100。<br>
            行舟法则：<br>
            连续执行≥3天：经验增长效率+10%；连续执行≥7天：经验增长效率+25%；连续执行≥30天：经验增长效率+40%。
          </p>
          <h6>【逆水行舟·真理之路】</h6>
          <p>
            废退法则：<br>
            单日懈怠：知识-0.1，智力-0.5；连续懈怠≥3天：知识-0.3/天，智力-1.5/天；连续懈怠≥7天：知识-1/天，智力-5/天。<br>
            行舟法则：<br>
            连续执行≥3天：知识和智力获取效率+10%；连续执行≥7天：知识和智力获取效率+25%；连续执行≥30天：知识和智力获取效率+40%。
          </p>
          <h6>【逆水行舟·晨曦之约】</h6>
          <p>
            废退：<br>
            每次未能按时睡觉（前一天24:00前），减少1意志。<br>
            每次未能按时起床（当天8:30前），减少0.5体力。<br>
            额外奖励：如果23:30前睡觉，8:00前起床，则额外增加0.5意志。（ui额外注释）<br>
            行舟：<br>
            持续坚持≥3天：每天+0.5意志、+0.2体力；≥7天：每天+1意志、+0.5体力；≥30天：每天+2意志、+1体力。<br>
            若23:30前睡且8:00前起，则额外+0.5意志。（UI附注）
          </p>
          <h6>其他功能</h6>
          <ul>
            <li>阶段性总结：分为基础属性与幻构师经验记录，以图表展示，每月、每年及过往趋势切换。</li>
            <li>任务系统：自动计算任务进度，完成时结算奖励属性。</li>
          </ul>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Bootstrap JS Bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- 导入导出功能的JavaScript代码 -->
  <script>
  // 导出数据
  document.getElementById('exportButton').addEventListener('click', function() {
    const data = {
      dailyRecords: JSON.parse(localStorage.getItem('dailyRecords') || '[]'),
      checkinRecords: JSON.parse(localStorage.getItem('checkinRecords') || '[]'),
      summaryRecords: JSON.parse(localStorage.getItem('summaryRecords') || '[]'),
      totalRecords: JSON.parse(localStorage.getItem('totalRecords') || '[]'),
      attributes: JSON.parse(localStorage.getItem('attributes') || '{}'),
      career: JSON.parse(localStorage.getItem('career') || '{}'),
      titles: JSON.parse(localStorage.getItem('titles') || '{}'),
      planStatus: JSON.parse(localStorage.getItem('planStatus') || '{}'),
      accumulatedData: JSON.parse(localStorage.getItem('accumulatedData') || '{}'),
      tasks: JSON.parse(localStorage.getItem('tasks') || '[]')
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '人生游戏计划数据_' + new Date().toISOString().split('T')[0] + '.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  });

  // 导入数据
  document.getElementById('importButton').addEventListener('click', function() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(e) {
      const file = e.target.files[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const data = JSON.parse(e.target.result);
          
          // 验证数据结构
          const requiredKeys = ['dailyRecords', 'checkinRecords', 'summaryRecords', 'totalRecords', 
                              'attributes', 'career', 'titles', 'planStatus', 'accumulatedData', 'tasks'];
          const missingKeys = requiredKeys.filter(key => !(key in data));
          
          if (missingKeys.length > 0) {
            alert('导入失败：数据格式不正确，缺少必要的数据字段');
            return;
          }
          
          // 确认导入
          if (confirm('确定要导入数据吗？这将覆盖当前所有数据！')) {
            // 保存所有数据到localStorage
            Object.entries(data).forEach(([key, value]) => {
              localStorage.setItem(key, JSON.stringify(value));
            });
            
            alert('数据导入成功！页面将刷新以加载新数据。');
            location.reload();
          }
        } catch (error) {
          alert('导入失败：文件格式错误或数据损坏');
          console.error('Import error:', error);
        }
      };
      reader.readAsText(file);
    };
    
    input.click();
  });
  </script>

  <script>
    /**************** 全局状态与历史分页变量 ****************/
    const state = {
      startDate: new Date('2025-03-01'),
      currentAttributes: {
        intelligence: 0,
        knowledge: 0,
        stamina: 0,
        willpower: 0,
        charisma: 0,
        experience: 1180
      },
      dailyRecords: [],
      checkinRecords: [],
      summaries: [],
      tasks: [],
      history: [], // 每日属性快照
      huanGoushi: {
        levels: [
          { level: "Lv.3", title: "描形学徒", total: 1500, stages: [
              {name: "初级", min: 1, max: 450}, 
              {name: "中级", min: 451, max: 1050}, 
              {name: "高级", min: 1051, max: 1500}
            ], current: 0 },
          { level: "Lv.4", title: "构素学者", total: 3000, stages: [
              {name: "初级", min: 1501, max: 2400}, 
              {name: "中级", min: 2401, max: 3600}, 
              {name: "高级", min: 3601, max: 4500}
            ], current: 0 },
          { level: "Lv.5", title: "灵绘使徒", total: 5000, stages: [
              {name: "初级", min: 4501, max: 6000}, 
              {name: "中级", min: 6001, max: 8000}, 
              {name: "高级", min: 8001, max: 9500}
            ], current: 0 },
          { level: "Lv.6", title: "影纹术士", total: 8000, stages: [
              {name: "初级", min: 9501, max: 11900}, 
              {name: "中级", min: 11901, max: 15100}, 
              {name: "高级", min: 15101, max: 17500}
            ], current: 0 },
          { level: "Lv.7", title: "心象织者", total: 12000, stages: [
              {name: "初级", min: 17501, max: 21100}, 
              {name: "中级", min: 21101, max: 25900}, 
              {name: "高级", min: 25901, max: 29500}
            ], current: 0 },
          { level: "Lv.8", title: "空境画匠", total: 18000, stages: [
              {name: "初级", min: 29501, max: 34900}, 
              {name: "中级", min: 34901, max: 42100}, 
              {name: "高级", min: 42101, max: 47500}
            ], current: 0 },
          { level: "Lv.9", title: "律令绘爵", total: 26000, stages: [
              {name: "初级", min: 47501, max: 55300}, 
              {name: "中级", min: 55301, max: 65700}, 
              {name: "高级", min: 65701, max: 73500}
            ], current: 0 },
          { level: "Lv.10", title: "幻构师", total: 36000, stages: [
              {name: "初级", min: 73501, max: 84300}, 
              {name: "中级", min: 84301, max: 98700}, 
              {name: "高级", min: 98701, max: 109500}
            ], current: 0 }
        ]
      },
      truthPath: {
        knowledge: [
          { level: "LV.1", title: "灰袍学徒", total: 150, stages: [
              {name: "初级", min: 1, max: 30}, 
              {name: "中级", min: 31, max: 75}, 
              {name: "高级", min: 76, max: 150}
            ], current: 0 },
          { level: "LV.2", title: "白袍向导", total: 500, stages: [
              {name: "初级", min: 151, max: 250}, 
              {name: "中级", min: 251, max: 400}, 
              {name: "高级", min: 401, max: 650}
            ], current: 0 },
          { level: "LV.3", title: "墨衣学者", total: 1500, stages: [
              {name: "初级", min: 651, max: 950}, 
              {name: "中级", min: 951, max: 1400}, 
              {name: "高级", min: 1401, max: 2150}
            ], current: 0 },
          { level: "LV.4", title: "青衿贤者", total: 4000, stages: [
              {name: "初级", min: 2151, max: 2950}, 
              {name: "中级", min: 2951, max: 4150}, 
              {name: "高级", min: 4151, max: 6150}
            ], current: 0 },
          { level: "LV.5", title: "玄冕宗师", total: 10000, stages: [
              {name: "初级", min: 6151, max: 8150}, 
              {name: "中级", min: 8151, max: 11150}, 
              {name: "高级", min: 11151, max: 16150}
            ], current: 0 }
        ],
        intelligence: [
          { level: "LV.1", title: "褐衣明理", total: 150, stages: [
              {name: "初级", min: 1, max: 30}, 
              {name: "中级", min: 31, max: 75}, 
              {name: "高级", min: 76, max: 150}
            ], current: 0 },
          { level: "LV.2", title: "缁衣慎思", total: 500, stages: [
              {name: "初级", min: 151, max: 250}, 
              {name: "中级", min: 251, max: 400}, 
              {name: "高级", min: 401, max: 650}
            ], current: 0 },
          { level: "LV.3", title: "朱衣审辩", total: 1500, stages: [
              {name: "初级", min: 651, max: 950}, 
              {name: "中级", min: 951, max: 1400}, 
              {name: "高级", min: 1401, max: 2150}
            ], current: 0 },
          { level: "LV.4", title: "紫绶格物", total: 4000, stages: [
              {name: "初级", min: 2151, max: 2950}, 
              {name: "中级", min: 2951, max: 4150}, 
              {name: "高级", min: 4151, max: 6150}
            ], current: 0 },
          { level: "LV.5", title: "金章弘道", total: 10000, stages: [
              {name: "初级", min: 6151, max: 8150}, 
              {name: "中级", min: 8151, max: 11150}, 
              {name: "高级", min: 11151, max: 16150}
            ], current: 0 }
        ]
      },
      titles: {
        checkin: [
          { level: "Lv.1", title: "星辉学徒", required: 7, current: 0, reward: "智力提升效率+5%" },
          { level: "Lv.2", title: "晨风哨卫", required: 30, current: 0, reward: "知识+智力提升效率+5%" },
          { level: "Lv.3", title: "夜穹守誓", required: 60, current: 0, reward: "全体属性+5%" },
          { level: "Lv.4", title: "破晓骑士", required: 90, current: 0, reward: "全体属性+10%" },
          { level: "Lv.5", title: "黎明星使", required: 120, current: 0, reward: "全体属性+15%" },
          { level: "Lv.6", title: "永夜圣者", required: 180, current: 0, reward: "全体属性+20%" },
          { level: "Lv.7", title: "晨曦领主", required: 365, current: 0, reward: "全体属性+25%" },
          { level: "Lv.8", title: "时序主宰", required: 730, current: 0, reward: "全体属性+30%" }
        ],
        will: [
          { level: "LV.1", title: "晨曦微志", required: 50, current: 0, reward: "魅力增长效率+5%" },
          { level: "LV.2", title: "坚石守心", required: 200, current: 0, reward: "魅力增长效率+10%" },
          { level: "LV.3", title: "荆棘先锋", required: 500, current: 0, reward: "魅力增长效率+15%" },
          { level: "LV.4", title: "钢铁铸意", required: 800, current: 0, reward: "魅力增长效率+20%" },
          { level: "LV.5", title: "风暴不屈", required: 1200, current: 0, reward: "魅力增长效率+25%" },
          { level: "LV.6", title: "星辰恒志", required: 2000, current: 0, reward: "魅力增长效率+30%" },
          { level: "LV.7", title: "炽魂永燃", required: 3000, current: 0, reward: "魅力增长效率+40%" },
          { level: "LV.8", title: "无朽之心", required: 5000, current: 0, reward: "魅力增长效率+50%" }
        ],
        charm: [
          { level: "LV.1", title: "萤火微光", required: 10, current: 0, reward: "全体属性+5%" },
          { level: "LV.2", title: "晨露流辉", required: 50, current: 0, reward: "全体属性+10%" },
          { level: "LV.3", title: "星芒初绽", required: 100, current: 0, reward: "全体属性+15%" },
          { level: "LV.4", title: "银月颂光", required: 200, current: 0, reward: "全体属性+20%" },
          { level: "LV.5", title: "日冕凝华", required: 300, current: 0, reward: "全体属性+25%" },
          { level: "LV.6", title: "虹彩冠冕", required: 500, current: 0, reward: "全体属性+30%" },
          { level: "LV.7", title: "天穹律光", required: 800, current: 0, reward: "全体属性+40%" },
          { level: "LV.8", title: "万象圣辉", required: 1200, current: 0, reward: "全体属性+50%" }
        ]
      }
    };

    // 历史记录模式及分页变量
    let dailyHistoryMode = false, dailyRecordPage = 1;
    let checkinHistoryMode = false, checkinPage = 1;
    let summaryHistoryMode = false, summaryPage = 1;
    let totalHistoryMode = false, totalPage = 1;

    // 初始化时调用表单更新函数
    document.addEventListener('DOMContentLoaded', function() {
      updateHuanGoushiTable();
      updateTruthPathTable();
      updateTitlesTable();
    });

    /**************** 逆水行舟状态显示 ****************/
    function updateNiShuiStatusDisplay() {
      let statusStr = "";
      
      // 意志计划状态
      let willStatusStr = "逆水行舟·意志：";
      
      // 计算每日记录的意志状态
      let consecutiveDays = calculateConsecutiveDailyRecord('all');
      let dailySlackDays = calculateConsecutiveSlackDailyRecord();
      
      if (consecutiveDays > 0) {
        // 行舟法则：连续执行增加意志
        let bonus = consecutiveDays >= 30 ? 3 : (consecutiveDays >= 7 ? 2 : (consecutiveDays >= 3 ? 1 : 0));
        willStatusStr += `每日记录连续执行${consecutiveDays}天，每日意志+${bonus}`;
      } else if (dailySlackDays > 0) {
        // 废退法则：懈怠减少意志
        let penalty = dailySlackDays >= 7 ? 3 : (dailySlackDays >= 3 ? 2 : 1);
        willStatusStr += `每日记录懈怠${dailySlackDays}天，每日意志-${penalty}`;
      } else {
        willStatusStr += "今日暂无记录";
      }
      
      // 幻构师计划状态
      let huangoushiDays = calculateConsecutiveDailyRecord('painting');
      let huangoushiStr = "幻构师计划：";
      if(huangoushiDays > 0) {
        let bonusRate = huangoushiDays >= 30 ? 40 : (huangoushiDays >= 7 ? 25 : (huangoushiDays >= 3 ? 10 : 0));
        huangoushiStr += `连续执行${huangoushiDays}天，经验增长效率+${bonusRate}%`;
      } else {
        let huangoushiSlackDays = calculateConsecutiveSlackDailyRecord('painting');
        let penalty = huangoushiSlackDays >= 7 ? 100 : (huangoushiSlackDays >= 3 ? 80 : 50);
        huangoushiStr += `懈怠${huangoushiSlackDays}天，每日经验-${penalty}`;
      }
      
      // 真理之路计划状态
      let truthPathDays = calculateConsecutiveDailyRecord('study');
      let truthPathStr = "真理之路计划：";
      if(truthPathDays > 0) {
        let bonusRate = truthPathDays >= 30 ? 40 : (truthPathDays >= 7 ? 25 : (truthPathDays >= 3 ? 10 : 0));
        truthPathStr += `连续执行${truthPathDays}天，知识和智力获取效率+${bonusRate}%`;
      } else {
        let truthPathSlackDays = calculateConsecutiveSlackDailyRecord('study');
        let knowledgePenalty = truthPathSlackDays >= 7 ? 1 : (truthPathSlackDays >= 3 ? 0.3 : 0.1);
        let intelligencePenalty = truthPathSlackDays >= 7 ? 5 : (truthPathSlackDays >= 3 ? 1.5 : 0.5);
        truthPathStr += `懈怠${truthPathSlackDays}天，每日知识-${knowledgePenalty}、智力-${intelligencePenalty}`;
      }
      
      // 晨曦之约计划状态
      let checkinDays = calculateConsecutiveCheckin();
      let checkinStr = "晨曦之约计划：";
      if(checkinDays > 0) {
        let bonusWill = checkinDays >= 30 ? 2 : (checkinDays >= 7 ? 1 : 0.5);
        let bonusStamina = checkinDays >= 30 ? 1 : (checkinDays >= 7 ? 0.5 : 0.2);
        checkinStr += `连续打卡${checkinDays}天，每日意志+${bonusWill}、体力+${bonusStamina}`;
      } else {
        checkinStr += "今日未打卡";
      }
      
      // 合并所有状态
      statusStr = `${willStatusStr}<br>${huangoushiStr}<br>${truthPathStr}<br>${checkinStr}`;
      
      // 更新显示
      document.getElementById('niShuiStatus').innerHTML = statusStr;
    }
    
    function updateNiShuiStatusCheckin() {
      let c = calculateConsecutiveCheckin();
      let statusStr = "";
      
      // 晨曦之约计划状态
      let checkinStr = "逆水行舟·晨曦之约：";
      let todayCheckin = getTodayCheckin();
      
      // 检查是否有打卡记录
      if (state.checkinRecords.length > 0) {
        let latestRecord = state.checkinRecords[state.checkinRecords.length - 1];
        
        // 检查废退法则（未按时睡觉和起床的惩罚）
        let penalties = [];
        if (latestRecord.sleepTime < "18:00" || latestRecord.sleepTime > "24:00") {
          penalties.push("未按时睡觉，意志-1");
        }
        if (latestRecord.wakeTime > "08:30") {
          penalties.push("未按时起床，体力-0.5");
        }
        
        if (penalties.length > 0) {
          let failDays = 1;
          for (let i = state.checkinRecords.length - 2; i >= 0; i--) {
            let record = state.checkinRecords[i];
            if (record.status === "失败") {
              failDays++;
            } else {
              break;
            }
          }
          checkinStr += `连续打卡失败${failDays}天，${penalties.join("，")}`;          
        } else {
          // 行舟法则（连续打卡奖励）
          if (c >= 30) {
            checkinStr += `连续打卡成功${c}天，每日意志+2、体力+1`;
          } else if (c >= 7) {
            checkinStr += `连续打卡成功${c}天，每日意志+1、体力+0.5`;
          } else if (c >= 3) {
            checkinStr += `连续打卡成功${c}天，每日意志+0.5、体力+0.2`;
          } else {
            checkinStr += `连续打卡成功${c}天`;
          }
          
          // 特殊奖励（23:30前睡且8:00前起）
          if (latestRecord.sleepTime <= "23:30" && latestRecord.wakeTime <= "08:00") {
            checkinStr += "<br>特殊奖励：23:30前睡且8:00前起，额外+0.5意志";
          }
        }
      } else {
        checkinStr += "暂无打卡记录";
      }

      // 意志计划状态
      let willStr = "<br>逆水行舟·意志：";
      
      if (state.checkinRecords.length > 0) {
        let latestRecord = state.checkinRecords[state.checkinRecords.length - 1];
        // 检查是否有废退（未按时睡觉或起床）
        if (latestRecord.sleepTime < "18:00" || latestRecord.sleepTime > "24:00" || latestRecord.wakeTime > "08:30") {
          let failDays = 1;
          for (let i = state.checkinRecords.length - 2; i >= 0; i--) {
            let record = state.checkinRecords[i];
            if (record.status === "失败") {
              failDays++;
            } else {
              break;
            }
          }
          if (failDays >= 7) {
            willStr += `晨曦之约计划连续懈怠${failDays}天，每日意志-3`;
          } else if (failDays >= 3) {
            willStr += `晨曦之约计划连续懈怠${failDays}天，每日意志-2`;
          } else {
            willStr += `晨曦之约计划懈怠${failDays}天，每日意志-1`;
          }
        } else {
          // 行舟法则（连续执行奖励）
          if (c >= 30) {
            willStr += `晨曦之约计划连续执行成功${c}天，每日意志+3`;
          } else if (c >= 7) {
            willStr += `晨曦之约计划连续执行成功${c}天，每日意志+2`;
          } else if (c >= 3) {
            willStr += `晨曦之约计划连续执行成功${c}天，每日意志+1`;
          } else {
            willStr += `晨曦之约计划执行成功${c}天`;
          }
        }
      } else {
        willStr += "暂无打卡记录";
      }
      
      statusStr = checkinStr + willStr;
      document.getElementById('niShuiStatusCheckin').innerHTML = statusStr;
    }
    
    // 获取今日打卡记录
    function getTodayCheckin() {
      const today = new Date().toISOString().slice(0,10);
      return state.checkinRecords.find(r => r.wakeDate === today);
    }
    
    // 检查是否是早睡早起
    function isEarlyCheckin(checkin) {
      if(!checkin) return false;
      const sleepTime = checkin.sleepTime;
      const wakeTime = checkin.wakeTime;
      return sleepTime <= "23:30" && wakeTime <= "08:00";
    }

    /**************** 逆水行舟系统统一运算函数 ****************/
    function applyNiShuiXingChuan() {
      // 每次操作后重新计算历史、幻构师、真理之路、称号与状态
      recordHistory();
      updateHuanGoushiLevels();
      updateTruthPathLevels();
      updateTitlesTable();
      updateStatusDisplay();
      updateStageCharts();
      updateNiShuiStatusDisplay();
      updateNiShuiStatusCheckin();
    }

    /**************** 更新当前时间与计划启动天数 ****************/
    function updateTime() {
      const now = new Date();
      document.getElementById('currentTime').innerText = "当前时间：" + now.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
      const diffTime = now - state.startDate;
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
      document.getElementById('planDays').innerText = "计划启动天数：" + diffDays;
    }

    /**************** 主题切换 ****************/
    const themes = {
      "宁静自然": ["#E9F5DB", "#B9D7EA", "#A3B18A", "#588157", "#3A5A40"],
      "海洋微风": ["#F0F4F8", "#B9D7EA", "#769FCD", "#537DA6", "#2C3E50"],
      "暖沙暮色": ["#FFF5E4", "#FFD1D1", "#FF9494", "#E14D2A", "#6B2737"],
      "科技极简": ["#F8F9FA", "#E9ECEF", "#CED4DA", "#6C757D", "#212529"],
      "森系深调": ["#2D3E40", "#387373", "#93BFB7", "#D9B70D", "#F2F2F2"],
      "柔灰渐变": ["#F8F9FD", "#E2E7FF", "#BAC1FF", "#9AA5FF", "#6B7AA1"],
      "活力橙蓝": ["#FFEEE5", "#FFBFA3", "#FF6B35", "#004E89", "#001C38"],
      "莫兰迪紫": ["#F5E6E8", "#E2C4D4", "#B88C9E", "#705861", "#3A2E39"],
      "薄荷清新": ["#E3F2EF", "#B2DFDB", "#80CBC4", "#4DB6AC", "#00695C"],
      "暗夜模式": ["#0F0F0F", "#232D3F", "#2C4C6B", "#4A6670", "#D9D9D9"]
    };

    function changeTheme(themeName) {
      const colors = themes[themeName];
      // 如果从海洋微风切换到宁静自然，先重置所有相关样式
      if (themeName === "宁静自然") {
        document.documentElement.style.setProperty('--color-bg', '#E9F5DB');
        document.documentElement.style.setProperty('--color-secondary', '#CFD7C7');
        document.documentElement.style.setProperty('--color-accent', '#A3B18A');
        document.documentElement.style.setProperty('--color-primary', '#588157');
        document.documentElement.style.setProperty('--color-dark', '#3A5A40');
        document.body.style.backgroundColor = '#E9F5DB';
        return;
      }
      // 其他主题正常切换
      document.documentElement.style.setProperty('--color-bg', colors[0]);
      document.documentElement.style.setProperty('--color-secondary', colors[1]);
      document.documentElement.style.setProperty('--color-accent', colors[2]);
      document.documentElement.style.setProperty('--color-primary', colors[3]);
      document.documentElement.style.setProperty('--color-dark', colors[4]);
      document.body.style.backgroundColor = colors[0];
    }

    document.getElementById('themeSelector').addEventListener('change', function() {
      changeTheme(this.value);
    });

    /**************** 图表初始化与更新 ****************/
    let basicAttrChart, experienceChart;
    let chartMode = "monthly"; // 默认每月趋势
    let basicChart = null;
    let expChart = null;

    document.getElementById('toggleMonthly').addEventListener('click', () => { chartMode = "monthly"; updateStageCharts(); });
    document.getElementById('toggleYearly').addEventListener('click', () => { chartMode = "yearly"; updateStageCharts(); });
    document.getElementById('togglePast').addEventListener('click', () => { chartMode = "past"; updateStageCharts(); });

    // 计算平均属性增长
    function calculateAverageGrowth(data, timeUnit) {
      // 根据时间单位对数据进行分组
      const groupedData = {};
      data.forEach(record => {
        const date = new Date(record.date);
        let key;
        if (timeUnit === 'week') {
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
        } else if (timeUnit === 'month') {
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        } else {
          key = date.getFullYear().toString();
        }
        
        if (!groupedData[key]) {
          groupedData[key] = {
            intelligence: [],
            knowledge: [],
            stamina: [],
            willpower: [],
            charisma: [],
            phantomExp: []
          };
        }
        
        groupedData[key].intelligence.push(record.intelligence || 0);
        groupedData[key].knowledge.push(record.knowledge || 0);
        groupedData[key].stamina.push(record.stamina || 0);
        groupedData[key].willpower.push(record.willpower || 0);
        groupedData[key].charisma.push(record.charisma || 0);
        groupedData[key].phantomExp.push(record.phantomExp || 0);
      });
      
      // 计算每个时间单位的平均值
      const averages = Object.keys(groupedData).map(key => {
        const group = groupedData[key];
        return {
          date: key,
          intelligence: average(group.intelligence),
          knowledge: average(group.knowledge),
          stamina: average(group.stamina),
          willpower: average(group.willpower),
          charisma: average(group.charisma),
          phantomExp: average(group.phantomExp)
        };
      });
      
      return averages.sort((a, b) => a.date.localeCompare(b.date));
    }

    // 计算平均值的辅助函数
    function average(arr) {
      return arr.length ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
    }

    // 更新基础属性图表
    function updateBasicChart() {
      const timeUnit = document.getElementById('basic-time-unit').value;
      const chartType = document.getElementById('basic-chart-type').value;
      
      const averages = calculateAverageGrowth(state.history, timeUnit);
      
      const datasets = [
        { label: '智力', data: averages.map(a => a.intelligence), borderColor: '#FF6384', fill: false },
        { label: '知识', data: averages.map(a => a.knowledge), borderColor: '#36A2EB', fill: false },
        { label: '体力', data: averages.map(a => a.stamina), borderColor: '#4BC0C0', fill: false },
        { label: '意志', data: averages.map(a => a.willpower), borderColor: '#FF9F40', fill: false },
        { label: '魅力', data: averages.map(a => a.charisma), borderColor: '#9966FF', fill: false }
      ];
      
      if (basicChart) {
        basicChart.destroy();
      }
      
      basicChart = new Chart(document.getElementById('basic-chart'), {
        type: chartType,
        data: {
          labels: averages.map(a => a.date),
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    // 更新幻构师经验图表
    function updateExpChart() {
      const timeUnit = document.getElementById('exp-time-unit').value;
      const chartType = document.getElementById('exp-chart-type').value;
      
      const averages = calculateAverageGrowth(state.history, timeUnit);
      
      if (expChart) {
        expChart.destroy();
      }
      
      expChart = new Chart(document.getElementById('exp-chart'), {
        type: chartType,
        data: {
          labels: averages.map(a => a.date),
          datasets: [{
            label: '幻构师经验',
            data: averages.map(a => a.phantomExp),
            borderColor: '#80CBC4',
            backgroundColor: 'rgba(128, 203, 196, 0.2)',
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    function initCharts() {
      const basicCtx = document.getElementById('basicAttrChart').getContext('2d');
      basicAttrChart = new Chart(basicCtx, {
        type: 'line',
        data: {
          labels: [],
          datasets: [
            { label: '智力', data: [], borderColor: 'rgba(75, 192, 192, 1)', fill: false },
            { label: '知识', data: [], borderColor: 'rgba(153, 102, 255, 1)', fill: false },
            { label: '体力', data: [], borderColor: 'rgba(255, 159, 64, 1)', fill: false },
            { label: '意志', data: [], borderColor: 'rgba(255, 99, 132, 1)', fill: false },
            { label: '魅力', data: [], borderColor: 'rgba(54, 162, 235, 1)', fill: false }
          ]
        },
        options: {
          responsive: true,
          scales: {
            x: { title: { display: true, text: '日期' } },
            y: { title: { display: true, text: '属性值' }, beginAtZero: true }
          }
        }
      });

      const expCtx = document.getElementById('experienceChart').getContext('2d');
      experienceChart = new Chart(expCtx, {
        type: 'line',
        data: { labels: [], datasets: [ { label: '幻构师经验', data: [], borderColor: 'rgba(255, 205, 86, 1)', fill: false } ] },
        options: {
          responsive: true,
          scales: {
            x: { title: { display: true, text: '日期' } },
            y: { title: { display: true, text: '经验值' }, beginAtZero: true }
          }
        }
      });
    }

    function updateStageCharts() {
      let labels = [];
      let attrData = {
        intelligence: [],
        knowledge: [],
        stamina: [],
        willpower: [],
        charisma: []
      };
      let expData = [];
      let cumulativeExp = 1180; // 设置初始经验值

      const today = new Date();
      const records = state.dailyRecords;

      switch(chartMode) {
        case "monthly":
          // 获取当月数据
          const currentMonth = today.getMonth();
          const currentYear = today.getFullYear();
          records.forEach(record => {
            const recordDate = new Date(record.recordDate);
            if (recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear) {
              labels.push(recordDate.getDate() + '日');
              attrData.intelligence.push(record.intelligenceInc || 0);
              attrData.knowledge.push(record.knowledgeInc || 0);
              attrData.stamina.push(record.staminaInc || 0);
              attrData.willpower.push(record.willpowerInc || 0);
              attrData.charisma.push(record.charmInc || 0);
              cumulativeExp += (record.experienceInc || 0);
              expData.push(cumulativeExp);
            }
          });
          break;

        case "yearly":
          // 获取当年数据
          const thisYear = today.getFullYear();
          records.forEach(record => {
            const recordDate = new Date(record.recordDate);
            if (recordDate.getFullYear() === thisYear) {
              labels.push((recordDate.getMonth() + 1) + '月' + recordDate.getDate() + '日');
              attrData.intelligence.push(record.intelligenceInc || 0);
              attrData.knowledge.push(record.knowledgeInc || 0);
              attrData.stamina.push(record.staminaInc || 0);
              attrData.willpower.push(record.willpowerInc || 0);
              attrData.charisma.push(record.charmInc || 0);
              cumulativeExp += (record.experienceInc || 0);
              expData.push(cumulativeExp);
            }
          });
          break;

        case "past":
          // 获取历史数据
          records.forEach(record => {
            const recordDate = new Date(record.recordDate);
            labels.push(recordDate.getFullYear() + '年' + (recordDate.getMonth() + 1) + '月' + recordDate.getDate() + '日');
            attrData.intelligence.push(record.intelligenceInc || 0);
            attrData.knowledge.push(record.knowledgeInc || 0);
            attrData.stamina.push(record.staminaInc || 0);
            attrData.willpower.push(record.willpowerInc || 0);
            attrData.charisma.push(record.charmInc || 0);
            cumulativeExp += (record.experienceInc || 0);
            expData.push(cumulativeExp);
          });
          break;
      }

      // 更新基础属性图表
      basicAttrChart.data.labels = labels;
      basicAttrChart.data.datasets[0].data = attrData.intelligence;
      basicAttrChart.data.datasets[1].data = attrData.knowledge;
      basicAttrChart.data.datasets[2].data = attrData.stamina;
      basicAttrChart.data.datasets[3].data = attrData.willpower;
      basicAttrChart.data.datasets[4].data = attrData.charisma;
      basicAttrChart.update();

      // 更新经验图表
      experienceChart.data.labels = labels;
      experienceChart.data.datasets[0].data = expData;
      experienceChart.update();
    }

    function recordHistory() {
      const todayStr = new Date().toISOString().slice(0,10);
      const lastRecord = state.history[state.history.length - 1];
      const snapshot = {
        date: todayStr,
        intelligence: state.currentAttributes.intelligence,
        knowledge: state.currentAttributes.knowledge,
        stamina: state.currentAttributes.stamina,
        willpower: state.currentAttributes.willpower,
        charisma: state.currentAttributes.charisma,
        experience: state.currentAttributes.experience
      };
      if(lastRecord && lastRecord.date === todayStr) {
        state.history[state.history.length - 1] = snapshot;
      } else {
        state.history.push(snapshot);
      }
      updateStageCharts();
    }

    /**************** 修正阶段名称函数 ****************/
    function getStageName(levelObj, current) {
      if(current < levelObj.stages[0].min) return levelObj.stages[0].name;
      for (let stage of levelObj.stages) {
        if(current >= stage.min && current <= stage.max) return stage.name;
      }
      return levelObj.stages[levelObj.stages.length - 1].name;
    }

    /**************** 幻构师计划与真理之路进度更新 ****************/
    function updateHuanGoushiLevels() {
      let exp = state.currentAttributes.experience;
      let cumulative = 0;
      let currentLevel = null;
      for (let i = 0; i < state.huanGoushi.levels.length; i++) {
        cumulative += state.huanGoushi.levels[i].total;
        if(exp <= cumulative) {
          currentLevel = state.huanGoushi.levels[i];
          let prevCumulative = cumulative - state.huanGoushi.levels[i].total;
          let currentProgress = exp - prevCumulative;
          currentLevel.current = currentProgress;
          break;
        }
      }
      if(!currentLevel) {
        currentLevel = state.huanGoushi.levels[state.huanGoushi.levels.length - 1];
        currentLevel.current = state.currentAttributes.experience - (cumulative - currentLevel.total);
      }
      document.getElementById('huanGoushiLevel').innerText = "幻构师: " + currentLevel.level + " " + currentLevel.title + " (" + getStageName(currentLevel, currentLevel.current) + ")";
      updateHuanGoushiTable();
    }

    function updateHuanGoushiTable() {
      const tbody = document.getElementById('huanGoushiTable').querySelector('tbody');
      tbody.innerHTML = '';
      state.huanGoushi.levels.forEach(level => {
        let currentExp = state.currentAttributes.experience;
        let prevLevelsTotal = state.huanGoushi.levels
          .slice(0, state.huanGoushi.levels.indexOf(level))
          .reduce((sum, l) => sum + l.total, 0);
        let currentLevelExp = Math.max(0, Math.min(currentExp - prevLevelsTotal, level.total));
        let progress = (currentLevelExp / level.total * 100).toFixed(2);
        let stageName = getStageName(level, currentLevelExp);
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${level.level} ${level.title}</td>
          <td>${level.total}</td>
          <td>${stageName}</td>
          <td>${currentLevelExp}</td>
          <td>
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: ${progress}%" 
                   aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                ${progress}%
              </div>
            </div>
          </td>
        `;
        tbody.appendChild(tr);
      });
    }

    function updateTruthPathLevels() {
      let knowledge = state.currentAttributes.knowledge;
      let cumulativeK = 0;
      let currentKnowledgeLevel = null;
      for (let i = 0; i < state.truthPath.knowledge.length; i++) {
        cumulativeK += state.truthPath.knowledge[i].total;
        if(knowledge <= cumulativeK) {
          currentKnowledgeLevel = state.truthPath.knowledge[i];
          let prev = cumulativeK - state.truthPath.knowledge[i].total;
          let currentProgress = knowledge - prev;
          currentKnowledgeLevel.current = currentProgress;
          break;
        }
      }
      if(!currentKnowledgeLevel) {
        currentKnowledgeLevel = state.truthPath.knowledge[state.truthPath.knowledge.length - 1];
        currentKnowledgeLevel.current = knowledge - (cumulativeK - currentKnowledgeLevel.total);
      }
      let intelligence = state.currentAttributes.intelligence;
      let cumulativeI = 0;
      let currentIntelligenceLevel = null;
      for (let i = 0; i < state.truthPath.intelligence.length; i++) {
        cumulativeI += state.truthPath.intelligence[i].total;
        if(intelligence <= cumulativeI) {
          currentIntelligenceLevel = state.truthPath.intelligence[i];
          let prev = cumulativeI - state.truthPath.intelligence[i].total;
          let currentProgress = intelligence - prev;
          currentIntelligenceLevel.current = currentProgress;
          break;
        }
      }
      if(!currentIntelligenceLevel) {
        currentIntelligenceLevel = state.truthPath.intelligence[state.truthPath.intelligence.length - 1];
        currentIntelligenceLevel.current = intelligence - (cumulativeI - currentIntelligenceLevel.total);
      }
      document.getElementById('truthPathKnowledgeLevel').innerText = "真理之路知识侧: " + currentKnowledgeLevel.level + " " + currentKnowledgeLevel.title + " (" + getStageName(currentKnowledgeLevel, currentKnowledgeLevel.current) + ")";
      document.getElementById('truthPathIntelligenceLevel').innerText = "真理之路智力侧: " + currentIntelligenceLevel.level + " " + currentIntelligenceLevel.title + " (" + getStageName(currentIntelligenceLevel, currentIntelligenceLevel.current) + ")";
      updateTruthPathTable('truthKnowledgeTable', state.truthPath.knowledge, knowledge);
      updateTruthPathTable('truthIntelligenceTable', state.truthPath.intelligence, intelligence);
    }

    function updateTruthPathTable() {
      // 更新知识侧表格
      const knowledgeTbody = document.getElementById('truthKnowledgeTable').querySelector('tbody');
      knowledgeTbody.innerHTML = '';
      state.truthPath.knowledge.forEach(level => {
        let currentKnowledge = state.currentAttributes.knowledge;
        let prevLevelsTotal = state.truthPath.knowledge
          .slice(0, state.truthPath.knowledge.indexOf(level))
          .reduce((sum, l) => sum + l.total, 0);
        let currentLevelKnowledge = Math.max(0, Math.min(currentKnowledge - prevLevelsTotal, level.total));
        let progress = (currentLevelKnowledge / level.total * 100).toFixed(2);
        let stageName = getStageName(level, currentLevelKnowledge);
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${level.level} ${level.title}</td>
          <td>${level.total}</td>
          <td>${stageName}</td>
          <td>${currentLevelKnowledge}</td>
          <td>
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: ${progress}%" 
                   aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                ${progress}%
              </div>
            </div>
          </td>
        `;
        knowledgeTbody.appendChild(tr);
      });

      // 更新智力侧表格
      const intelligenceTbody = document.getElementById('truthIntelligenceTable').querySelector('tbody');
      intelligenceTbody.innerHTML = '';
      state.truthPath.intelligence.forEach(level => {
        let currentIntelligence = state.currentAttributes.intelligence;
        let prevLevelsTotal = state.truthPath.intelligence
          .slice(0, state.truthPath.intelligence.indexOf(level))
          .reduce((sum, l) => sum + l.total, 0);
        let currentLevelIntelligence = Math.max(0, Math.min(currentIntelligence - prevLevelsTotal, level.total));
        let progress = (currentLevelIntelligence / level.total * 100).toFixed(2);
        let stageName = getStageName(level, currentLevelIntelligence);
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${level.level} ${level.title}</td>
          <td>${level.total}</td>
          <td>${stageName}</td>
          <td>${currentLevelIntelligence}</td>
          <td>
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: ${progress}%" 
                   aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                ${progress}%
              </div>
            </div>
          </td>
        `;
        intelligenceTbody.appendChild(tr);
      });
    }

    /**************** 计算连续记录（成功与懈怠） ****************/
    function calculateConsecutiveDailyRecord(type = 'all', specificDate = null) {
      let records = state.dailyRecords.slice().sort((a, b) => new Date(a.recordDate) - new Date(b.recordDate));
      let consecutive = 0;
      if(records.length === 0) return 0;
      
      // 如果没有指定日期，使用最新记录的日期
      let targetDate = specificDate ? new Date(specificDate) : new Date(records[records.length - 1].recordDate);
      let targetRecord = specificDate ? records.find(r => r.recordDate === specificDate) : records[records.length - 1];
      
      // 如果找不到指定日期的记录，返回0
      if (!targetRecord) return 0;
      
      // 根据类型检查不同的条件
      let hasActivity = false;
      if(type === 'all') {
        hasActivity = targetRecord.studyHours > 0 || targetRecord.readingPages > 0 || targetRecord.paintingHours > 0;
      } else if(type === 'study') {
        hasActivity = targetRecord.studyHours > 0 || targetRecord.readingPages > 0;
      } else if(type === 'painting') {
        hasActivity = targetRecord.paintingHours > 0;
      }
      
      if(hasActivity) {
        consecutive = 1;
        let lastDate = targetDate;
        
        // 找到目标记录在数组中的索引
        let targetIndex = records.findIndex(r => r.recordDate === targetRecord.recordDate);
        
        // 从目标记录向前遍历
        for(let i = targetIndex - 1; i >= 0; i--) {
          let currentDate = new Date(records[i].recordDate);
          let diffDays = (lastDate - currentDate) / (1000 * 60 * 60 * 24);
          
          let currentHasActivity = false;
          if(type === 'all') {
            currentHasActivity = records[i].studyHours > 0 || records[i].readingPages > 0 || records[i].paintingHours > 0;
          } else if(type === 'study') {
            currentHasActivity = records[i].studyHours > 0 || records[i].readingPages > 0;
          } else if(type === 'painting') {
            currentHasActivity = records[i].paintingHours > 0;
          }
          
          if(diffDays === 1 && currentHasActivity) {
            consecutive++;
            lastDate = currentDate;
          } else {
            break;
          }
        }
      }
      return consecutive;
    }

    function calculateConsecutiveSlackDailyRecord(type = 'all', specificDate = null) {
      let records = state.dailyRecords.slice().sort((a, b) => new Date(a.recordDate) - new Date(b.recordDate));
      let consecutive = 0;
      if(records.length === 0) return 0;
      
      // 如果没有指定日期，使用最新记录的日期
      let targetDate = specificDate ? new Date(specificDate) : new Date(records[records.length - 1].recordDate);
      let targetRecord = specificDate ? records.find(r => r.recordDate === specificDate) : records[records.length - 1];
      
      // 如果找不到指定日期的记录，返回0
      if (!targetRecord) return 0;
      
      // 根据类型检查不同的条件
      let isSlack = false;
      if(type === 'all') {
        isSlack = targetRecord.studyHours === 0 && targetRecord.readingPages === 0 && targetRecord.paintingHours === 0;
      } else if(type === 'study') {
        isSlack = targetRecord.studyHours === 0 && targetRecord.readingPages === 0;
      } else if(type === 'painting') {
        isSlack = targetRecord.paintingHours === 0;
      }
      
      if(isSlack) {
        consecutive = 1;
        let lastDate = targetDate;
        
        // 找到目标记录在数组中的索引
        let targetIndex = records.findIndex(r => r.recordDate === targetRecord.recordDate);
        
        // 从目标记录向前遍历
        for(let i = targetIndex - 1; i >= 0; i--) {
          let currentIsSlack = false;
          if(type === 'all') {
            currentIsSlack = records[i].studyHours === 0 && records[i].readingPages === 0 && records[i].paintingHours === 0;
          } else if(type === 'study') {
            currentIsSlack = records[i].studyHours === 0 && records[i].readingPages === 0;
          } else if(type === 'painting') {
            currentIsSlack = records[i].paintingHours === 0;
          }
          
          if(currentIsSlack) {
            let currentDate = new Date(records[i].recordDate);
            let diffDays = (lastDate - currentDate) / (1000 * 60 * 60 * 24);
            if(diffDays === 1) {
              consecutive++;
              lastDate = currentDate;
            } else {
              break;
            }
          } else {
            break;
          }
        }
      }
      return consecutive;
    }

    /**************** 更新称号表 ****************/
    function updateTitlesTable() {
      let consecutiveCheckin = calculateConsecutiveCheckin();
      const checkinTbody = document.getElementById('checkinTitleTable').querySelector('tbody');
      checkinTbody.innerHTML = "";
      state.titles.checkin.forEach(item => {
        let progress = Math.min((consecutiveCheckin / item.required) * 100, 100);
        let statusText = (consecutiveCheckin >= item.required) ? "已获得" : progress.toFixed(2) + "%";
        let tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${item.level}</td>
          <td>${item.title}</td>
          <td>${item.required}</td>
          <td>${consecutiveCheckin}</td>
          <td>${item.reward}</td>
          <td>
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: ${progress.toFixed(2)}%;" aria-valuenow="${progress.toFixed(2)}" aria-valuemin="0" aria-valuemax="100">${statusText}</div>
            </div>
          </td>
        `;
        checkinTbody.appendChild(tr);
      });
      
      // 更新当前称号显示
      let currentCheckinTitle = "无";
      for(let i = state.titles.checkin.length - 1; i >= 0; i--) {
        if(consecutiveCheckin >= state.titles.checkin[i].required) {
          currentCheckinTitle = state.titles.checkin[i].title;
          break;
        }
      }
      document.getElementById('checkinTitle').innerText = "晨曦之约: " + currentCheckinTitle;

      const willTbody = document.getElementById('willTitleTable').querySelector('tbody');
      willTbody.innerHTML = "";
      state.titles.will.forEach(item => {
        let progress = Math.min((state.currentAttributes.willpower / item.required) * 100, 100);
        let statusText = (state.currentAttributes.willpower >= item.required) ? "已获得" : progress.toFixed(2) + "%";
        let tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${item.level}</td>
          <td>${item.title}</td>
          <td>${item.required}</td>
          <td>${state.currentAttributes.willpower}</td>
          <td>${item.reward}</td>
          <td>
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: ${progress.toFixed(2)}%;" aria-valuenow="${progress.toFixed(2)}" aria-valuemin="0" aria-valuemax="100">${statusText}</div>
            </div>
          </td>
        `;
        willTbody.appendChild(tr);
      });
      
      // 更新当前意志称号显示
      let currentWillTitle = "无";
      for(let i = state.titles.will.length - 1; i >= 0; i--) {
        if(state.currentAttributes.willpower >= state.titles.will[i].required) {
          currentWillTitle = state.titles.will[i].title;
          break;
        }
      }
      document.getElementById('willTitle').innerText = "意志称号: " + currentWillTitle;

      const charmTbody = document.getElementById('charmTitleTable').querySelector('tbody');
      charmTbody.innerHTML = "";
      state.titles.charm.forEach(item => {
        let progress = Math.min((state.currentAttributes.charisma / item.required) * 100, 100);
        let statusText = (state.currentAttributes.charisma >= item.required) ? "已获得" : progress.toFixed(2) + "%";
        let tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${item.level}</td>
          <td>${item.title}</td>
          <td>${item.required}</td>
          <td>${state.currentAttributes.charisma.toFixed(2)}</td>
          <td>${item.reward}</td>
          <td>
            <div class="progress">
              <div class="progress-bar" role="progressbar" style="width: ${progress.toFixed(2)}%;" aria-valuenow="${progress.toFixed(2)}" aria-valuemin="0" aria-valuemax="100">${statusText}</div>
            </div>
          </td>
        `;
        charmTbody.appendChild(tr);
      });
      
      // 更新当前魅力称号显示
      let currentCharmTitle = "无";
      for(let i = state.titles.charm.length - 1; i >= 0; i--) {
        if(state.currentAttributes.charisma >= state.titles.charm[i].required) {
          currentCharmTitle = state.titles.charm[i].title;
          break;
        }
      }
      document.getElementById('charmTitle').innerText = "魅力称号: " + currentCharmTitle;
    }

    /**************** 计算连续打卡天数 ****************/
    function calculateConsecutiveCheckin(specificDate = null) {
      let records = state.checkinRecords.slice().sort((a, b) => new Date(a.wakeDate) - new Date(b.wakeDate));
      let consecutive = 0;
      if(records.length === 0) return 0;
      
      // 如果没有指定日期，使用最新记录的日期
      let targetDate = specificDate ? new Date(specificDate) : new Date(records[records.length - 1].wakeDate);
      let targetRecord = specificDate ? records.find(r => r.wakeDate === specificDate) : records[records.length - 1];
      
      // 如果找不到指定日期的记录或记录状态不是成功，返回0
      if(!targetRecord || targetRecord.status !== "成功") return 0;
      
      consecutive = 1;
      let lastDate = targetDate;
      
      // 找到目标记录在数组中的索引
      let targetIndex = records.findIndex(r => r.wakeDate === targetRecord.wakeDate);
      
      // 从目标记录向前遍历
      for (let i = targetIndex - 1; i >= 0; i--) {
        let currentDate = new Date(records[i].wakeDate);
        let diffDays = (lastDate - currentDate) / (1000 * 60 * 60 * 24);
        if(diffDays === 1 && records[i].status === "成功") {
          consecutive++;
          lastDate = currentDate;
        } else {
          break;
        }
      }
      
      // 从目标记录向后遍历
      lastDate = targetDate;
      for (let i = targetIndex + 1; i < records.length; i++) {
        let currentDate = new Date(records[i].wakeDate);
        let diffDays = (currentDate - lastDate) / (1000 * 60 * 60 * 24);
        if(diffDays === 1 && records[i].status === "成功") {
          consecutive++;
          lastDate = currentDate;
        } else {
          break;
        }
      }
      
      return consecutive;
    }

    /**************** 分页与历史记录更新函数 ****************/
    function updateDailyRecordTable() {
      let records = state.dailyRecords.slice().sort((a, b) => new Date(b.recordDate) - new Date(a.recordDate));
      if(!dailyHistoryMode) {
        const today = new Date().toISOString().slice(0,10);
        records = records.filter(r => r.recordDate === today);
      }
      let totalRecords = records.length;
      let totalPages = Math.ceil(totalRecords / 10) || 1;
      if(dailyRecordPage > totalPages) dailyRecordPage = totalPages;
      if(dailyRecordPage < 1) dailyRecordPage = 1;
      let startIndex = (dailyRecordPage - 1) * 10;
      let paginatedRecords = records.slice(startIndex, startIndex + 10);
      const tbody = document.getElementById('dailyRecordTable').querySelector('tbody');
      tbody.innerHTML = "";
      paginatedRecords.forEach((record) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${record.recordDate || new Date().toISOString().slice(0,10)}</td>
          <td>${record.studyHours}</td>
          <td>${record.readingPages}</td>
          <td>${record.paintingHours}</td>
          <td>${record.experienceInc}</td>
          <td>${record.intelligenceInc}</td>
          <td>${record.knowledgeInc}</td>
          <td>${record.staminaInc}</td>
          <td>${record.willpowerInc}</td>
          <td>${record.charmInc.toFixed(2)}</td>
          <td>${record.remark}</td>
          <td>
            <button class="btn btn-sm btn-warning" onclick="editDailyRecord(${state.dailyRecords.indexOf(record)})">编辑</button>
            <button class="btn btn-sm btn-danger" onclick="deleteDailyRecord(${state.dailyRecords.indexOf(record)})">删除</button>
          </td>
        `;
        tbody.appendChild(tr);
      });
      let paginationDiv = document.getElementById('dailyRecordPagination');
      paginationDiv.innerHTML = "";
      let prevBtn = document.createElement('button');
      prevBtn.className = "btn btn-sm btn-secondary me-2";
      prevBtn.innerText = "上一页";
      prevBtn.onclick = () => { dailyRecordPage--; updateDailyRecordTable(); };
      let nextBtn = document.createElement('button');
      nextBtn.className = "btn btn-sm btn-secondary";
      nextBtn.innerText = "下一页";
      nextBtn.onclick = () => { dailyRecordPage++; updateDailyRecordTable(); };
      let pageInfo = document.createElement('span');
      pageInfo.className = "align-self-center";
      pageInfo.innerText = ` 第 ${dailyRecordPage} 页 / 共 ${totalPages} 页 `;
      paginationDiv.appendChild(prevBtn);
      paginationDiv.appendChild(pageInfo);
      paginationDiv.appendChild(nextBtn);
    }

    function updateCheckinTable() {
      let records = state.checkinRecords.slice().sort((a, b) => new Date(b.sleepDate) - new Date(a.sleepDate));
      if(!checkinHistoryMode) {
        const today = new Date().toISOString().slice(0,10);
        records = records.filter(r => r.sleepDate === today);
      }
      let totalRecords = records.length;
      let totalPages = Math.ceil(totalRecords / 10) || 1;
      if(checkinPage > totalPages) checkinPage = totalPages;
      if(checkinPage < 1) checkinPage = 1;
      let startIndex = (checkinPage - 1) * 10;
      let paginatedRecords = records.slice(startIndex, startIndex + 10);
      const tbody = document.getElementById('checkinTable').querySelector('tbody');
      tbody.innerHTML = "";
      paginatedRecords.forEach((record) => {
        // 在原始数组中查找记录的真实索引
        const originalIndex = state.checkinRecords.findIndex(r => 
          r.sleepDate === record.sleepDate && 
          r.wakeDate === record.wakeDate && 
          r.sleepTime === record.sleepTime && 
          r.wakeTime === record.wakeTime
        );
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${record.sleepDate}</td>
          <td>${record.wakeDate}</td>
          <td>${record.sleepTime}</td>
          <td>${record.wakeTime}</td>
          <td>${record.status}</td>
          <td>${record.special ? "是" : "否"}</td>
          <td>
            <button class="btn btn-sm btn-warning edit-checkin" data-index="${originalIndex}">编辑</button>
            <button class="btn btn-sm btn-danger delete-checkin" data-index="${originalIndex}">删除</button>
          </td>
        `;
        tbody.appendChild(tr);
      });
      
      // 为新添加的按钮绑定事件监听器
      tbody.querySelectorAll('.edit-checkin').forEach(button => {
        button.addEventListener('click', function() {
          editCheckin(parseInt(this.dataset.index));
        });
      });
      
      tbody.querySelectorAll('.delete-checkin').forEach(button => {
        button.addEventListener('click', function() {
          deleteCheckin(parseInt(this.dataset.index));
        });
      });
      let paginationDiv = document.getElementById('checkinPagination');
      paginationDiv.innerHTML = "";
      let prevBtn = document.createElement('button');
      prevBtn.className = "btn btn-sm btn-secondary me-2";
      prevBtn.innerText = "上一页";
      prevBtn.onclick = () => { checkinPage--; updateCheckinTable(); };
      let nextBtn = document.createElement('button');
      nextBtn.className = "btn btn-sm btn-secondary";
      nextBtn.innerText = "下一页";
      nextBtn.onclick = () => { checkinPage++; updateCheckinTable(); };
      let pageInfo = document.createElement('span');
      pageInfo.className = "align-self-center";
      pageInfo.innerText = ` 第 ${checkinPage} 页 / 共 ${totalPages} 页 `;
      paginationDiv.appendChild(prevBtn);
      paginationDiv.appendChild(pageInfo);
      paginationDiv.appendChild(nextBtn);
    }

    function updateSummaryTable() {
      let records = state.summaries.slice().sort((a, b) => new Date(b.summaryDate) - new Date(a.summaryDate));
      if(!summaryHistoryMode) {
        const today = new Date().toISOString().slice(0,10);
        records = records.filter(r => r.summaryDate === today);
      }
      let totalRecords = records.length;
      let totalPages = Math.ceil(totalRecords / 10) || 1;
      if(summaryPage > totalPages) summaryPage = totalPages;
      if(summaryPage < 1) summaryPage = 1;
      let startIndex = (summaryPage - 1) * 10;
      let paginatedRecords = records.slice(startIndex, startIndex + 10);
      const tbody = document.getElementById('summaryTable').querySelector('tbody');
      tbody.innerHTML = "";
      paginatedRecords.forEach((record) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${record.summaryDate}</td>
          <td>${record.summaryContent}</td>
          <td>
            <button class="btn btn-sm btn-warning" onclick="editSummary(${state.summaries.indexOf(record)})">编辑</button>
            <button class="btn btn-sm btn-danger" onclick="deleteSummary(${state.summaries.indexOf(record)})">删除</button>
          </td>
        `;
        tbody.appendChild(tr);
      });
      let paginationDiv = document.getElementById('summaryPagination');
      paginationDiv.innerHTML = "";
      let prevBtn = document.createElement('button');
      prevBtn.className = "btn btn-sm btn-secondary me-2";
      prevBtn.innerText = "上一页";
      prevBtn.onclick = () => { summaryPage--; updateSummaryTable(); };
      let nextBtn = document.createElement('button');
      nextBtn.className = "btn btn-sm btn-secondary";
      nextBtn.innerText = "下一页";
      nextBtn.onclick = () => { summaryPage++; updateSummaryTable(); };
      let pageInfo = document.createElement('span');
      pageInfo.className = "align-self-center";
      pageInfo.innerText = ` 第 ${summaryPage} 页 / 共 ${totalPages} 页 `;
      paginationDiv.appendChild(prevBtn);
      paginationDiv.appendChild(pageInfo);
      paginationDiv.appendChild(nextBtn);
    }

    /**************** 获取当前等级进度 ****************/
    function getCurrentLevelProgress(levelsArray) {
      let currentLevel = null;
      let attrValue = 0;
      
      if(levelsArray === state.huanGoushi.levels) {
        attrValue = state.currentAttributes.experience;
      } else if(levelsArray === state.truthPath.knowledge) {
        attrValue = state.currentAttributes.knowledge;
      } else if(levelsArray === state.truthPath.intelligence) {
        attrValue = state.currentAttributes.intelligence;
      }
      
      let cumulative = 0;
      for (let i = 0; i < levelsArray.length; i++) {
        cumulative += levelsArray[i].total;
        if(attrValue <= cumulative) {
          currentLevel = levelsArray[i];
          let prevCumulative = cumulative - levelsArray[i].total;
          let currentProgress = attrValue - prevCumulative;
          let percentage = ((currentProgress / currentLevel.total) * 100).toFixed(2);
          return `${currentLevel.level} ${currentLevel.title} (${getStageName(currentLevel, currentProgress)}) - ${currentProgress}/${currentLevel.total} (${percentage}%)`;
        }
      }
      
      // 如果超过所有等级
      currentLevel = levelsArray[levelsArray.length - 1];
      let currentProgress = attrValue - (cumulative - currentLevel.total);
      return `${currentLevel.level} ${currentLevel.title} (${getStageName(currentLevel, currentProgress)}) - ${currentProgress}/${currentLevel.total} (100%)`;
    }
    
    function updateTotalRecords() {
  const tbody = document.getElementById('totalRecordTable').querySelector('tbody');
  tbody.innerHTML = '';
  
  // 获取所有日期的记录
  const allDates = new Set();
  state.dailyRecords.forEach(r => allDates.add(r.recordDate));
  state.checkinRecords.forEach(r => allDates.add(r.sleepDate));
  state.summaries.forEach(r => allDates.add(r.summaryDate));
  
  // 转换为数组并排序
  const dates = Array.from(allDates).sort((a, b) => new Date(b) - new Date(a));
  
  // 分页处理
  const itemsPerPage = 10;
  const totalPages = Math.ceil(dates.length / itemsPerPage) || 1;
  if(totalPage > totalPages) totalPage = totalPages;
  if(totalPage < 1) totalPage = 1;
  const startIndex = (totalPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, dates.length);
  const displayDates = totalHistoryMode ? dates : dates.slice(startIndex, endIndex);
  
  displayDates.forEach(date => {
    const row = document.createElement('tr');
    
    // 获取当天和前一天的数据
    const currentDayRecords = state.dailyRecords.filter(r => r.recordDate === date);
    const previousDayRecords = state.dailyRecords.filter(r => {
      const recordDate = new Date(r.recordDate);
      const prevDate = new Date(date);
      prevDate.setDate(prevDate.getDate() - 1);
      return recordDate.toISOString().split('T')[0] === prevDate.toISOString().split('T')[0];
    });
    
    // 计算进度变化
    const currentExp = currentDayRecords.reduce((sum, r) => sum + (r.experienceInc || 0), 0);
    const currentKnowledge = currentDayRecords.reduce((sum, r) => sum + (r.knowledgeInc || 0), 0);
    const currentIntelligence = currentDayRecords.reduce((sum, r) => sum + (r.intelligenceInc || 0), 0);
    
    // 创建进度显示HTML，包含增减状态指示器
    function createProgressHtml(value, type) {
      const icon = value > 0 ? '↑' : value < 0 ? '↓' : '=';
      const color = value > 0 ? 'text-success' : value < 0 ? 'text-danger' : 'text-secondary';
      return `<div class="${color}">${type}: ${value.toFixed(1)} ${icon}</div>`;
    }
    
    row.innerHTML = `
      <td class="text-center">${date}</td>
      <td class="text-center">${createProgressHtml(currentExp, '幻构师')}</td>
      <td class="text-center">${createProgressHtml(currentKnowledge, '知识侧')}</td>
      <td class="text-center">${createProgressHtml(currentIntelligence, '智力侧')}</td>
      <td class="text-center">${state.checkinRecords.some(r => r.sleepDate === date) ? '✓' : '✗'}</td>
      <td class="text-center">${state.summaries.some(r => r.summaryDate === date) ? '✓' : '✗'}</td>
      <td class="text-center">
        <button class="btn btn-sm btn-info" onclick="viewDayDetail('${date}')">详情</button>
      </td>
    `;
    
    tbody.appendChild(row);
  });
  
  // 更新分页
  const paginationDiv = document.getElementById('totalRecordPagination');
  paginationDiv.innerHTML = "";
  let prevBtn = document.createElement('button');
  prevBtn.className = "btn btn-sm btn-secondary me-2";
  prevBtn.innerText = "上一页";
  prevBtn.onclick = () => { totalPage--; updateTotalRecords(); };
  let nextBtn = document.createElement('button');
  nextBtn.className = "btn btn-sm btn-secondary";
  nextBtn.innerText = "下一页";
  nextBtn.onclick = () => { totalPage++; updateTotalRecords(); };
  let pageInfo = document.createElement('span');
  pageInfo.className = "align-self-center";
  pageInfo.innerText = ` 第 ${totalPage} 页 / 共 ${totalPages} 页 `;
  paginationDiv.appendChild(prevBtn);
  paginationDiv.appendChild(pageInfo);
  paginationDiv.appendChild(nextBtn);
}

// 添加查看详情功能
window.viewDayDetail = function(date) {
  const records = state.dailyRecords.filter(r => r.recordDate === date);
  const checkin = state.checkinRecords.find(r => r.sleepDate === date);
  const summary = state.summaries.find(r => r.summaryDate === date);
  
  let detailContent = `<h5>日期：${date}</h5>`;
  if (records.length > 0) {
    detailContent += '<h6>每日记录：</h6>';
    records.forEach(r => {
      detailContent += `<p>学习：${r.studyHours}小时，阅读：${r.readingPages}页，绘画：${r.paintingHours}小时</p>`;
    });
  }
  if (checkin) {
    detailContent += `<h6>晨曦打卡：</h6><p>睡觉时间：${checkin.sleepTime}，起床时间：${checkin.wakeTime}</p>`;
  }
  if (summary) {
    detailContent += `<h6>每日总结：</h6><p>${summary.summaryContent}</p>`;
  }
  
  const modal = new bootstrap.Modal(document.getElementById('statusInfoModal'));
  document.querySelector('#statusInfoModal .modal-title').textContent = '每日详情';
  document.querySelector('#statusInfoModal .modal-body').innerHTML = detailContent;
  modal.show();
};

    /**************** 事件处理及记录更新 ****************/
    document.getElementById('addDailyRecord').addEventListener('click', function() {
      const studyHours = parseFloat(document.getElementById('studyHours').value) || 0;
      const readingPages = parseFloat(document.getElementById('readingPages').value) || 0;
      const paintingHours = parseFloat(document.getElementById('paintingHours').value) || 0;
      const intelligenceBase = studyHours;
      const knowledgeBase = readingPages / 10;
      const experienceBase = paintingHours * 10;
      let remark = "";
      const recordDate = new Date().toISOString().slice(0,10);
      let record = { recordDate, studyHours, readingPages, paintingHours, intelligenceInc: 0, knowledgeInc: 0, experienceInc: 0, staminaInc: 0, willpowerInc: 0, charmInc: 0, remark };
      
      // 逆水行舟·意志计算
      const hasActivity = studyHours > 0 || readingPages > 0 || paintingHours > 0;
      
      // 先添加记录以便计算连续天数
      state.dailyRecords.push({...record});
      let consecutiveDays = calculateConsecutiveDailyRecord('all', recordDate);
      let slackDays = calculateConsecutiveSlackDailyRecord('all', recordDate);
      // 移除临时添加的记录
      state.dailyRecords.pop();
      
      // 独立计算逆水行舟·意志法则
      let willpowerChange = 0;
      if (hasActivity) {
        // 行舟法则：连续执行增加意志
        if (consecutiveDays >= 30) {
          willpowerChange = 3;
          remark += "逆水行舟·意志(行舟30天) +3意志 ";
        } else if (consecutiveDays >= 7) {
          willpowerChange = 2;
          remark += "逆水行舟·意志(行舟7天) +2意志 ";
        } else if (consecutiveDays >= 3) {
          willpowerChange = 1;
          remark += "逆水行舟·意志(行舟3天) +1意志 ";
        }
      } else {
        // 废退法则：懈怠减少意志
        if (slackDays >= 7) {
          willpowerChange = -3;
          remark += "逆水行舟·意志(懈怠7天) -3意志 ";
        } else if (slackDays >= 3) {
          willpowerChange = -2;
          remark += "逆水行舟·意志(懈怠3天) -2意志 ";
        } else {
          willpowerChange = -1;
          remark += "逆水行舟·意志(懈怠) -1意志 ";
        }
      }
      
      // 应用意志变化并更新显示
      record.willpowerInc = willpowerChange;
      state.currentAttributes.willpower = Math.max(0, state.currentAttributes.willpower + willpowerChange);
      updateAllDisplays();
      
      // 逆水行舟·幻构师计算
      let huanGoushiSuccess = paintingHours > 0;
      if (huanGoushiSuccess) {
        // 幻构师行舟法则：连续执行增加经验效率
        // 先添加记录以便计算连续天数
        state.dailyRecords.push({...record});
        let huanGoushiDays = calculateConsecutiveDailyRecord('painting', recordDate);
        // 移除临时添加的记录
        state.dailyRecords.pop();
        
        let huanGoushiBonusRate = 0;
        if (huanGoushiDays >= 30) { huanGoushiBonusRate = 0.40; }
        else if (huanGoushiDays >= 7) { huanGoushiBonusRate = 0.25; }
        else if (huanGoushiDays >= 3) { huanGoushiBonusRate = 0.10; }
        record.experienceInc = experienceBase * (1 + huanGoushiBonusRate);
      } else {
        // 幻构师废退法则：懈怠扣除经验
        // 先添加记录以便计算连续天数
        state.dailyRecords.push({...record});
        let huanGoushiSlackDays = calculateConsecutiveSlackDailyRecord('painting', recordDate);
        // 移除临时添加的记录
        state.dailyRecords.pop();
        
        let penaltyExp = (huanGoushiSlackDays >= 7) ? 100 : (huanGoushiSlackDays >= 3 ? 80 : 50);
        record.experienceInc = -penaltyExp;
      }
      
      // 逆水行舟·真理之路计算
      let truthPathSuccess = studyHours > 0 || readingPages > 0;
      if (truthPathSuccess) {
        // 真理之路行舟法则：连续执行增加知识和智力获取效率
        // 先添加记录以便计算连续天数
        state.dailyRecords.push({...record});
        let truthPathDays = calculateConsecutiveDailyRecord('study', recordDate);
        // 移除临时添加的记录
        state.dailyRecords.pop();
        
        let truthPathBonusRate = 0;
        if (truthPathDays >= 30) { truthPathBonusRate = 0.40; }
        else if (truthPathDays >= 7) { truthPathBonusRate = 0.25; }
        else if (truthPathDays >= 3) { truthPathBonusRate = 0.10; }
        record.intelligenceInc = intelligenceBase * (1 + truthPathBonusRate);
        record.knowledgeInc = knowledgeBase * (1 + truthPathBonusRate);
      } else {
        // 真理之路废退法则：懈怠扣除知识和智力
        // 先添加记录以便计算连续天数
        state.dailyRecords.push({...record});
        let truthPathSlackDays = calculateConsecutiveSlackDailyRecord('study', recordDate);
        // 移除临时添加的记录
        state.dailyRecords.pop();
        
        let penaltyKnowledge = (truthPathSlackDays >= 7) ? 1 : (truthPathSlackDays >= 3 ? 0.3 : 0.1);
        let penaltyIntelligence = (truthPathSlackDays >= 7) ? 5 : (truthPathSlackDays >= 3 ? 1.5 : 0.5);
        record.intelligenceInc = -penaltyIntelligence;
        record.knowledgeInc = -penaltyKnowledge;
      }
      
      // 设置备注
      if (!huanGoushiSuccess && !truthPathSuccess) {
        remark = "懈怠";
      }
      record.remark = remark;
      state.dailyRecords.push(record);
      state.currentAttributes.intelligence += record.intelligenceInc;
      state.currentAttributes.knowledge += record.knowledgeInc;
      state.currentAttributes.experience += record.experienceInc;
      state.currentAttributes.willpower += record.willpowerInc;
      if(huanGoushiSuccess || truthPathSuccess) {
        let bonusCharm = (record.intelligenceInc + record.knowledgeInc) * 0.1;
        record.charmInc = bonusCharm;
        state.currentAttributes.charisma += bonusCharm;
      }
      updateDailyRecordTable();
      updateTotalRecords();
      updateAllDisplays();
      recordHistory();
      updateNiShuiStatusDisplay();
      applyNiShuiXingChuan();
      
      // 清空输入框
      document.getElementById('studyHours').value = '';
      document.getElementById('readingPages').value = '';
      document.getElementById('paintingHours').value = '';
    });

    window.editDailyRecord = function(index) {
  const record = state.dailyRecords[index];
  // 创建模态框
  const modalHtml = `
    <div class="modal fade" id="editDailyRecordModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">编辑每日记录</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="editDailyRecordForm">
              <div class="mb-3">
                <label class="form-label">日期</label>
                <input type="date" class="form-control" id="editRecordDate" value="${record.recordDate}">
              </div>
              <div class="mb-3">
                <label class="form-label">学习时长（小时）</label>
                <input type="number" class="form-control" id="editStudyHours" value="${record.studyHours}" step="0.1" min="0">
              </div>
              <div class="mb-3">
                <label class="form-label">阅读页数</label>
                <input type="number" class="form-control" id="editReadingPages" value="${record.readingPages}" step="1" min="0">
              </div>
              <div class="mb-3">
                <label class="form-label">绘画时长（小时）</label>
                <input type="number" class="form-control" id="editPaintingHours" value="${record.paintingHours}" step="0.1" min="0">
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="saveDailyRecord(${index})">保存</button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // 添加模态框到body
  document.body.insertAdjacentHTML('beforeend', modalHtml);
  
  // 显示模态框
  const modal = new bootstrap.Modal(document.getElementById('editDailyRecordModal'));
  modal.show();
  
  // 模态框关闭时移除
  document.getElementById('editDailyRecordModal').addEventListener('hidden.bs.modal', function() {
    this.remove();
  });
};

window.saveDailyRecord = function(index) {
  const record = state.dailyRecords[index];
  const newDate = document.getElementById('editRecordDate').value;
  const newStudy = parseFloat(document.getElementById('editStudyHours').value) || 0;
  const newRead = parseFloat(document.getElementById('editReadingPages').value) || 0;
  const newPaint = parseFloat(document.getElementById('editPaintingHours').value) || 0;
  
  // 先从当前状态中减去原记录的属性值
  state.currentAttributes.intelligence -= record.intelligenceInc;
  state.currentAttributes.knowledge -= record.knowledgeInc;
  state.currentAttributes.experience -= record.experienceInc;
  state.currentAttributes.charisma -= record.charmInc;
  state.currentAttributes.willpower -= record.willpowerInc;
  state.currentAttributes.stamina -= record.staminaInc;
  
  const studyHours = newStudy;
  const readingPages = newRead;
  const paintingHours = newPaint;
  const intelligenceBase = studyHours;
  const knowledgeBase = readingPages / 10;
  const experienceBase = paintingHours * 10;
  let remark = "";
  
  // 临时移除当前记录以计算正确的连续天数
  state.dailyRecords.splice(index, 1);
  
  // 创建新记录并添加到数组中，以便计算连续天数
  let tempRecord = { 
    recordDate: newDate,
    studyHours, 
    readingPages, 
    paintingHours, 
    intelligenceInc: 0, 
    knowledgeInc: 0, 
    experienceInc: 0, 
    staminaInc: 0, 
    willpowerInc: 0, 
    charmInc: 0, 
    remark 
  };
  state.dailyRecords.push(tempRecord);
  
  // 逆水行舟·意志计算
  const hasActivity = studyHours > 0 || readingPages > 0 || paintingHours > 0;
  let willpowerChange = 0;
  
  if (hasActivity) {
    let consecutiveDays = calculateConsecutiveDailyRecord('all', newDate);
    if (consecutiveDays >= 30) {
      willpowerChange = 3;
      remark += "逆水行舟·意志(行舟30天) +3意志 ";
    } else if (consecutiveDays >= 7) {
      willpowerChange = 2;
      remark += "逆水行舟·意志(行舟7天) +2意志 ";
    } else if (consecutiveDays >= 3) {
      willpowerChange = 1;
      remark += "逆水行舟·意志(行舟3天) +1意志 ";
    }
  } else {
    let willSlackDays = calculateConsecutiveSlackDailyRecord('all', newDate);
    if (willSlackDays >= 7) {
      willpowerChange = -3;
      remark += "逆水行舟·意志(懈怠7天) -3意志 ";
    } else if (willSlackDays >= 3) {
      willpowerChange = -2;
      remark += "逆水行舟·意志(懈怠3天) -2意志 ";
    } else {
      willpowerChange = -1;
      remark += "逆水行舟·意志(懈怠) -1意志 ";
    }
  }
  
  tempRecord.willpowerInc = willpowerChange;
  
  // 逆水行舟·幻构师计算
  let huanGoushiSuccess = paintingHours > 0;
  if (huanGoushiSuccess) {
    // 幻构师行舟法则：连续执行增加经验效率
    let huanGoushiDays = calculateConsecutiveDailyRecord('painting', newDate);
    let huanGoushiBonusRate = 0;
    if (huanGoushiDays >= 30) { huanGoushiBonusRate = 0.40; }
    else if (huanGoushiDays >= 7) { huanGoushiBonusRate = 0.25; }
    else if (huanGoushiDays >= 3) { huanGoushiBonusRate = 0.10; }
    tempRecord.experienceInc = experienceBase * (1 + huanGoushiBonusRate);
  } else {
    // 幻构师废退法则：懈怠扣除经验
    let huanGoushiSlackDays = calculateConsecutiveSlackDailyRecord('painting', newDate);
    let penaltyExp = (huanGoushiSlackDays >= 7) ? 100 : (huanGoushiSlackDays >= 3 ? 80 : 50);
    tempRecord.experienceInc = -penaltyExp;
  }
  
  // 逆水行舟·真理之路计算
  let truthPathSuccess = studyHours > 0 || readingPages > 0;
  if (truthPathSuccess) {
    // 真理之路行舟法则：连续执行增加知识和智力获取效率
    let truthPathDays = calculateConsecutiveDailyRecord('study', newDate);
    let truthPathBonusRate = 0;
    if (truthPathDays >= 30) { truthPathBonusRate = 0.40; }
    else if (truthPathDays >= 7) { truthPathBonusRate = 0.25; }
    else if (truthPathDays >= 3) { truthPathBonusRate = 0.10; }
    tempRecord.intelligenceInc = intelligenceBase * (1 + truthPathBonusRate);
    tempRecord.knowledgeInc = knowledgeBase * (1 + truthPathBonusRate);
  } else {
    // 真理之路废退法则：懈怠扣除知识和智力
    let truthPathSlackDays = calculateConsecutiveSlackDailyRecord('study', newDate);
    let penaltyKnowledge = (truthPathSlackDays >= 7) ? 1 : (truthPathSlackDays >= 3 ? 0.3 : 0.1);
    let penaltyIntelligence = (truthPathSlackDays >= 7) ? 5 : (truthPathSlackDays >= 3 ? 1.5 : 0.5);
    tempRecord.intelligenceInc = -penaltyIntelligence;
    tempRecord.knowledgeInc = -penaltyKnowledge;
  }
  
  // 设置备注
  if (!huanGoushiSuccess && !truthPathSuccess) {
    remark = "懈怠";
  }
  tempRecord.remark = remark;
  
  // 从数组中移除临时记录
  state.dailyRecords.pop();
  
  // 重新插入记录
  state.dailyRecords.splice(index, 0, tempRecord);
  
  // 更新当前状态的属性值
  state.currentAttributes.intelligence += tempRecord.intelligenceInc;
  state.currentAttributes.knowledge += tempRecord.knowledgeInc;
  state.currentAttributes.experience += tempRecord.experienceInc;
  state.currentAttributes.willpower += tempRecord.willpowerInc;
  state.currentAttributes.stamina += tempRecord.staminaInc;
  
  // 计算魅力加成
  if(huanGoushiSuccess || truthPathSuccess) {
    let bonusCharm = (tempRecord.intelligenceInc + tempRecord.knowledgeInc) * 0.1;
    tempRecord.charmInc = bonusCharm;
    state.currentAttributes.charisma = Math.max(0, state.currentAttributes.charisma + bonusCharm);
  }
  
  updateDailyRecordTable();
  updateAllDisplays();
  recordHistory();
  updateNiShuiStatusDisplay();
  applyNiShuiXingChuan();
  
  // 关闭模态框
  bootstrap.Modal.getInstance(document.getElementById('editDailyRecordModal')).hide();
};

    window.deleteDailyRecord = function(index) {
      if(confirm('确定要删除这条记录吗？')) {
        const record = state.dailyRecords[index];
        // 从当前状态中减去该记录的所有属性值
        state.currentAttributes.intelligence -= record.intelligenceInc;
        state.currentAttributes.knowledge -= record.knowledgeInc;
        state.currentAttributes.experience -= record.experienceInc;
        state.currentAttributes.willpower = Math.max(0, state.currentAttributes.willpower - record.willpowerInc);
        state.currentAttributes.stamina -= record.staminaInc;
        state.currentAttributes.charisma = Math.max(0, state.currentAttributes.charisma - record.charmInc);
        
        state.dailyRecords.splice(index, 1);
        
        // 重新计算删除记录后的意志值变化
        const lastRecord = state.dailyRecords[state.dailyRecords.length - 1];
        if (lastRecord) {
          const hasActivity = lastRecord.studyHours > 0 || lastRecord.readingPages > 0 || lastRecord.paintingHours > 0;
          let willpowerChange = 0;
          
          if (hasActivity) {
            let consecutiveDays = calculateConsecutiveDailyRecord('all', lastRecord.recordDate);
            if (consecutiveDays >= 30) willpowerChange = 3;
            else if (consecutiveDays >= 7) willpowerChange = 2;
            else if (consecutiveDays >= 3) willpowerChange = 1;
          } else {
            let slackDays = calculateConsecutiveSlackDailyRecord('all', lastRecord.recordDate);
            if (slackDays >= 7) willpowerChange = -3;
            else if (slackDays >= 3) willpowerChange = -2;
            else willpowerChange = -1;
          }
          
          state.currentAttributes.willpower = Math.max(0, state.currentAttributes.willpower + willpowerChange);
        }
        
        updateDailyRecordTable();
        updateAllDisplays();
        recordHistory();
        updateNiShuiStatusDisplay();
        applyNiShuiXingChuan();
      }
    };

    function recalculateAttributes() {
      // 重置属性到基础值
      state.currentAttributes.willpower = state.baseAttributes.willpower;
      state.currentAttributes.stamina = state.baseAttributes.stamina;
      
      // 遍历所有打卡记录重新计算属性
      for(let record of state.checkinRecords) {
        if(!record.special && record.status === "失败") {
          // 晨曦之约废退法则
          const sleepHour = parseInt(record.sleepTime.split(':')[0]);
          const sleepMinute = parseInt(record.sleepTime.split(':')[1]);
          const validSleepTime = (sleepHour >= 18 && sleepHour <= 23) || (sleepHour === 0 && sleepMinute === 0);
          if(!validSleepTime) {
            state.currentAttributes.willpower -= 1;
          }
          if(!(record.wakeTime <= "08:30")) {
            state.currentAttributes.stamina -= 0.5;
          }
          
          // 意志废退法则
          let slackDays = calculateConsecutiveCheckinSlack();
          let penaltyWill = (slackDays >= 7) ? 3 : (slackDays >= 3 ? 2 : 1);
          state.currentAttributes.willpower -= penaltyWill;
        } else if(record.status === "成功") {
          let c = calculateConsecutiveCheckin();
          let bonusWill = 0, bonusStamina = 0;
          
          // 晨曦之约行舟法则
          if(c >= 30) { 
            bonusWill = 2; 
            bonusStamina = 1; 
          } else if(c >= 7) { 
            bonusWill = 1; 
            bonusStamina = 0.5; 
          } else if(c >= 3) { 
            bonusWill = 0.5; 
            bonusStamina = 0.2; 
          }
          
          // 意志行舟法则
          if(c >= 30) {
            bonusWill += 3;
          } else if(c >= 7) {
            bonusWill += 2;
          } else if(c >= 3) {
            bonusWill += 1;
          }
          
          state.currentAttributes.willpower += bonusWill;
          state.currentAttributes.stamina += bonusStamina;
          
          // 检查是否有特殊奖励（23:30前睡且8:00前起）
          if(record.sleepTime < "23:30" && record.wakeTime < "08:00") {
            state.currentAttributes.willpower += 0.5;
          }
        }
      }
    }

    /**************** 逆水行舟·晨曦之约（打卡） ****************/
    function calculateConsecutiveCheckinSlack() {
  let records = state.checkinRecords.slice().sort((a, b) => new Date(a.wakeDate) - new Date(b.wakeDate));
  let consecutive = 0;
  if(records.length === 0) return 0;
  
  let targetDate = new Date(records[records.length - 1].wakeDate);
  let targetRecord = records[records.length - 1];
  
  if(targetRecord.status !== "失败") return 0;
  
  consecutive = 1;
  let lastDate = targetDate;
  
  let targetIndex = records.findIndex(r => r.wakeDate === targetRecord.wakeDate);
  
  for (let i = targetIndex - 1; i >= 0; i--) {
    let currentDate = new Date(records[i].wakeDate);
    let diffDays = (lastDate - currentDate) / (1000 * 60 * 60 * 24);
    if(diffDays === 1 && records[i].status === "失败") {
      consecutive++;
      lastDate = currentDate;
    } else {
      break;
    }
  }
  return consecutive;
}

document.getElementById('addCheckin').addEventListener('click', function() {
      const sleepDate = document.getElementById('sleepDate').value;
      const wakeDate = document.getElementById('wakeDate').value;
      const sleepTime = document.getElementById('sleepTime').value;
      const wakeTime = document.getElementById('wakeTime').value;
      const special = document.getElementById('specialCheckin').checked;
      let status = "失败";
      let onTime = false;
      if(special) {
        status = "成功";
        onTime = true;
      } else {
        // 修改睡觉时间判断逻辑：前一天18:00-24:00为有效时间
        const sleepHour = parseInt(sleepTime.split(':')[0]);
        const sleepMinute = parseInt(sleepTime.split(':')[1]);
        const validSleepTime = (sleepHour >= 18 && sleepHour <= 23) || (sleepHour === 0 && sleepMinute === 0);
        const validWakeTime = wakeTime <= "08:30";
        
        if(sleepTime && wakeTime && validSleepTime && validWakeTime) {
          status = "成功";
          onTime = true;
        } else {
          status = "失败";
          onTime = false;
        }
      }
      const record = { sleepDate, wakeDate, sleepTime, wakeTime, status, special };
      state.checkinRecords.push(record);
      
      // 根据三种状态判断奖惩机制
      if(!onTime) {
        // 状态1：懈怠，执行废退法则
        const sleepHour = parseInt(sleepTime.split(':')[0]);
        const sleepMinute = parseInt(sleepTime.split(':')[1]);
        const validSleepTime = (sleepHour >= 18 && sleepHour <= 23) || (sleepHour === 0 && sleepMinute === 0);
        const validWakeTime = wakeTime <= "08:30";
        
        if(!validSleepTime) {
          state.currentAttributes.willpower -= 1;
        }
        if(!validWakeTime) {
          state.currentAttributes.stamina -= 0.5;
        }
        
        // 计算逆水行舟·意志法则
        let slackDays = calculateConsecutiveCheckinSlack();
        let penaltyWill = (slackDays >= 7) ? 3 : (slackDays >= 3 ? 2 : 1);
        state.currentAttributes.willpower -= penaltyWill;
      } else {
        // 状态2和3：执行计划或达到标准
        let c = calculateConsecutiveCheckin();
        let bonusWill = 0, bonusStamina = 0;
        
        // 状态3：连续执行达到标准，执行行舟法则
        if(c >= 30) { bonusWill = 3; bonusStamina = 1; }
        else if(c >= 7) { bonusWill = 2; bonusStamina = 0.5; }
        else if(c >= 3) { bonusWill = 1; bonusStamina = 0.2; }
        
        state.currentAttributes.willpower += bonusWill;
        state.currentAttributes.stamina += bonusStamina;
        
        // 检查是否有特殊奖励（23:30前睡且8:00前起）
        if(sleepTime < "23:30" && wakeTime < "08:00") {
          state.currentAttributes.willpower += 0.5;
        }
      }
      
      // 更新所有显示
      updateAllDisplays();
      updateNiShuiStatusDisplay();
      updateNiShuiStatusCheckin();
      applyNiShuiXingChuan();
      
      // 清空输入框
      document.getElementById('sleepDate').value = '';
      document.getElementById('wakeDate').value = '';
      document.getElementById('sleepTime').value = '';
      document.getElementById('wakeTime').value = '';
      document.getElementById('specialCheckin').checked = false;
      applyNiShuiXingChuan();
    });

    window.editCheckin = function(index) {
      const record = state.checkinRecords[index];
      // 创建模态框
      const modalHtml = `
        <div class="modal fade" id="editCheckinModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">编辑晨曦之约记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <form id="editCheckinForm">
                  <div class="mb-3">
                    <label class="form-label">入睡日期</label>
                    <input type="date" class="form-control" id="editSleepDate" value="${record.sleepDate}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">起床日期</label>
                    <input type="date" class="form-control" id="editWakeDate" value="${record.wakeDate}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">睡觉时间</label>
                    <input type="time" class="form-control" id="editSleepTime" value="${record.sleepTime}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">起床时间</label>
                    <input type="time" class="form-control" id="editWakeTime" value="${record.wakeTime}">
                  </div>
                  <div class="form-check mb-3">
                    <input type="checkbox" class="form-check-input" id="editSpecialCheckin" ${record.special ? 'checked' : ''}>
                    <label class="form-check-label">特殊情况</label>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCheckin(${index})">保存</button>
              </div>
            </div>
          </div>
        </div>
      `;
      
      // 添加模态框到body
      document.body.insertAdjacentHTML('beforeend', modalHtml);
      
      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('editCheckinModal'));
      modal.show();
      
      // 模态框关闭时移除
      document.getElementById('editCheckinModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
      });
    };

    window.saveCheckin = function(index) {
      const record = state.checkinRecords[index];
      const newSleepDate = document.getElementById('editSleepDate').value;
      const newWakeDate = document.getElementById('editWakeDate').value;
      const newSleepTime = document.getElementById('editSleepTime').value;
      const newWakeTime = document.getElementById('editWakeTime').value;
      const newSpecial = document.getElementById('editSpecialCheckin').checked;
      
      // 更新记录
      record.sleepDate = newSleepDate;
      record.wakeDate = newWakeDate;
      record.sleepTime = newSleepTime;
      record.wakeTime = newWakeTime;
      record.special = newSpecial;
      record.status = (record.special || (newSleepTime < "24:00" && newWakeTime < "08:30")) ? "成功" : "失败";
      
      // 重新计算所有属性
      recalculateAttributes();
      
      updateCheckinTable();
      updateAllDisplays();
      recordHistory();
      updateNiShuiStatusDisplay();
      updateNiShuiStatusCheckin();
      applyNiShuiXingChuan();
      
      // 关闭模态框
      bootstrap.Modal.getInstance(document.getElementById('editCheckinModal')).hide();
    };

    window.deleteCheckin = function(index) {
      const record = state.checkinRecords[index];
      if(confirm("确定要删除这条打卡记录吗？")) {
        // 先恢复属性变化
        if(!record.special && record.status === "失败") {
          const sleepHour = parseInt(record.sleepTime.split(':')[0]);
          const validSleepTime = (sleepHour >= 18 && sleepHour <= 23) || (sleepHour === 0 && record.sleepTime === "00:00");
          if(!validSleepTime) {
            state.currentAttributes.willpower += 1;
          }
          if(!(record.wakeTime <= "08:30")) {
            state.currentAttributes.stamina += 0.5;
          }
          
          // 恢复逆水行舟·意志懈怠惩罚
          let slackDays = calculateConsecutiveCheckinSlack();
          let penaltyWill = (slackDays >= 7) ? 3 : (slackDays >= 3 ? 2 : 1);
          state.currentAttributes.willpower += penaltyWill;
        } else if(record.status === "成功") {
          let c = calculateConsecutiveCheckin();
          let bonusWill = 0, bonusStamina = 0;
          if(c >= 30) { bonusWill = 2; bonusStamina = 1; }
          else if(c >= 7) { bonusWill = 1; bonusStamina = 0.5; }
          else if(c >= 3) { bonusWill = 0.5; bonusStamina = 0.2; }
          if(record.sleepTime < "23:30" && record.wakeTime < "08:00") { bonusWill += 0.5; }
          state.currentAttributes.willpower -= bonusWill;
          state.currentAttributes.stamina -= bonusStamina;
        }
        
        // 删除记录
        state.checkinRecords.splice(index, 1);
        
        // 更新显示
        updateCheckinTable();
        updateAllDisplays();
        updateTitlesTable();
        updateNiShuiStatusDisplay();
        updateNiShuiStatusCheckin();
        updateTotalRecords();
        recordHistory();
        applyNiShuiXingChuan();
      }
    };

    /**************** 每日个人总结 ****************/
    document.getElementById('addSummary').addEventListener('click', function() {
      const summaryDate = document.getElementById('summaryDate').value;
      const summaryContent = document.getElementById('summaryContent').value;
      const record = { summaryDate, summaryContent };
      state.summaries.push(record);
      updateSummaryTable();
      updateTotalRecords();
      applyNiShuiXingChuan();
    });

    window.editSummary = function(index) {
      const record = state.summaries[index];
      // 创建模态框
      const modalHtml = `
        <div class="modal fade" id="editSummaryModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">编辑每日总结</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <form id="editSummaryForm">
                  <div class="mb-3">
                    <label class="form-label">日期</label>
                    <input type="date" class="form-control" id="editSummaryDate" value="${record.summaryDate}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">总结内容</label>
                    <textarea class="form-control" id="editSummaryContent" rows="5">${record.summaryContent}</textarea>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveSummary(${index})">保存</button>
              </div>
            </div>
          </div>
        </div>
      `;
      
      // 添加模态框到body
      document.body.insertAdjacentHTML('beforeend', modalHtml);
      
      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('editSummaryModal'));
      modal.show();
      
      // 模态框关闭时移除
      document.getElementById('editSummaryModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
      });
    };

    window.saveSummary = function(index) {
      const newDate = document.getElementById('editSummaryDate').value;
      const newContent = document.getElementById('editSummaryContent').value;
      
      if(newDate && newContent) {
        state.summaries[index].summaryDate = newDate;
        state.summaries[index].summaryContent = newContent;
        
        updateSummaryTable();
        updateTotalRecords();
        applyNiShuiXingChuan();
      }
      
      // 关闭模态框
      bootstrap.Modal.getInstance(document.getElementById('editSummaryModal')).hide();
    };

    window.deleteSummary = function(index) {
      state.summaries.splice(index, 1);
      updateSummaryTable();
      updateTotalRecords();
      applyNiShuiXingChuan();
    };

    /**************** 任务系统 ****************/
    document.getElementById('addTask').addEventListener('click', () => {
      const taskName = document.getElementById('taskName').value;
      const taskDesc = document.getElementById('taskDesc').value;
      const taskType = document.getElementById('taskType').value;
      const taskCycle = document.getElementById('taskCycle').value;
      const taskTargetType = document.getElementById('taskTargetType').value;
      const taskTarget = document.getElementById('taskTarget').value;
      const taskDeadline = document.getElementById('taskDeadline').value;

      // 收集奖励信息
      const rewards = [];
      document.querySelectorAll('.reward-item').forEach(item => {
        const type = item.querySelector('.reward-type').value;
        const value = parseFloat(item.querySelector('.reward-value').value);
        if (type && !isNaN(value)) {
          rewards.push({ type, value });
        }
      });

      // 收集惩罚信息
      const penalties = [];
      document.querySelectorAll('.penalty-item').forEach(item => {
        const type = item.querySelector('.penalty-type').value;
        const value = parseFloat(item.querySelector('.penalty-value').value);
        if (type && !isNaN(value)) {
          penalties.push({ type, value });
        }
      });

      const task = {
        name: taskName,
        description: taskDesc,
        type: taskType,
        cycle: taskCycle,
        targetType: taskTargetType,
        target: parseFloat(taskTarget),
        deadline: taskDeadline,
        rewards,
        penalties,
        current: 0
      };

      state.tasks.push(task);
      updateTaskTables();
      saveState();

      // 重置表单
      document.getElementById('taskForm').reset();
    });

    // 添加奖励项的处理
    document.getElementById('addReward').addEventListener('click', function() {
      const rewardsList = document.getElementById('rewardsList');
      const newReward = document.createElement('div');
      newReward.className = 'reward-item mb-2';
      newReward.innerHTML = `
        <div class="row">
          <div class="col-md-5">
            <select class="form-select reward-type">
              <option value="智力">智力</option>
              <option value="知识">知识</option>
              <option value="体力">体力</option>
              <option value="意志">意志</option>
              <option value="魅力">魅力</option>
              <option value="幻构师经验">幻构师经验</option>
            </select>
          </div>
          <div class="col-md-5">
            <input type="number" class="form-control reward-value" min="0" placeholder="输入奖励数值">
          </div>
          <div class="col-md-2">
            <button type="button" class="btn btn-danger btn-sm remove-reward">删除</button>
          </div>
        </div>
      `;
      rewardsList.appendChild(newReward);

      // 为新添加的删除按钮绑定事件
      newReward.querySelector('.remove-reward').addEventListener('click', function() {
        newReward.remove();
      });
    });

    // 添加惩罚按钮事件
    document.getElementById('addPenalty').addEventListener('click', () => {
      const penaltiesList = document.getElementById('penaltiesList');
      const newPenaltyItem = document.querySelector('.penalty-item').cloneNode(true);
      newPenaltyItem.querySelector('.penalty-value').value = '';
      penaltiesList.appendChild(newPenaltyItem);

      // 为新添加的删除按钮绑定事件
      newPenaltyItem.querySelector('.remove-penalty').addEventListener('click', function() {
        this.closest('.penalty-item').remove();
      });
    });

    // 初始化删除惩罚按钮事件
    document.querySelectorAll('.remove-penalty').forEach(button => {
      button.addEventListener('click', function() {
        this.closest('.penalty-item').remove();
      });
    });

    // 为初始的删除按钮绑定事件
    document.querySelector('.remove-reward').addEventListener('click', function() {
      if (document.querySelectorAll('.reward-item').length > 1) {
        this.closest('.reward-item').remove();
      }
    });

    function calculateRemainingDays(deadline) {
      if (!deadline) return null;
      const today = new Date();
      const deadlineDate = new Date(deadline);
      const diffTime = deadlineDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays > 0 ? diffDays : 0;
    }

    function updateTaskTables() {
      const incompleteTbody = document.querySelector('#incompleteTasksTable tbody');
      const completedTbody = document.querySelector('#completedTasksTable tbody');
      incompleteTbody.innerHTML = '';
      completedTbody.innerHTML = '';

      state.tasks.forEach(task => {
        const progress = calculateTaskProgress(task);
        const remainingDays = calculateRemainingDays(task.deadline);
        const row = `
          <tr>
            <td>${task.name}</td>
            <td>${task.description}</td>
            <td>${task.type}</td>
            <td>${task.cycle}</td>
            <td>
              <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: ${progress}%" aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100">
                  ${progress}%
                </div>
              </div>
            </td>
            <td>${remainingDays !== null ? `${remainingDays}天` : '无限期'}</td>
            <td>${formatRewardsAndPenalties(task)}</td>
            <td>
              <button class="btn btn-sm btn-danger delete-task">删除</button>
            </td>
          </tr>
        `;
        
        if (progress >= 100) {
          completedTbody.insertAdjacentHTML('beforeend', row);
        } else {
          incompleteTbody.insertAdjacentHTML('beforeend', row);
        }
      });
    }

    function formatRewardsAndPenalties(task) {
      let result = '奖励：';
      task.rewards.forEach(reward => {
        result += `${reward.type}+${reward.value} `;
      });
      if (task.penalties && task.penalties.length > 0) {
        result += '<br>惩罚：';
        task.penalties.forEach(penalty => {
          result += `${penalty.type}-${penalty.value} `;
        });
      }
      return result;
    }

    window.editTask = function(index) {
      const task = state.tasks[index];
      const newName = prompt("编辑任务名称", task.taskName);
      const newDesc = prompt("编辑任务描述", task.taskDesc);
      if(newName && newDesc) {
        task.taskName = newName;
        task.taskDesc = newDesc;
        updateTaskTables();
        applyNiShuiXingChuan();
      }
    };

    window.deleteTask = function(index) {
      state.tasks.splice(index, 1);
      updateTaskTables();
      applyNiShuiXingChuan();
    };

    window.completeTask = function(index) {
      const task = state.tasks[index];
      task.status = "已完成";
      
      // 根据奖励类型分别增加对应属性
      task.rewards.forEach(reward => {
        switch(reward.type) {
          case "智力":
            state.currentAttributes.intelligence += reward.value;
            break;
          case "知识":
            state.currentAttributes.knowledge += reward.value;
            break;
          case "体力":
            state.currentAttributes.stamina += reward.value;
            break;
          case "意志":
            state.currentAttributes.willpower += reward.value;
            break;
          case "魅力":
            state.currentAttributes.charisma += reward.value;
            break;
          case "幻构师经验":
            state.currentAttributes.experience += reward.value;
            break;
        }
      });
      
      updateTaskTables();
      updateStatusDisplay();
      recordHistory();
      updateHuanGoushiLevels();
      updateTruthPathLevels();
      applyNiShuiXingChuan();
    };

    /**************** 更新“当前状态” ****************/
    function updateStatusDisplay() {
      document.getElementById('attrIntelligence').innerText = "智力: " + state.currentAttributes.intelligence.toFixed(2);
      document.getElementById('attrKnowledge').innerText = "知识: " + state.currentAttributes.knowledge.toFixed(2);
      document.getElementById('attrStamina').innerText = "体力: " + state.currentAttributes.stamina.toFixed(2);
      document.getElementById('attrWillpower').innerText = "意志: " + state.currentAttributes.willpower.toFixed(2);
      
      // 计算魅力值变化
      let totalOtherAttributes = state.currentAttributes.intelligence + 
                                state.currentAttributes.knowledge + 
                                state.currentAttributes.stamina + 
                                state.currentAttributes.willpower;
      state.currentAttributes.charisma = parseFloat((totalOtherAttributes * 0.1).toFixed(2));
      
      document.getElementById('attrCharisma').innerText = "魅力: " + state.currentAttributes.charisma.toFixed(2);
      document.getElementById('attrExperience').innerText = "幻构师经验: " + state.currentAttributes.experience.toFixed(2);
    }

    // 在属性变化时更新所有相关表格和显示
    function updateAllDisplays() {
      updateStatusDisplay();
      updateHuanGoushiTable();
      updateTruthPathTable();
      updateTitlesTable();
      updateNiShuiStatusDisplay();
      updateNiShuiStatusCheckin();
      recordHistory();
      updateStageCharts();
      updatePlanProgress();
      updateTotalAttributes();
    }

    function updateTotalAttributes() {
      // 直接从当前状态获取属性值
      const totalIntelligence = state.currentAttributes.intelligence;
      const totalKnowledge = state.currentAttributes.knowledge;
      const totalStamina = state.currentAttributes.stamina;
      const totalWillpower = state.currentAttributes.willpower;
      const totalCharisma = state.currentAttributes.charisma;
      const totalExperience = state.currentAttributes.experience - 1180; // 减去初始值

      // 更新显示
      document.getElementById('totalIntelligence').innerHTML = `<i class="fas fa-brain me-1"></i>智力: +${totalIntelligence.toFixed(1)}`;
      document.getElementById('totalKnowledge').innerHTML = `<i class="fas fa-book-open me-1"></i>知识: +${totalKnowledge.toFixed(1)}`;
      document.getElementById('totalStamina').innerHTML = `<i class="fas fa-heartbeat me-1"></i>体力: +${totalStamina.toFixed(1)}`;
      document.getElementById('totalWillpower').innerHTML = `<i class="fas fa-fist-raised me-1"></i>意志: +${totalWillpower.toFixed(1)}`;
      document.getElementById('totalCharisma').innerHTML = `<i class="fas fa-star me-1"></i>魅力: +${totalCharisma.toFixed(1)}`;
      document.getElementById('totalExperience').innerHTML = `<i class="fas fa-palette me-1"></i>幻构师经验: +${totalExperience.toFixed(1)}`;
    }

    /**************** 保存与重置 ****************/
    document.getElementById('saveButton').addEventListener('click', function() {
      localStorage.setItem('gameState', JSON.stringify(state));
      alert("数据已保存");
    });
    
    // 页面加载时从localStorage恢复数据
    window.addEventListener('load', function() {
      const savedState = localStorage.getItem('gameState');
      if (savedState) {
        try {
          const parsedState = JSON.parse(savedState);
          // 恢复所有状态数据
          state.startDate = new Date(parsedState.startDate);
          state.currentAttributes = parsedState.currentAttributes;
          state.dailyRecords = parsedState.dailyRecords;
          state.checkinRecords = parsedState.checkinRecords;
          state.summaries = parsedState.summaries;
          state.tasks = parsedState.tasks;
          state.history = parsedState.history;
          
          // 更新所有显示
          updateAllDisplays();
          updateDailyRecordTable();
          updateCheckinTable();
          updateSummaryTable();
          updateTotalRecords();
          updateTaskTables();
          applyNiShuiXingChuan();
          console.log('成功从本地存储恢复数据');
        } catch (error) {
          console.error('恢复数据时出错:', error);
        }
      }
    });

    document.getElementById('resetButton').addEventListener('click', function() {
      if(confirm("确定重置所有数据？")) {
        localStorage.clear();
        location.reload();
      }
    });

    /**************** 模态框显示 ****************/
    document.getElementById('statusInfoButton').addEventListener('click', function() {
      const myModal = new bootstrap.Modal(document.getElementById('statusInfoModal'));
      myModal.show();
    });

    /**************** 历史记录切换按钮 ****************/
    document.getElementById('showDailyHistory').addEventListener('click', function() {
      dailyHistoryMode = !dailyHistoryMode;
      dailyRecordPage = 1;
      updateDailyRecordTable();
      applyNiShuiXingChuan();
    });
    document.getElementById('showCheckinHistory').addEventListener('click', function() {
      checkinHistoryMode = !checkinHistoryMode;
      checkinPage = 1;
      updateCheckinTable();
      applyNiShuiXingChuan();
    });
    document.getElementById('showSummaryHistory').addEventListener('click', function() {
      summaryHistoryMode = !summaryHistoryMode;
      summaryPage = 1;
      updateSummaryTable();
      applyNiShuiXingChuan();
    });
    document.getElementById('showTotalHistory').addEventListener('click', function() {
      totalHistoryMode = !totalHistoryMode;
      totalPage = 1;
      updateTotalRecords();
      applyNiShuiXingChuan();
    });

    /**************** 晨曦之约编辑和删除功能 ****************/
    window.editCheckin = function(index) {
      const record = state.checkinRecords[index];
      // 创建模态框
      const modalHtml = `
        <div class="modal fade" id="editCheckinModal" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">编辑晨曦之约记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <form id="editCheckinForm">
                  <div class="mb-3">
                    <label class="form-label">入睡日期</label>
                    <input type="date" class="form-control" id="editSleepDate" value="${record.sleepDate}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">起床日期</label>
                    <input type="date" class="form-control" id="editWakeDate" value="${record.wakeDate}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">睡觉时间</label>
                    <input type="time" class="form-control" id="editSleepTime" value="${record.sleepTime}">
                  </div>
                  <div class="mb-3">
                    <label class="form-label">起床时间</label>
                    <input type="time" class="form-control" id="editWakeTime" value="${record.wakeTime}">
                  </div>
                  <div class="mb-3">
                    <div class="form-check">
                      <input type="checkbox" class="form-check-input" id="editSpecial" ${record.special ? 'checked' : ''}>
                      <label class="form-check-label">特殊情况</label>
                    </div>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCheckin(${index})">保存</button>
              </div>
            </div>
          </div>
        </div>
      `;
      
      // 添加模态框到body
      document.body.insertAdjacentHTML('beforeend', modalHtml);
      
      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('editCheckinModal'));
      modal.show();
      
      // 模态框关闭时移除
      document.getElementById('editCheckinModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
      });
    };

    window.saveCheckin = function(index) {
      const record = state.checkinRecords[index];
      const sleepDate = document.getElementById('editSleepDate').value;
      const wakeDate = document.getElementById('editWakeDate').value;
      const sleepTime = document.getElementById('editSleepTime').value;
      const wakeTime = document.getElementById('editWakeTime').value;
      const special = document.getElementById('editSpecialCheckin').checked;
      
      let status = "失败";
      let onTime = false;
      if(special) {
        status = "成功";
        onTime = true;
      } else {
        const sleepHour = parseInt(sleepTime.split(':')[0]);
        const sleepMinute = parseInt(sleepTime.split(':')[1]);
        const validSleepTime = (sleepHour >= 18 && sleepHour <= 23) || (sleepHour === 0 && sleepMinute === 0);
        const validWakeTime = wakeTime <= "08:30";
        
        if(sleepTime && wakeTime && validSleepTime && validWakeTime) {
          status = "成功";
          onTime = true;
        } else {
          status = "失败";
          onTime = false;
        }
      }
      
      record.sleepDate = sleepDate;
      record.wakeDate = wakeDate;
      record.sleepTime = sleepTime;
      record.wakeTime = wakeTime;
      record.status = status;
      record.special = special;
      
      // 重新计算属性
      state.currentAttributes.willpower = state.baseAttributes.willpower;
      state.currentAttributes.stamina = state.baseAttributes.stamina;
      
      // 遍历所有打卡记录重新计算属性
      for(let r of state.checkinRecords) {
        if(!r.special && r.status === "失败") {
          if(!(r.sleepTime < "24:00")) {
            state.currentAttributes.willpower -= 1;
          }
          if(!(r.wakeTime < "08:30")) {
            state.currentAttributes.stamina -= 0.5;
          }
          
          // 计算逆水行舟·意志法则的废退部分
          let slackDays = calculateConsecutiveCheckinSlack();
          let penaltyWill = (slackDays >= 7) ? 3 : (slackDays >= 3 ? 2 : 1);
          state.currentAttributes.willpower -= penaltyWill;
        } else if(r.status === "成功") {
          // 计算晨曦之约的基础奖励
          let c = calculateConsecutiveCheckin(r.wakeDate);
          let bonusWill = 0, bonusStamina = 0;
          if(c >= 30) { bonusWill = 2; bonusStamina = 1; }
          else if(c >= 7) { bonusWill = 1; bonusStamina = 0.5; }
          else if(c >= 3) { bonusWill = 0.5; bonusStamina = 0.2; }
          if(r.sleepTime < "23:30" && r.wakeTime < "08:00") { bonusWill += 0.5; }
          
          // 计算逆水行舟·意志法则的行舟部分
          if(c >= 30) { bonusWill += 3; }
          else if(c >= 7) { bonusWill += 2; }
          else if(c >= 3) { bonusWill += 1; }
          
          state.currentAttributes.willpower += bonusWill;
          state.currentAttributes.stamina += bonusStamina;
        }
      }
      
      updateDailyRecordTable();
      updateAllDisplays();
      recordHistory();
      updateNiShuiStatusDisplay();
      applyNiShuiXingChuan();
      
      // 关闭模态框
      bootstrap.Modal.getInstance(document.getElementById('editCheckinModal')).hide();
    };

    window.deleteCheckin = function(index) {
      const record = state.checkinRecords[index];
      if(confirm("确定要删除这条打卡记录吗？")) {
        // 先恢复属性变化
        if(!record.special && record.status === "失败") {
          const sleepHour = parseInt(record.sleepTime.split(':')[0]);
          const validSleepTime = (sleepHour >= 18 && sleepHour <= 23) || (sleepHour === 0 && record.sleepTime === "00:00");
          if(!validSleepTime) {
            state.currentAttributes.willpower += 1;
          }
          if(!(record.wakeTime <= "08:30")) {
            state.currentAttributes.stamina += 0.5;
          }
          
          // 恢复逆水行舟·意志懈怠惩罚
          let slackDays = calculateConsecutiveCheckinSlack();
          let penaltyWill = (slackDays >= 7) ? 3 : (slackDays >= 3 ? 2 : 1);
          state.currentAttributes.willpower += penaltyWill;
        } else if(record.status === "成功") {
          let c = calculateConsecutiveCheckin();
          let bonusWill = 0, bonusStamina = 0;
          if(c >= 30) { bonusWill = 2; bonusStamina = 1; }
          else if(c >= 7) { bonusWill = 1; bonusStamina = 0.5; }
          else if(c >= 3) { bonusWill = 0.5; bonusStamina = 0.2; }
          if(record.sleepTime < "23:30" && record.wakeTime < "08:00") { bonusWill += 0.5; }
          state.currentAttributes.willpower -= bonusWill;
          state.currentAttributes.stamina -= bonusStamina;
        }
        
        // 删除记录
        state.checkinRecords.splice(index, 1);
        
        // 更新显示
        updateCheckinTable();
        updateAllDisplays();
        updateTitlesTable();
        updateNiShuiStatusDisplay();
        updateNiShuiStatusCheckin();
        updateTotalRecords();
        recordHistory();
        applyNiShuiXingChuan();
      }
    };

    /**************** 计划执行成功率计算 ****************/
    function getTotalDays(type) {
      if(type === 'checkin') {
        return state.checkinRecords.length;
      }
      return state.dailyRecords.length;
    }

    function getSuccessDays(type) {
      if(type === 'checkin') {
        return state.checkinRecords.filter(r => r.status === '成功').length;
      } else if(type === 'painting') {
        return state.dailyRecords.filter(r => r.paintingHours > 0).length;
      } else if(type === 'study') {
        return state.dailyRecords.filter(r => r.studyHours > 0 || r.readingPages > 0).length;
      }
      return 0;
    }

    function updatePlanProgress() {
      // 获取各计划的总天数和成功天数
      const huangoushiTotal = getTotalDays('painting');
      const huangoushiSuccess = getSuccessDays('painting');
      const truthPathTotal = getTotalDays('study');
      const truthPathSuccess = getSuccessDays('study');
      const checkinTotal = getTotalDays('checkin');
      const checkinSuccess = getSuccessDays('checkin');

      // 更新进度条
      updateProgressBar('huanGoushiProgress', huangoushiSuccess, huangoushiTotal);
      updateProgressBar('truthPathProgress', truthPathSuccess, truthPathTotal);
      updateProgressBar('checkinProgress', checkinSuccess, checkinTotal);

      // 更新天数显示
      document.getElementById('huanGoushiDays').innerHTML = `<i class="fas fa-paint-brush me-1"></i>幻构师计划: ${huangoushiSuccess}天`;
      document.getElementById('truthPathDays').innerHTML = `<i class="fas fa-scroll me-1"></i>真理之路计划: ${truthPathSuccess}天`;
      document.getElementById('checkinDays').innerHTML = `<i class="fas fa-sun me-1"></i>晨曦之约计划: ${checkinSuccess}天`;
    }

    function updateProgressBar(id, success, total) {
      const percentage = total > 0 ? Math.round((success / total) * 100) : 0;
      const progressBar = document.getElementById(id);
      progressBar.style.width = `${percentage}%`;
      progressBar.textContent = `${percentage}%`;
      progressBar.setAttribute('aria-valuenow', percentage);
    }

    /**************** 定时更新 ****************/
    setInterval(updateTime, 1000);
    updateTime();
    updateStatusDisplay();

    window.addEventListener('DOMContentLoaded', () => {
      initCharts();
      updateStageCharts();
      updatePlanProgress();
      updateBasicChart();
      updateExpChart();
    });
  </script>
</body>
</html>