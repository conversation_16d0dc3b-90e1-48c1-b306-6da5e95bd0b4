# LifeGame 累计增长属性统计功能说明

## 📊 功能概述

"累计增长属性统计"是LifeGame v2.1新增的强大分析工具，位于"阶段性总结"板块中。该功能可以详细分析指定时间段内各个渠道对属性增长的贡献，帮助用户深入了解自己的成长模式和各种活动的效果。

## ✨ 主要特性

### 🎯 全面的渠道分析
- **每日记录**：来自每日记录表单中的所有属性增长（包括基础活动和自定义属性）
- **每日任务**：来自每日任务完成奖励的属性增长
- **普通任务**：来自普通任务完成奖励和失败惩罚的属性净增长
- **晨曦之约**：来自晨曦之约的意志和体力增长

### 📈 完整的属性覆盖
- **基础属性**：智力、知识、阅识、体力、意志、魅力、幻构师经验
- **自定义属性**：所有已启用的自定义属性
- **智能过滤**：可按属性类型筛选显示

### ⏰ 灵活的时间范围
- **预设范围**：最近7天、30天、90天、一年
- **全部时间**：分析所有历史数据
- **自定义范围**：指定任意起止日期

### 📊 丰富的统计信息
- **统计概览**：时间范围、活跃天数、总增长值、平均日增长
- **详细表格**：各渠道对各属性的具体贡献值
- **百分比占比**：各渠道的贡献比例
- **正负值显示**：支持任务惩罚的负值显示

## 🚀 使用指南

### 访问统计功能
1. 切换到"阶段性总结"选项卡
2. 找到"📊 累计增长属性统计"区域
3. 点击"展开"按钮打开统计界面

### 设置统计参数

#### 时间范围选择
- **快速选择**：从下拉菜单选择预设的时间范围
- **自定义范围**：选择"自定义范围"后设置具体的起止日期

#### 属性过滤
- **全部属性**：显示所有基础属性和自定义属性
- **基础属性**：仅显示系统内置的7个基础属性
- **自定义属性**：仅显示用户创建的自定义属性

### 查看统计结果

#### 统计概览卡片
- **统计时间范围**：显示分析的具体日期区间和总天数
- **活跃天数**：有记录数据的实际天数
- **总增长值**：所有属性增长绝对值的总和
- **平均日增长**：总增长值除以活跃天数

#### 详细统计表格
- **渠道列**：显示四个主要的属性增长渠道
- **属性列**：显示各个属性的具体增长值
- **渠道总计**：每个渠道的总贡献值
- **占比**：各渠道在总增长中的百分比

#### 数值颜色编码
- **绿色**：正值（属性增长）
- **红色**：负值（属性减少，如任务惩罚）
- **灰色**：零值（无变化）

### 导出统计数据
1. 点击表格下方的"📊 导出统计数据"按钮
2. 系统会生成CSV格式的统计报告
3. 文件包含完整的统计数据和时间信息
4. 可用于进一步的数据分析或存档

## 📋 使用场景

### 1. 成长效果分析
**场景**：想了解最近一个月哪种活动对自己的成长贡献最大
**操作**：
- 设置时间范围为"最近30天"
- 查看各渠道的贡献占比
- 分析哪个渠道的总贡献最高

### 2. 习惯效果评估
**场景**：评估每日任务系统对属性提升的效果
**操作**：
- 设置合适的时间范围
- 重点关注"每日任务"行的数据
- 对比与其他渠道的贡献差异

### 3. 自定义属性追踪
**场景**：分析自定义属性的增长情况
**操作**：
- 属性过滤选择"自定义属性"
- 查看各个自定义属性的增长趋势
- 评估自定义属性设计的合理性

### 4. 阶段性回顾
**场景**：进行季度或年度的成长回顾
**操作**：
- 使用"自定义范围"设置具体的回顾期间
- 导出统计数据进行深度分析
- 制定下一阶段的改进计划

## 🎨 界面特色

### 一致的设计风格
- 采用与主界面相同的玻璃拟态效果
- 使用统一的主题色彩系统
- 保持现有界面的视觉连贯性

### 直观的数据展示
- 清晰的表格布局
- 直观的颜色编码
- 友好的数值格式化

### 响应式交互
- 实时的参数调整
- 即时的数据更新
- 流畅的展开收起动画

## ⚙️ 技术特性

### 高效的数据处理
- 智能的日期范围计算
- 高效的数据聚合算法
- 最小化的重复计算

### 完整的兼容性
- 完全支持自定义属性系统
- 兼容所有现有数据格式
- 自动处理数据缺失情况

### 安全的数据导出
- 标准的CSV格式
- 完整的数据完整性
- 友好的文件命名

## 📊 统计算法说明

### 渠道分类逻辑
1. **每日记录**：统计`dailyRecords`中所有`deltas`的属性增量
2. **每日任务**：统计`dailyTaskRecords`对应的任务奖励
3. **普通任务**：统计`tasks`中已完成/失败任务的奖励/惩罚
4. **晨曦之约**：统计`dawn.history`中的意志和体力增量

### 时间范围计算
- **相对范围**：基于当前日期向前推算
- **绝对范围**：使用用户指定的起止日期
- **全部时间**：扫描所有数据源获取最早日期

### 百分比计算
- 基于各渠道贡献的绝对值计算
- 避免负值对百分比的影响
- 确保总和为100%

## ⚠️ 注意事项

### 数据准确性
1. **时间一致性**：确保各数据源的日期格式一致
2. **属性匹配**：自定义属性需要正确配置才能统计
3. **任务状态**：只统计已完成或已失败的任务

### 性能考虑
1. **大数据量**：大量历史数据可能影响计算速度
2. **频繁刷新**：避免过于频繁的统计刷新
3. **导出限制**：超大数据集可能影响导出性能

### 使用建议
1. **合理范围**：选择合适的时间范围进行分析
2. **定期回顾**：建议定期进行统计分析
3. **数据备份**：重要的统计结果建议导出保存

## 🆕 版本信息

**功能版本**：v2.1.0  
**发布日期**：2025-07-02  
**兼容性**：完全兼容现有数据，支持自定义属性系统  
**依赖**：需要Chart.js库支持（已内置）

---

这个强大的统计功能将帮助您更好地理解自己的成长模式，优化时间分配，制定更有效的个人发展策略！
