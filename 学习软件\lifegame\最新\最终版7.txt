<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>人生游戏计划</title>
    <style>
        /* ===== 全局样式 ===== */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600&display=swap');

        :root {
            /* 主题色彩变量 - 默认暗夜模式 */
            --bg-primary: #f1f5f9;
            --bg-secondary: #e2e8f0;
            --bg-tertiary: #cbd5e1;
            --bg-glass: rgba(248, 250, 252, 0.8);
            --bg-glass-light: rgba(248, 250, 252, 0.6);
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --accent-blue: #3b82f6;
            --accent-purple: #8b5cf6;
            --accent-pink: #ec4899;
            --border-color: rgba(59, 130, 246, 0.2);
            --shadow-color: rgba(0, 0, 0, 0.1);
            --glow-color: rgba(59, 130, 246, 0.3);
        }

        /* 宁静自然主题 */
        [data-theme="nature"] {
            --bg-primary: #E9F5DB;
            --bg-secondary: #CFD7C7;
            --bg-tertiary: #A3B18A;
            --bg-glass: rgba(233, 245, 219, 0.8);
            --bg-glass-light: rgba(233, 245, 219, 0.6);
            --text-primary: #3A5A40;
            --text-secondary: #588157;
            --text-muted: #588157;
            --accent-blue: #588157;
            --accent-purple: #A3B18A;
            --accent-pink: #CFD7C7;
            --border-color: rgba(163, 177, 138, 0.3);
            --shadow-color: rgba(58, 90, 64, 0.1);
            --glow-color: rgba(88, 129, 87, 0.3);
        }

        /* 海洋微风主题 */
        [data-theme="ocean"] {
            --bg-primary: #F0F4F8;
            --bg-secondary: #B9D7EA;
            --bg-tertiary: #769FCD;
            --bg-glass: rgba(240, 244, 248, 0.8);
            --bg-glass-light: rgba(240, 244, 248, 0.6);
            --text-primary: #2C3E50;
            --text-secondary: #537DA6;
            --text-muted: #537DA6;
            --accent-blue: #537DA6;
            --accent-purple: #769FCD;
            --accent-pink: #B9D7EA;
            --border-color: rgba(118, 159, 205, 0.3);
            --shadow-color: rgba(44, 62, 80, 0.1);
            --glow-color: rgba(83, 125, 166, 0.3);
        }

        /* 暖沙暮色主题 */
        [data-theme="sunset"] {
            --bg-primary: #FFF5E4;
            --bg-secondary: #FFD1D1;
            --bg-tertiary: #FF9494;
            --bg-glass: rgba(255, 245, 228, 0.8);
            --bg-glass-light: rgba(255, 245, 228, 0.6);
            --text-primary: #6B2737;
            --text-secondary: #E14D2A;
            --text-muted: #E14D2A;
            --accent-blue: #E14D2A;
            --accent-purple: #FF9494;
            --accent-pink: #FFD1D1;
            --border-color: rgba(255, 148, 148, 0.3);
            --shadow-color: rgba(107, 39, 55, 0.1);
            --glow-color: rgba(225, 77, 42, 0.3);
        }

        /* 科技极简主题 */
        [data-theme="tech"] {
            --bg-primary: #F8F9FA;
            --bg-secondary: #E9ECEF;
            --bg-tertiary: #CED4DA;
            --bg-glass: rgba(248, 249, 250, 0.8);
            --bg-glass-light: rgba(248, 249, 250, 0.6);
            --text-primary: #212529;
            --text-secondary: #6C757D;
            --text-muted: #6C757D;
            --accent-blue: #6C757D;
            --accent-purple: #CED4DA;
            --accent-pink: #E9ECEF;
            --border-color: rgba(206, 212, 218, 0.3);
            --shadow-color: rgba(33, 37, 41, 0.1);
            --glow-color: rgba(108, 117, 125, 0.3);
        }

        /* 朝日之森主题 */
        [data-theme="forest"] {
            --bg-primary: #F9F7F0;
            --bg-secondary: #F4F2E8;
            --bg-tertiary: #EDE9DC;
            --bg-glass: rgba(249, 247, 240, 0.8);
            --bg-glass-light: rgba(249, 247, 240, 0.6);
            --text-primary: #3C3C3C;
            --text-secondary: #5B8C85;
            --text-muted: #7A9B94;
            --accent-blue: #5B8C85;
            --accent-purple: #A7D7C5;
            --accent-pink: #F4D35E;
            --border-color: rgba(91, 140, 133, 0.2);
            --shadow-color: rgba(60, 60, 60, 0.1);
            --glow-color: rgba(91, 140, 133, 0.3);
        }

        /* 月凉之海主题 */
        [data-theme="sea"] {
            --bg-primary: #EDF6F9;
            --bg-secondary: #E3F0F3;
            --bg-tertiary: #D6E8EC;
            --bg-glass: rgba(237, 246, 249, 0.8);
            --bg-glass-light: rgba(237, 246, 249, 0.6);
            --text-primary: #1D3557;
            --text-secondary: #006D77;
            --text-muted: #457B9D;
            --accent-blue: #006D77;
            --accent-purple: #83C5BE;
            --accent-pink: #E29578;
            --border-color: rgba(0, 109, 119, 0.2);
            --shadow-color: rgba(29, 53, 87, 0.1);
            --glow-color: rgba(0, 109, 119, 0.3);
        }

        /* 暮日之落主题 */
        [data-theme="sunset-warm"] {
            --bg-primary: #FDF6EC;
            --bg-secondary: #FBF0E0;
            --bg-tertiary: #F8E8D4;
            --bg-glass: rgba(253, 246, 236, 0.8);
            --bg-glass-light: rgba(253, 246, 236, 0.6);
            --text-primary: #333333;
            --text-secondary: #E76F51;
            --text-muted: #8B5A3C;
            --accent-blue: #E76F51;
            --accent-purple: #F4A261;
            --accent-pink: #2A9D8F;
            --border-color: rgba(231, 111, 81, 0.2);
            --shadow-color: rgba(51, 51, 51, 0.1);
            --glow-color: rgba(231, 111, 81, 0.3);
        }

        /* 活力橙蓝主题 */
        [data-theme="orange-blue"] {
            --bg-primary: #FFEEE5;
            --bg-secondary: #FFBFA3;
            --bg-tertiary: #FF6B35;
            --bg-glass: rgba(255, 238, 229, 0.8);
            --bg-glass-light: rgba(255, 238, 229, 0.6);
            --text-primary: #001C38;
            --text-secondary: #004E89;
            --text-muted: #004E89;
            --accent-blue: #004E89;
            --accent-purple: #FF6B35;
            --accent-pink: #FFBFA3;
            --border-color: rgba(255, 107, 53, 0.3);
            --shadow-color: rgba(0, 28, 56, 0.1);
            --glow-color: rgba(0, 78, 137, 0.3);
        }

        /* 莫兰迪紫主题 */
        [data-theme="morandi-purple"] {
            --bg-primary: #F5E6E8;
            --bg-secondary: #E2C4D4;
            --bg-tertiary: #B88C9E;
            --bg-glass: rgba(245, 230, 232, 0.8);
            --bg-glass-light: rgba(245, 230, 232, 0.6);
            --text-primary: #3A2E39;
            --text-secondary: #705861;
            --text-muted: #705861;
            --accent-blue: #705861;
            --accent-purple: #B88C9E;
            --accent-pink: #E2C4D4;
            --border-color: rgba(184, 140, 158, 0.3);
            --shadow-color: rgba(58, 46, 57, 0.1);
            --glow-color: rgba(112, 88, 97, 0.3);
        }

        /* 薄荷清新主题 */
        [data-theme="mint-fresh"] {
            --bg-primary: #E3F2EF;
            --bg-secondary: #B2DFDB;
            --bg-tertiary: #80CBC4;
            --bg-glass: rgba(227, 242, 239, 0.8);
            --bg-glass-light: rgba(227, 242, 239, 0.6);
            --text-primary: #00695C;
            --text-secondary: #4DB6AC;
            --text-muted: #4DB6AC;
            --accent-blue: #4DB6AC;
            --accent-purple: #80CBC4;
            --accent-pink: #B2DFDB;
            --border-color: rgba(128, 203, 196, 0.3);
            --shadow-color: rgba(0, 105, 92, 0.1);
            --glow-color: rgba(77, 182, 172, 0.3);
        }

        /* 暗夜模式主题 */
        [data-theme="dark-night"] {
            --bg-primary: #0F0F0F;
            --bg-secondary: #232D3F;
            --bg-tertiary: #2C4C6B;
            --bg-glass: rgba(15, 15, 15, 0.8);
            --bg-glass-light: rgba(15, 15, 15, 0.6);
            --text-primary: #D9D9D9;
            --text-secondary: #4A6670;
            --text-muted: #4A6670;
            --accent-blue: #4A6670;
            --accent-purple: #2C4C6B;
            --accent-pink: #232D3F;
            --border-color: rgba(44, 76, 107, 0.3);
            --shadow-color: rgba(0, 0, 0, 0.3);
            --glow-color: rgba(74, 102, 112, 0.3);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", "Arial", sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.7;
            overflow-x: hidden;
            font-size: 15px;
            font-weight: 500;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* 玻璃拟态效果 */
        .glass-effect {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px var(--shadow-color);
        }
        /* ===== 头部导航栏 ===== */
        header {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 20px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 20px var(--shadow-color);
        }

        header .left {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        header .left .datetime {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", sans-serif;
            font-size: 1.6em;
            font-weight: 700;
            color: var(--accent-blue);
        }

        header .left .days {
            font-size: 1.0em;
            color: var(--text-secondary);
            font-weight: 600;
        }

        header .right {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* 主题切换器样式 */
        .theme-selector {
            position: relative;
            margin-right: 16px;
        }

        .theme-toggle {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
        }

        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 8px 32px var(--shadow-color);
            min-width: 180px;
            z-index: 1000;
            display: none;
        }

        .theme-dropdown.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        .theme-option {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9em;
            color: var(--text-secondary);
        }

        .theme-option:last-child {
            border-bottom: none;
        }

        .theme-option:hover {
            background: var(--bg-glass-light);
            color: var(--accent-blue);
        }

        .theme-option.active {
            background: var(--accent-blue);
            color: white;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        header .right button {
            padding: 12px 24px;
            font-size: 1.0em;
            border: none;
            border-radius: 12px;
            background: var(--accent-blue);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        header .right button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        header .right button:hover {
            background: var(--accent-purple);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--glow-color);
        }

        header .right button:hover::before {
            left: 100%;
        }
        .container {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* ===== “当前状态” 样式 ===== */
        .status-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px var(--shadow-color);
            margin-bottom: 20px;
            position: relative;
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px var(--shadow-color);
        }
        .status-card h2 {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", sans-serif;
            font-size: 1.6em;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-card h2::before {
            content: '📊';
            font-size: 1.2em;
        }

        .status-card .question-mark {
            position: absolute;
            top: 24px;
            right: 24px;
            cursor: pointer;
            font-size: 1.3em;
            color: var(--accent-blue);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-glass-light);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .status-card .question-mark:hover {
            background: var(--accent-blue);
            color: white;
            box-shadow: 0 4px 15px var(--glow-color);
        }

        .status-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .status-box {
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .status-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-blue);
        }

        .status-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--shadow-color);
            border-color: var(--accent-blue);
        }

        .status-box h3 {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", sans-serif;
            margin-bottom: 16px;
            font-size: 1.2em;
            font-weight: 700;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }

        .status-box p {
            font-size: 1.0em;
            margin: 8px 0;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 500;
        }

        .status-box p span {
            font-weight: 700;
            color: var(--accent-blue);
        }

        /* ===== 选项卡导航 ===== */
        .tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            padding: 8px;
            background: var(--bg-glass-light);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            overflow-x: auto;
        }

        .tab {
            padding: 14px 24px;
            cursor: pointer;
            background: transparent;
            border: none;
            border-radius: 12px;
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 1.0em;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .tab::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--accent-blue);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .tab.active {
            background: var(--accent-blue);
            color: white;
            box-shadow: 0 4px 15px var(--glow-color);
            transform: translateY(-1px);
        }

        .tab.active::before {
            width: 80%;
        }

        .tab:hover:not(.active) {
            background: var(--bg-glass);
            color: var(--text-primary);
            transform: translateY(-1px);
        }

        .tab-content {
            display: none;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px var(--shadow-color);
            line-height: 1.6;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* ===== 统一排序控件样式 ===== */
        .sort-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            text-align: center;
            justify-content: center;
        }

        .sort-controls label {
            font-size: 0.9em;
            color: var(--text-color);
            font-weight: 500;
        }

        .sort-select {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background: var(--bg-color);
            color: var(--text-color);
            font-size: 0.9em;
            transition: all 0.2s ease;
            min-width: 180px;
        }

        .sort-select:hover {
            border-color: var(--accent-blue);
        }

        .sort-select:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        /* ===== 阶段性总结样式 ===== */
        .charts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .chart-section {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px var(--shadow-color);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .chart-header h3 {
            margin: 0;
            color: var(--text-color);
            font-size: 1.2em;
        }

        .chart-toggle-group {
            display: flex;
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 2px;
            border: 1px solid var(--border-color);
        }

        .chart-toggle-btn {
            padding: 6px 12px;
            border: none;
            background: transparent;
            color: var(--text-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85em;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .chart-toggle-btn:hover {
            background: var(--bg-hover);
        }

        .chart-toggle-btn.active {
            background: var(--accent-blue);
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .chart-section {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px var(--shadow-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .chart-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
        }

        .chart-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px var(--shadow-color);
        }

        .chart-section h3 {
            font-family: "Inter", sans-serif;
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-section h3::before {
            content: '📈';
            font-size: 1.1em;
        }

        .chart-wrapper {
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
            background: var(--bg-glass-light);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid var(--border-color);
        }

        .chart-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 12px;
            background: var(--bg-glass-light);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        .chart-type-toggle,
        .time-range-toggle {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: wrap;
        }

        .time-range-toggle h4 {
            font-size: 0.8em;
            margin: 0;
            color: var(--text-muted);
            font-weight: 500;
            white-space: nowrap;
        }

        .toggle-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.8em;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .toggle-btn:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
            transform: translateY(-1px);
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            color: white;
            border-color: var(--accent-blue);
            box-shadow: 0 4px 15px var(--glow-color);
        }

        /* ===== 通用表单 & 表格样式 ===== */
        form {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .form-group:hover {
            background: var(--bg-glass);
            transform: translateX(2px);
            box-shadow: 0 4px 15px var(--shadow-color);
            border-color: var(--accent-blue);
        }

        .form-group label {
            margin: 0;
            cursor: pointer;
            font-size: 1.0em;
            color: var(--text-secondary);
            user-select: none;
            transition: color 0.3s ease;
            font-weight: 600;
            white-space: nowrap;
            min-width: fit-content;
        }

        .form-group:hover label {
            color: var(--accent-blue);
        }

        .form-group input[type="number"],
        .form-group input[type="text"],
        .form-group input[type="date"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 1.0em;
            background: var(--bg-glass-light);
            color: var(--text-primary);
            transition: all 0.3s ease;
            font-weight: 500;
            min-width: 120px;
        }

        /* 每日记录表单中的数字输入框优化 */
        #dailyRecordForm .form-group input[type="number"] {
            min-width: 160px;
            text-align: center;
            font-size: 1.05em;
            font-weight: 600;
            padding: 12px 16px;
        }

        /* 确保标签文本完整显示 */
        #dailyRecordForm .form-group label {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 8px;
            display: block;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px var(--glow-color);
            background: var(--bg-glass);
        }

        .form-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--accent-blue);
            cursor: pointer;
        }
        .form-inline {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin: 16px 0;
            padding: 20px;
            background: var(--bg-glass);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px var(--shadow-color);
        }

        /* 每日记录表单特殊优化 */
        #dailyRecordForm .form-inline {
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 18px;
            max-width: 100%;
            overflow: hidden;
        }

        /* 任务系统容器优化 */
        #penaltyContainer, #rewardContainer,
        #editPenaltyContainer, #editRewardContainer {
            max-width: 100%;
            overflow: hidden;
        }

        .form-inline .form-group {
            margin-bottom: 0;
            padding: 14px;
            background: var(--bg-glass-light);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            min-width: 0; /* 防止flex项目溢出 */
        }

        /* 每日记录表单组特殊样式 */
        #dailyRecordForm .form-inline .form-group {
            padding: 16px;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .form-inline .form-group:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
        }

        form button {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            background: var(--accent-blue);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.0em;
            font-weight: 700;
            position: relative;
            overflow: hidden;
            margin-top: 8px;
        }

        form button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        form button:hover {
            background: var(--accent-purple);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--glow-color);
        }

        form button:hover::before {
            left: 100%;
        }

        form button:active {
            transform: translateY(0);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px var(--shadow-color);
        }

        table th, table td {
            border: none;
            border-bottom: 1px solid var(--border-color);
            padding: 14px 18px;
            text-align: left;
            font-size: 1.0em;
            vertical-align: middle;
            font-weight: 500;
        }

        table th {
            background: var(--accent-blue);
            color: white;
            font-weight: 700;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        table td {
            color: var(--text-secondary);
            text-align: center;
        }

        table tr:hover {
            background: var(--bg-glass-light);
        }

        table tr:last-child td {
            border-bottom: none;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 16px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
        }

        .pagination button.active {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            color: white;
            border-color: var(--accent-blue);
            box-shadow: 0 4px 15px var(--glow-color);
        }

        /* ===== 进度条样式 ===== */
        .progress-container {
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            height: 16px;
            position: relative;
        }

        .progress-bar {
            height: 100%;
            background: var(--accent-blue);
            width: 0%;
            transition: width 0.6s ease;
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* ===== 弹出框（规则说明 & 编辑） ===== */
        .modal {
            display: none;
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background: rgba(0,0,0,0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            width: 80%;
            max-width: 800px;
            max-height: 90%;
            overflow-y: auto;
            border-radius: 16px;
            padding: 32px;
            position: relative;
            box-shadow: 0 20px 60px var(--shadow-color);
            color: var(--text-primary);
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 24px;
            cursor: pointer;
            font-size: 1.5em;
            color: var(--text-muted);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-glass-light);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--accent-blue);
            color: white;
            transform: rotate(90deg);
        }

        /* ===== 编辑模态框样式 ===== */
        .edit-modal .modal-content {
            max-width: 600px;
            padding: 16px;
        }
        .edit-modal .form-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 12px;
        }
        .edit-modal .form-inline .form-group {
            flex: 1 1 180px;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .edit-modal h3 {
            margin-bottom: 10px;
            font-size: 1.1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .edit-modal .form-group {
            margin-bottom: 8px;
        }
        .edit-modal .form-group label {
            display: inline-block;
            margin-bottom: 4px;
            font-size: 14px;
        }
        .edit-modal .form-group.date-group {
            margin-bottom: 10px;
        }
        .edit-modal .form-group.date-group label {
            font-weight: 500;
        }
        .edit-modal input[type="text"],
        .edit-modal input[type="number"],
        .edit-modal input[type="date"],
        .edit-modal textarea,
        .edit-modal select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .edit-modal input[type="checkbox"] {
            margin: 0;
            width: 16px;
            height: 16px;
        }
        .edit-modal button.save-btn {
            background-color: #28a745;
            color: #fff;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 8px;
            transition: background-color 0.2s;
        }
        .edit-modal button.save-btn:hover {
            background-color: #218838;
        }

        /* ===== “编辑任务” 弹窗样式 ===== */
        .edit-task-modal .modal-content {
            max-width: 650px;
        }
        .edit-task-modal h3 {
            margin-bottom: 12px;
            font-size: 1.1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .edit-task-modal .form-inline {
            flex-direction: column;
            gap: 12px;
        }
        .edit-task-modal .form-group {
            width: 100%;
        }
        .edit-task-modal button.save-task-btn {
            background-color: #007bff;
        }
        .edit-task-modal button.save-task-btn:hover {
            background-color: #0069d9;
        }

        /* ===== 任务操作按钮样式 ===== */
        .complete-btn {
            background-color: #28a745;
            color: #fff;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin-right: 4px;
            transition: background-color 0.2s;
        }
        .complete-btn:hover {
            background-color: #218838;
        }
        .fail-btn {
            background-color: #dc3545;
            color: #fff;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin-right: 4px;
            transition: background-color 0.2s;
        }
        .fail-btn:hover {
            background-color: #c82333;
        }
        .delete-btn {
            background-color: #6c757d;
            color: #fff;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin-right: 4px;
            transition: background-color 0.2s;
        }
        .delete-btn:hover {
            background-color: #5a6268;
        }
        .editTaskBtn {
            background-color: #007bff;
            color: #fff;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin-right: 4px;
            transition: background-color 0.2s;
        }
        .editTaskBtn:hover {
            background-color: #0069d9;
        }

        /* ===== 任务筛选控制区域样式 ===== */
        .task-controls {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px var(--shadow-color);
            position: relative;
            overflow: hidden;
        }

        .task-controls::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
        }

        .filter-section, .display-section {
            margin-bottom: 16px;
        }

        .filter-section h4, .display-section h4 {
            margin: 0 0 12px 0;
            font-size: 1.1em;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-section h4::before {
            content: '🔍';
            font-size: 1em;
        }

        .display-section h4::before {
            content: '👁️';
            font-size: 1em;
        }

        .task-controls .form-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
            margin: 0;
            padding: 0;
            background: transparent;
            border: none;
            box-shadow: none;
        }

        .task-controls .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
            padding: 8px 12px;
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            min-width: fit-content;
        }

        .task-controls .form-group label {
            font-size: 0.9em;
            font-weight: 600;
            color: var(--text-secondary);
            white-space: nowrap;
            margin: 0;
        }

        .task-controls .form-group select {
            min-width: 120px;
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass);
            color: var(--text-primary);
            font-size: 0.9em;
        }

        .task-controls .form-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--accent-blue);
        }

        /* 描述列隐藏时的样式 */
        .description-column {
            transition: all 0.3s ease;
        }

        .description-column[style*="display: none"] {
            width: 0;
            padding: 0;
            border: none;
        }

        /* ===== 小按钮样式 ===== */
        .small-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        /* ===== 自定义项目样式 ===== */
        .custom-items-section {
            margin: 20px 0;
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: var(--bg-glass-light);
            border-bottom: 1px solid var(--border-color);
        }

        .section-header h4 {
            margin: 0;
            font-size: 1.1em;
            font-weight: 600;
            color: var(--text-primary);
        }

        .toggle-section-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .toggle-section-btn:hover {
            background: var(--accent-blue);
            color: white;
            border-color: var(--accent-blue);
        }

        .custom-items-container {
            padding: 20px;
        }

        .custom-items-list {
            margin-bottom: 16px;
        }

        .custom-item {
            display: grid;
            grid-template-columns: 1fr 100px 150px 100px auto;
            gap: 12px;
            align-items: end;
            padding: 12px;
            margin-bottom: 12px;
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .custom-item-field {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .custom-item-field label {
            font-size: 0.85em;
            font-weight: 600;
            color: var(--text-secondary);
            margin: 0;
        }

        .custom-item:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
        }

        .custom-item input[type="text"],
        .custom-item input[type="number"],
        .custom-item select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9em;
            background: var(--bg-glass);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .custom-item input:focus,
        .custom-item select:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px var(--glow-color);
        }

        .custom-item-remove {
            padding: 8px 12px;
            border: 1px solid #dc3545;
            border-radius: 6px;
            background: #dc3545;
            color: white;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .custom-item-remove:hover {
            background: #c82333;
            border-color: #c82333;
            transform: translateY(-1px);
        }

        .add-custom-btn {
            width: 100%;
            padding: 12px;
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .add-custom-btn:hover {
            border-color: var(--accent-blue);
            color: var(--accent-blue);
            background: var(--bg-glass-light);
        }

        .small-btn:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
            transform: translateY(-1px);
        }

        /* ===== 任务筛选控件样式 ===== */
        .task-filters {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .task-filters h4 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
            font-size: 1.1em;
            font-weight: 600;
        }

        .task-filters .form-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            box-shadow: none;
        }

        .task-filters .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 0;
            padding: 0;
            background: none;
            border: none;
        }

        .task-filters label {
            font-size: 0.9em;
            color: var(--text-secondary);
            white-space: nowrap;
            margin: 0;
        }

        .task-filters select {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass-light);
            color: var(--text-primary);
            font-size: 0.9em;
            min-width: 100px;
        }

        .task-filters select:focus {
            outline: none;
            border-color: var(--accent-blue);
            background: var(--bg-glass);
        }

        .task-filters input[type="checkbox"] {
            margin: 0;
        }

        /* ===== 撤回按钮样式 ===== */
        #undoBtn {
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-pink));
            border: 1px solid var(--accent-orange);
            color: white;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        #undoBtn:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--accent-pink), var(--accent-orange));
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        #undoBtn:disabled {
            background: var(--bg-glass);
            border-color: var(--border-color);
            color: var(--text-secondary);
            cursor: not-allowed;
            opacity: 0.6;
        }

        #undoBtn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* ===== 日期输入区域样式 ===== */
        .date-input-section {
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px var(--shadow-color);
            position: relative;
            overflow: hidden;
        }

        .date-input-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
        }

        .date-input-group {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 0;
        }

        .date-input-group label {
            font-size: 1.2em;
            font-weight: 700;
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .date-input-group input[type="date"] {
            padding: 14px 18px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            background: var(--bg-glass-light);
            color: var(--text-primary);
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-width: 180px;
        }

        .date-input-group input[type="date"]:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px var(--glow-color);
            background: var(--bg-glass);
        }

        /* ===== 历史记录按钮样式 ===== */
        .history-toggle-btn {
            background: var(--bg-glass-light);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: inline-block;
            text-align: center;
            min-width: 100px;
        }

        .history-toggle-btn:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .history-toggle-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* ===== 每日记录卡片样式 ===== */
        .daily-history-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
        }

        .daily-record-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 32px var(--shadow-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .daily-record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px var(--shadow-color);
            border-color: var(--accent-blue);
        }

        .daily-record-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .record-date {
            font-size: 1.1em;
            font-weight: 700;
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .record-actions {
            display: flex;
            gap: 8px;
        }

        .record-actions button {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.8em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .record-actions button:hover {
            background: var(--accent-blue);
            color: white;
            border-color: var(--accent-blue);
            transform: translateY(-1px);
        }

        .record-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .record-section {
            background: var(--bg-glass-light);
            border-radius: 8px;
            padding: 12px;
            border: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 0.9em;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .section-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }

        .data-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .data-label {
            font-size: 0.75em;
            color: var(--text-muted);
            margin-bottom: 2px;
            font-weight: 500;
        }

        .data-value {
            font-size: 0.9em;
            font-weight: 600;
            color: var(--text-secondary);
        }

        .data-value.positive {
            color: var(--accent-blue);
        }

        .data-value.negative {
            color: var(--accent-pink);
        }

        .data-value.zero {
            color: var(--text-muted);
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .info-text {
            font-size: 0.8em;
            color: var(--text-muted);
            line-height: 1.4;
            margin-top: 4px;
        }

        .record-section.full-width {
            grid-column: 1 / -1;
        }

        .record-section .full-width {
            grid-column: 1 / -1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .daily-history-cards {
                grid-template-columns: 1fr;
            }

            .record-content {
                grid-template-columns: 1fr;
            }

            .section-content {
                grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
            }
        }

        .remove-btn {
            padding: 3px 6px;
            border: 1px solid #dc3545;
            border-radius: 4px;
            background: #dc3545;
            color: white;
            cursor: pointer;
            font-size: 0.7em;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 36px;
            height: 26px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            flex-shrink: 0;
        }

        .remove-btn:hover {
            background: #c82333;
            border-color: #c82333;
            transform: translateY(-1px);
        }

        /* ===== 奖励/惩罚项布局样式 ===== */
        .reward-penalty-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 12px;
            background: var(--bg-glass-light);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            min-width: 0;
            flex-wrap: wrap;
        }

        .reward-penalty-item .form-group {
            margin-bottom: 0;
            flex: 1;
            min-width: 0;
        }

        .reward-penalty-item .form-group:first-child {
            flex: 0 0 160px;
            min-width: 160px;
        }

        .reward-penalty-item .form-group:nth-child(2) {
            flex: 0 0 140px;
            min-width: 140px;
        }

        /* 响应式布局：小屏幕时垂直排列 */
        @media (max-width: 600px) {
            .reward-penalty-item {
                flex-direction: column;
                align-items: stretch;
            }

            .reward-penalty-item .form-group:first-child,
            .reward-penalty-item .form-group:nth-child(2) {
                flex: 1;
                min-width: auto;
            }
        }

        .reward-penalty-item select,
        .reward-penalty-item input {
            width: 100%;
            padding: 8px 12px;
            font-size: 0.95em;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            height: 36px;
            box-sizing: border-box;
            color: var(--text-primary);
            font-weight: 500;
        }

        .reward-penalty-item input[type="number"] {
            text-align: center;
            font-weight: 600;
        }

        .reward-penalty-item select:focus,
        .reward-penalty-item input:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px var(--glow-color);
        }

        /* ===== 每日总记录新样式 ===== */
        .daily-overview-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 24px 32px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            color: var(--text-primary);
            box-shadow: 0 8px 32px var(--shadow-color);
            position: relative;
            overflow: hidden;
        }

        .overview-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
        }

        .overview-header h2 {
            margin: 0;
            font-family: "Inter", sans-serif;
            font-size: 1.6em;
            font-weight: 600;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .date-display {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 1em;
            color: var(--text-secondary);
        }

        .refresh-btn {
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            font-weight: 500;
        }

        .refresh-btn:hover {
            background: var(--accent-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px var(--glow-color);
        }

        .overview-panel {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px var(--shadow-color);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .overview-panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px var(--shadow-color);
        }

        .panel-header {
            background: var(--bg-glass-light);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            position: relative;
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
        }

        .panel-header h3 {
            margin: 0 0 6px 0;
            color: var(--text-primary);
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", sans-serif;
            font-size: 1.3em;
            font-weight: 700;
        }

        .panel-subtitle {
            color: var(--text-muted);
            font-size: 0.85em;
            margin: 0;
            font-weight: 400;
        }

        /* ===== 进度条卡片样式 ===== */
        .progress-card {
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 4px 20px var(--shadow-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .progress-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px var(--shadow-color);
            border-color: var(--accent-blue);
        }

        .progress-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .progress-card-icon {
            font-size: 1.6em;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 50%;
            color: white;
            box-shadow: 0 4px 15px var(--glow-color);
        }

        .progress-card-info {
            flex: 1;
        }

        .progress-card-title {
            font-size: 1.0em;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .progress-card-level {
            font-size: 0.85em;
            color: var(--text-muted);
            margin-bottom: 8px;
        }

        .progress-bar-container {
            background: var(--bg-glass-light);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            height: 10px;
            overflow: hidden;
            position: relative;
            margin-bottom: 8px;
        }

        .progress-bar-fill {
            height: 100%;
            background: var(--accent-blue);
            border-radius: 8px;
            transition: width 0.6s ease;
            position: relative;
        }

        .progress-bar-fill.career-art {
            background: var(--accent-pink);
        }

        .progress-bar-fill.career-knowledge {
            background: var(--accent-purple);
        }

        .progress-bar-fill.career-intelligence {
            background: var(--accent-blue);
        }

        .progress-bar-fill.title-dawn {
            background: #ffc107;
        }

        .progress-bar-fill.title-readexp {
            background: var(--accent-blue);
        }

        .progress-bar-fill.title-will {
            background: var(--accent-pink);
        }

        .progress-bar-fill.title-charisma {
            background: var(--accent-purple);
        }

        .progress-percentage {
            font-size: 0.8em;
            color: var(--text-muted);
            text-align: right;
            font-weight: 500;
        }

        .progress-tooltip {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.8em;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .progress-card:hover .progress-tooltip {
            opacity: 1;
        }

        /* 属性卡片样式 */
        .attribute-cards, .career-cards, .title-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            padding: 16px;
            background: var(--bg-glass-light);
            border-radius: 12px;
            margin: 0;
        }

        .attr-card, .career-card, .title-card {
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 20px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .attr-card:hover, .career-card:hover, .title-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px var(--shadow-color);
            border-color: var(--accent-blue);
        }

        .attr-icon, .career-icon, .title-icon {
            font-size: 1.4em;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 50%;
            color: white;
            box-shadow: 0 4px 15px var(--glow-color);
        }

        .attr-info, .career-info, .title-info {
            flex: 1;
        }

        .attr-name, .career-name, .title-name {
            font-size: 0.9em;
            color: var(--text-muted);
            margin-bottom: 4px;
            font-weight: 600;
        }

        .attr-value, .career-progress, .title-progress {
            font-size: 1.2em;
            font-weight: 700;
            color: var(--text-primary);
        }

        .attr-value.positive {
            color: var(--accent-blue);
        }

        .attr-value.negative {
            color: var(--accent-pink);
        }

        /* 表格样式 */
        .attributes-table-container, .career-table-container, .titles-table-container {
            padding: 0 16px 16px 16px;
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 32px var(--shadow-color);
        }

        .modern-table th {
            background: var(--accent-blue);
            color: white;
            padding: 14px 18px;
            text-align: center;
            font-weight: 700;
            font-size: 1.0em;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modern-table td {
            padding: 14px 18px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            font-size: 1.0em;
            color: var(--text-secondary);
            vertical-align: middle;
            font-weight: 500;
        }

        .modern-table tr:hover {
            background: var(--bg-glass-light);
        }

        .modern-table tr:last-child td {
            border-bottom: none;
        }

        /* 任务概览样式 */
        .tasks-overview {
            padding: 16px;
        }

        .task-overview-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid var(--accent-blue);
            box-shadow: 0 8px 32px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .task-overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px var(--shadow-color);
            border-color: var(--accent-blue);
        }

        .task-overview-card.urgent {
            border-left-color: var(--accent-pink);
        }

        .task-overview-card.warning {
            border-left-color: #ffc107;
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .task-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1em;
        }

        .task-type {
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8em;
            border: 1px solid var(--border-color);
        }

        .task-progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9em;
            color: var(--text-muted);
        }

        .task-progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-glass-light);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
            border: 1px solid var(--border-color);
        }

        .task-progress-fill {
            height: 100%;
            background: var(--accent-blue);
            transition: width 0.3s ease;
        }

        .task-progress-fill.warning {
            background: #ffc107;
        }

        .task-progress-fill.danger {
            background: var(--accent-pink);
        }

        /* 总结面板样式 */
        .summary-overview {
            padding: 16px;
        }

        .summary-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #6f42c1;
            line-height: 1.6;
            color: #495057;
            min-height: 80px;
        }

        .summary-empty {
            color: #6c757d;
            font-style: italic;
            text-align: center;
            padding: 40px 20px;
        }

        /* 历史记录切换样式 */
        .history-toggle {
            text-align: center;
            margin: 30px 0;
        }

        .toggle-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-glass-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 0.8em;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .toggle-btn:hover {
            background: var(--bg-glass);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
            transform: translateY(-1px);
        }

        .toggle-btn.active {
            background: var(--accent-blue);
            color: white;
            border-color: var(--accent-blue);
            box-shadow: 0 4px 15px var(--glow-color);
        }

        /* 详细历史记录样式 */
        .detailed-history {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            overflow: hidden;
            border: 1px solid #e8ecf4;
        }

        .history-controls {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px 25px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-controls h3 {
            margin: 0;
            color: #495057;
            font-size: 1.3em;
        }

        .date-range-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-range-selector select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background: white;
            color: #495057;
        }

        .history-day-card {
            border-bottom: 1px solid #e9ecef;
            padding: 25px;
        }

        .history-day-card:last-child {
            border-bottom: none;
        }

        .history-day-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .history-date {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
        }

        .history-summary-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .history-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .history-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .history-section h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1em;
        }

        .history-section p {
            margin: 5px 0;
            font-size: 0.9em;
            color: #6c757d;
        }

        .no-data-message {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
            font-style: italic;
        }

        .no-data-message .icon {
            font-size: 3em;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* ===== 响应式设计 ===== */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            header {
                padding: 16px 20px;
                flex-direction: column;
                gap: 16px;
            }

            header .right {
                flex-wrap: wrap;
                justify-content: center;
            }

            .status-section {
                grid-template-columns: 1fr;
            }

            .tabs {
                flex-wrap: wrap;
                gap: 4px;
            }

            .tab {
                font-size: 0.8em;
                padding: 8px 12px;
            }

            .form-inline {
                grid-template-columns: 1fr;
                gap: 12px;
                padding: 16px;
            }

            #dailyRecordForm .form-inline {
                grid-template-columns: 1fr;
                gap: 14px;
            }

            #dailyRecordForm .form-inline .form-group {
                min-height: 70px;
                padding: 14px;
            }

            #dailyRecordForm .form-group input[type="number"] {
                min-width: auto;
                width: 100%;
            }

            .modal-content {
                width: 95%;
                padding: 20px;
            }

            table {
                font-size: 0.8em;
            }

            table th, table td {
                padding: 8px 6px;
            }
        }

        @media (max-width: 480px) {
            header .left .datetime {
                font-size: 1.2em;
            }

            .status-card h2 {
                font-size: 1.2em;
            }

            .status-box {
                padding: 16px;
            }

            .tab {
                font-size: 0.75em;
                padding: 6px 10px;
            }
        }

        /* ===== 滚动条样式 ===== */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-glass-light);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
        }

        /* ===== 选择文本样式 ===== */
        ::selection {
            background: var(--glow-color);
            color: white;
        }

        ::-moz-selection {
            background: var(--glow-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- ===== 页眉：显示当前北京时间 & 计划启动天数 & 保存/导入/导出/重置 ===== -->
    <header>
        <div class="left">
            <div class="datetime" id="currentDatetime">加载中...</div>
            <div class="days" id="planDays">计划启动天数：0</div>
        </div>
        <div class="right">
            <div class="theme-selector">
                <div class="theme-toggle" id="themeToggle">🎨 主题</div>
                <div class="theme-dropdown" id="themeDropdown">
                    <div class="theme-option active" data-theme="dark">💙 清新蓝调</div>
                    <div class="theme-option" data-theme="nature">🌿 宁静自然</div>
                    <div class="theme-option" data-theme="ocean">🌊 海洋微风</div>
                    <div class="theme-option" data-theme="sunset">🌅 暖沙暮色</div>
                    <div class="theme-option" data-theme="tech">⚡ 科技极简</div>
                    <div class="theme-option" data-theme="orange-blue">🔥 活力橙蓝</div>
                    <div class="theme-option" data-theme="morandi-purple">💜 莫兰迪紫</div>
                    <div class="theme-option" data-theme="mint-fresh">🌱 薄荷清新</div>
                    <div class="theme-option" data-theme="dark-night">🌙 暗夜模式</div>
                    <div class="theme-option" data-theme="forest">🌲 朝日之森</div>
                    <div class="theme-option" data-theme="sea">🌊 月凉之海</div>
                    <div class="theme-option" data-theme="sunset-warm">🌇 暮日之落</div>
                </div>
            </div>
            <button id="setStartDateBtn">设置开始日期</button>
            <button id="undoBtn" disabled title="没有可撤回的操作">↶ 撤回</button>
            <button id="importBtn">导入</button>
            <button id="exportBtn">导出</button>
            <button id="saveBtn">保存</button>
            <button id="resetBtn">重置</button>
        </div>
    </header>

    <div class="container">
        <!-- ===== 当前状态 区域 ===== -->
        <div class="status-card">
            <h2>当前状态 <span class="question-mark" id="statusInfoBtn">?</span></h2>
            <div class="status-section">
                <!-- 属性 -->
                <div class="status-box">
                    <h3>属性</h3>
                    <p>智力：<span id="attrInt">0.00</span></p>
                    <p>知识：<span id="attrKnowledge">0.00</span></p>
                    <p>阅识：<span id="attrReadExp">0.00</span></p>
                    <p>体力：<span id="attrStamina">0.00</span></p>
                    <p>意志：<span id="attrWill">0.00</span></p>
                    <p>魅力：<span id="attrCharisma">0.00</span></p>
                    <p>幻构师经验：<span id="attrArtExp">0</span></p>
                </div>
                <!-- 职业水平 -->
                <div class="status-box">
                    <h3>职业水平</h3>
                    <p>幻构师等级：<span id="careerArtLevel">Lv.1 描形学徒（初级）</span></p>
                    <p>真理之路（知识侧）：<span id="careerTruthKnowledge">LV.1 灰袍学徒（初级）</span></p>
                    <p>真理之路（智力侧）：<span id="careerTruthIntelligence">LV.1 褐衣明理（初级）</span></p>
                </div>
                <!-- 称号 -->
                <div class="status-box">
                    <h3>称号</h3>
                    <p>晨曦之约称号：<span id="titleDawn">无</span></p>
                    <p>阅识称号：<span id="titleReadExp">无</span></p>
                    <p>意志称号：<span id="titleWill">无</span></p>
                    <p>魅力称号：<span id="titleCharisma">无</span></p>
                </div>
            </div>
        </div>

        <!-- ===== 选项卡导航 ===== -->
        <div class="tabs">
            <div class="tab active" data-tab="dailyRecordTab">每日记录</div>
            <div class="tab" data-tab="dawnTab">晨曦之约计划</div>
            <div class="tab" data-tab="dailySummaryTab">每日个人总结</div>
            <div class="tab" data-tab="dailyOverallTab">每日总记录</div>
            <div class="tab" data-tab="artPlanTab">幻构师计划</div>
            <div class="tab" data-tab="truthPlanTab">真理之路计划</div>
            <div class="tab" data-tab="titlesTab">称号系统</div>
            <div class="tab" data-tab="tasksTab">任务系统</div>
            <div class="tab" data-tab="phaseSummaryTab">阶段性总结</div>
        </div>

        <!-- ===== "阶段性总结" 选项卡内容 ===== -->
        <div class="tab-content" id="phaseSummaryTab">
            <div class="charts-container">
                <!-- 基础属性记录 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3>基础属性记录</h3>
                        <div class="chart-toggle-group">
                            <button class="chart-toggle-btn active" id="basicAttrDeltaBtn">每日增量</button>
                            <button class="chart-toggle-btn" id="basicAttrTotalBtn">每日总量</button>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="basicAttrChart"></canvas>
                    </div>
                    <div class="chart-controls">
                        <div class="chart-type-toggle">
                            <button class="toggle-btn active" data-chart="line" data-target="basicAttr">折线图</button>
                            <button class="toggle-btn" data-chart="bar" data-target="basicAttr">条形图</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>当年趋势</h4>
                            <button class="toggle-btn active" data-range="day" data-target="basicAttr">按天</button>
                            <button class="toggle-btn" data-range="month" data-target="basicAttr">按月</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>历史趋势</h4>
                            <button class="toggle-btn" data-range="month-history" data-target="basicAttr">按月</button>
                            <button class="toggle-btn" data-range="year" data-target="basicAttr">按年</button>
                        </div>
                    </div>
                </div>

                <!-- 幻构师经验记录 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3>幻构师经验记录</h3>
                        <div class="chart-toggle-group">
                            <button class="chart-toggle-btn active" id="artExpDeltaBtn">每日增量</button>
                            <button class="chart-toggle-btn" id="artExpTotalBtn">每日总量</button>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="artExpChart"></canvas>
                    </div>
                    <div class="chart-controls">
                        <div class="chart-type-toggle">
                            <button class="toggle-btn active" data-chart="line" data-target="artExp">折线图</button>
                            <button class="toggle-btn" data-chart="bar" data-target="artExp">条形图</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>当年趋势</h4>
                            <button class="toggle-btn active" data-range="day" data-target="artExp">按天</button>
                            <button class="toggle-btn" data-range="month" data-target="artExp">按月</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>历史趋势</h4>
                            <button class="toggle-btn" data-range="month-history" data-target="artExp">按月</button>
                            <button class="toggle-btn" data-range="year" data-target="artExp">按年</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ===== “每日记录” 选项卡内容 ===== -->
        <div class="tab-content active" id="dailyRecordTab">
            <!-- 表单：输入当日多条记录 -->
            <form id="dailyRecordForm">
                <!-- 日期选择单独一行 -->
                <div class="date-input-section">
                    <div class="date-input-group">
                        <label>📅 记录日期</label>
                        <input type="date" id="inputDailyDate" required>
                    </div>
                </div>

                <div class="form-inline">
                    <div class="form-group">
                        <label>绘画时长（分钟）</label>
                        <input type="number" id="inputArtMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>学习时长（分钟）</label>
                        <input type="number" id="inputStudyMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅读专著（页数）</label>
                        <input type="number" id="inputSpecialReadPages" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>运动时长（分钟）</label>
                        <input type="number" id="inputExerciseMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅书页数（页数）</label>
                        <input type="number" id="inputBookReadMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅影时长（分钟）</label>
                        <input type="number" id="inputVideoWatchMinutes" min="0" value="0">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkWillArt"><label for="chkWillArt">绘画参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillStudy"><label for="chkWillStudy">学习参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillExercise"><label for="chkWillExercise">运动参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillBook"><label for="chkWillBook">阅书参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillVideo"><label for="chkWillVideo">阅影参与意志</label>
                    </div>
                </div>

                <!-- 其它自定义项目区域 -->
                <div class="custom-items-section">
                    <div class="section-header">
                        <h4>🔧 其它自定义项目</h4>
                        <button type="button" id="toggleCustomItemsBtn" class="toggle-section-btn">展开</button>
                    </div>
                    <div id="customItemsContainer" class="custom-items-container" style="display: none;">
                        <div class="custom-items-help">
                            <p style="margin: 0 0 12px 0; font-size: 0.9em; color: var(--text-muted); line-height: 1.5;">
                                💡 <strong>使用说明：</strong>可添加任意自定义活动（如跑步、冥想、编程等），设置数值和换算率。
                                属性增量 = 数值 × 换算率，同样受称号效率加成影响。项目配置会自动保存为模板。
                            </p>
                            <details style="margin-bottom: 16px;">
                                <summary style="cursor: pointer; font-size: 0.85em; color: var(--accent-blue); font-weight: 600;">📋 常用配置示例</summary>
                                <div style="margin-top: 8px; font-size: 0.8em; color: var(--text-muted); line-height: 1.4;">
                                    <div style="margin: 4px 0;">• 跑步（分钟） → 体力，换算率 0.5</div>
                                    <div style="margin: 4px 0;">• 冥想（分钟） → 意志，换算率 0.3</div>
                                    <div style="margin: 4px 0;">• 编程（小时） → 智力，换算率 0.8</div>
                                    <div style="margin: 4px 0;">• 写作（页数） → 知识，换算率 0.2</div>
                                    <div style="margin: 4px 0;">• 社交（次数） → 魅力，换算率 0.1</div>
                                    <div style="margin: 4px 0;">• 练字（分钟） → 幻构师经验，换算率 0.2</div>
                                </div>
                            </details>
                        </div>
                        <div class="custom-items-list" id="customItemsList">
                            <!-- 动态插入自定义项目 -->
                        </div>
                        <button type="button" id="addCustomItemBtn" class="add-custom-btn">+ 添加自定义项目</button>
                    </div>
                </div>

                <button type="button" id="addDailyRecordBtn">添加记录</button>
            </form>
            <div style="margin-bottom: 16px; text-align: center;">
                <button id="toggleDailyHistoryBtn" class="history-toggle-btn">历史记录</button>
            </div>
            <div id="dailyHistoryContainer">
                <div class="sort-controls">
                    <label>排序方式：</label>
                    <select id="dailyHistorySortSelect" class="sort-select">
                        <option value="desc">日期倒序（最新在前）</option>
                        <option value="asc">日期正序（最旧在前）</option>
                    </select>
                </div>
                <div id="dailyHistoryCards" class="daily-history-cards">
                    <!-- 动态插入卡片 -->
                </div>
                <div class="pagination" id="dailyHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “晨曦之约计划” 选项卡内容 ===== -->
        <div class="tab-content" id="dawnTab">
            <form id="dawnForm">
                <div class="form-group">
                    <input type="checkbox" id="chkSleptOnTime"><label for="chkSleptOnTime">及时入睡</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkWokeOnTime"><label for="chkWokeOnTime">及时起床</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkSpecialCase"><label for="chkSpecialCase">特殊情况（算作成功）</label>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlySleep"><label for="chkEarlySleep">早睡 +1意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlyRise"><label for="chkEarlyRise">早起 +0.5体力</label>
                    </div>
                </div>
                <button type="button" id="addDawnRecordBtn">打卡</button>
            </form>
            <div style="margin-bottom: 16px; text-align: center;">
                <button id="toggleDawnHistoryBtn" class="history-toggle-btn">历史记录</button>
            </div>
            <div id="dawnHistoryContainer">
                <div class="sort-controls">
                    <label>排序方式：</label>
                    <select id="dawnHistorySortSelect" class="sort-select">
                        <option value="desc">日期倒序（最新在前）</option>
                        <option value="asc">日期正序（最旧在前）</option>
                    </select>
                </div>
                <table id="dawnHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>结果</th>
                            <th>早睡</th>
                            <th>早起</th>
                            <th>意志Δ</th>
                            <th>体力Δ</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dawnHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="dawnHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “每日个人总结” 选项卡内容 ===== -->
        <div class="tab-content" id="dailySummaryTab">
            <form id="dailySummaryForm">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="inputSummaryDate" required>
                </div>
                <div class="form-group">
                    <label>总结内容</label>
                    <textarea id="inputSummaryContent" rows="3"></textarea>
                </div>
                <button type="button" id="addDailySummaryBtn">保存总结</button>
            </form>
            <div style="margin-bottom: 16px; text-align: center;">
                <button id="toggleSummaryHistoryBtn" class="history-toggle-btn">历史记录</button>
            </div>
            <div id="summaryHistoryContainer">
                <div class="sort-controls">
                    <label>排序方式：</label>
                    <select id="summaryHistorySortSelect" class="sort-select">
                        <option value="desc">日期倒序（最新在前）</option>
                        <option value="asc">日期正序（最旧在前）</option>
                    </select>
                </div>
                <table id="summaryHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>内容</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="summaryHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="summaryHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “每日总记录” 选项卡内容 ===== -->
        <div class="tab-content" id="dailyOverallTab">
            <!-- 今日概览卡片 -->
            <div class="daily-overview-container">
                <div class="overview-header">
                    <h2>每日总记录</h2>
                    <div class="date-display">
                        <span id="currentOverviewDate"></span>
                        <button id="refreshOverviewBtn" class="refresh-btn">🔄 刷新</button>
                    </div>
                </div>

                <!-- 属性增量面板 -->
                <div class="overview-panel">
                    <div class="panel-header">
                        <h3>📊 属性增量</h3>
                        <span class="panel-subtitle">最近7天的属性变化</span>
                    </div>
                    <div class="attributes-overview">
                        <div class="attribute-cards">
                            <div class="attr-card">
                                <div class="attr-icon">🧠</div>
                                <div class="attr-info">
                                    <div class="attr-name">智力</div>
                                    <div class="attr-value" id="todayIntDelta">+0.00</div>
                                </div>
                            </div>
                            <div class="attr-card">
                                <div class="attr-icon">📚</div>
                                <div class="attr-info">
                                    <div class="attr-name">知识</div>
                                    <div class="attr-value" id="todayKnowledgeDelta">+0.00</div>
                                </div>
                            </div>
                            <div class="attr-card">
                                <div class="attr-icon">👁️</div>
                                <div class="attr-info">
                                    <div class="attr-name">阅识</div>
                                    <div class="attr-value" id="todayReadExpDelta">+0.00</div>
                                </div>
                            </div>
                            <div class="attr-card">
                                <div class="attr-icon">💪</div>
                                <div class="attr-info">
                                    <div class="attr-name">体力</div>
                                    <div class="attr-value" id="todayStaminaDelta">+0.00</div>
                                </div>
                            </div>
                            <div class="attr-card">
                                <div class="attr-icon">🔥</div>
                                <div class="attr-info">
                                    <div class="attr-name">意志</div>
                                    <div class="attr-value" id="todayWillDelta">+0.00</div>
                                </div>
                            </div>
                            <div class="attr-card">
                                <div class="attr-icon">✨</div>
                                <div class="attr-info">
                                    <div class="attr-name">魅力</div>
                                    <div class="attr-value" id="todayCharismaDelta">+0.00</div>
                                </div>
                            </div>
                            <div class="attr-card">
                                <div class="attr-icon">🎨</div>
                                <div class="attr-info">
                                    <div class="attr-name">幻构师经验</div>
                                    <div class="attr-value" id="todayArtExpDelta">+0.00</div>
                                </div>
                            </div>
                        </div>
                        <div class="attributes-table-container">
                            <table class="modern-table" id="attributesHistoryTable">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>智力Δ</th>
                                        <th>知识Δ</th>
                                        <th>阅识Δ</th>
                                        <th>体力Δ</th>
                                        <th>意志Δ</th>
                                        <th>魅力Δ</th>
                                        <th>幻构师经验Δ</th>
                                    </tr>
                                </thead>
                                <tbody id="attributesHistoryBody">
                                    <!-- 动态插入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 职业水平进度面板 -->
                <div class="overview-panel">
                    <div class="panel-header">
                        <h3>🎯 职业水平进度</h3>
                        <span class="panel-subtitle">等级提升情况与进度</span>
                    </div>
                    <div class="career-overview">
                        <div style="padding: 16px;">
                            <!-- 幻构师等级进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">🎨</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">幻构师等级</div>
                                        <div class="progress-card-level" id="artLevelName">Lv.1 描形学徒（初级）</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill career-art" id="artLevelProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="artLevelTooltip">当前经验: 1460 / 1500</div>
                                </div>
                                <div class="progress-percentage" id="artLevelPercentage">0%</div>
                            </div>

                            <!-- 真理之路(知识)进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">📖</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">真理之路(知识)</div>
                                        <div class="progress-card-level" id="truthKnowledgeLevelName">LV.1 灰袍学徒（初级）</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill career-knowledge" id="truthKnowledgeProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="truthKnowledgeTooltip">当前知识: 0 / 150</div>
                                </div>
                                <div class="progress-percentage" id="truthKnowledgePercentage">0%</div>
                            </div>

                            <!-- 真理之路(智力)进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">🧠</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">真理之路(智力)</div>
                                        <div class="progress-card-level" id="truthIntelligenceLevelName">LV.1 褐衣明理（初级）</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill career-intelligence" id="truthIntelligenceProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="truthIntelligenceTooltip">当前智力: 0 / 150</div>
                                </div>
                                <div class="progress-percentage" id="truthIntelligencePercentage">0%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 称号进度面板 -->
                <div class="overview-panel">
                    <div class="panel-header">
                        <h3>🏆 称号进度</h3>
                        <span class="panel-subtitle">称号等级与获得进度</span>
                    </div>
                    <div class="titles-overview">
                        <div style="padding: 16px;">
                            <!-- 晨曦之约称号进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">🌅</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">晨曦之约称号</div>
                                        <div class="progress-card-level" id="dawnTitleName">无称号</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill title-dawn" id="dawnTitleProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="dawnTitleTooltip">连续成功: 0 / 7 天</div>
                                </div>
                                <div class="progress-percentage" id="dawnTitlePercentage">0%</div>
                            </div>

                            <!-- 阅识称号进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">👁️</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">阅识称号</div>
                                        <div class="progress-card-level" id="readExpTitleName">无称号</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill title-readexp" id="readExpTitleProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="readExpTitleTooltip">当前阅识: 0 / 50</div>
                                </div>
                                <div class="progress-percentage" id="readExpTitlePercentage">0%</div>
                            </div>

                            <!-- 意志称号进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">🔥</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">意志称号</div>
                                        <div class="progress-card-level" id="willTitleName">无称号</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill title-will" id="willTitleProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="willTitleTooltip">当前意志: 0 / 50</div>
                                </div>
                                <div class="progress-percentage" id="willTitlePercentage">0%</div>
                            </div>

                            <!-- 魅力称号进度卡片 -->
                            <div class="progress-card">
                                <div class="progress-card-header">
                                    <div class="progress-card-icon">✨</div>
                                    <div class="progress-card-info">
                                        <div class="progress-card-title">魅力称号</div>
                                        <div class="progress-card-level" id="charismaTitleName">无称号</div>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill title-charisma" id="charismaTitleProgressBar" style="width: 0%"></div>
                                    <div class="progress-tooltip" id="charismaTitleTooltip">当前魅力: 0 / 10</div>
                                </div>
                                <div class="progress-percentage" id="charismaTitlePercentage">0%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务情况概览面板 -->
                <div class="overview-panel">
                    <div class="panel-header">
                        <h3>📋 任务情况概览</h3>
                        <span class="panel-subtitle">未完成任务及进度</span>
                    </div>
                    <div class="tasks-overview" id="tasksOverviewPanel">
                        <!-- 动态插入任务卡片 -->
                    </div>
                </div>

                <!-- 每日个人总结面板 -->
                <div class="overview-panel">
                    <div class="panel-header">
                        <h3>📝 每日个人总结</h3>
                        <span class="panel-subtitle">今日总结与反思</span>
                    </div>
                    <div class="summary-overview" id="summaryOverviewPanel">
                        <!-- 动态插入总结内容 -->
                    </div>
                </div>

                <!-- 历史记录切换 -->
                <div class="history-toggle">
                    <button id="toggleDetailedHistoryBtn" class="toggle-btn">📊 查看详细历史记录</button>
                </div>

                <!-- 详细历史记录 -->
                <div id="detailedHistoryContainer" class="detailed-history" style="display: none;">
                    <div class="history-controls">
                        <h3>📈 详细历史记录</h3>
                        <div class="date-range-selector">
                            <label>显示天数：</label>
                            <select id="historyDaysSelect">
                                <option value="7">最近7天</option>
                                <option value="14">最近14天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="60">最近60天</option>
                                <option value="all">全部</option>
                            </select>
                        </div>
                    </div>
                    <div id="detailedHistoryContent">
                        <!-- 动态插入详细历史记录 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- ===== “幻构师计划” 选项卡内容 ===== -->
        <div class="tab-content" id="artPlanTab">
            <table id="artPlanTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求经验</th>
                        <th>阶段</th>
                        <th>当前经验</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “真理之路计划” 选项卡内容 ===== -->
        <div class="tab-content" id="truthPlanTab">
            <h3>知识侧</h3>
            <table id="truthKnowledgeTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求知识</th>
                        <th>阶段</th>
                        <th>当前知识</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <h3 style="margin-top: 16px;">智力侧</h3>
            <table id="truthIntelligenceTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求智力</th>
                        <th>阶段</th>
                        <th>当前智力</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “称号系统” 选项卡内容 ===== -->
        <div class="tab-content" id="titlesTab">
            <!-- 晨曦之约称号 -->
            <h3>晨曦之约称号</h3>
            <table id="titlesDawnTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需坚持天数</th>
                        <th>当前坚持天数</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 阅识称号 -->
            <h3 style="margin-top: 16px;">阅识称号</h3>
            <table id="titlesReadExpTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需阅识</th>
                        <th>当前阅识</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 意志称号 -->
            <h3 style="margin-top: 16px;">意志称号</h3>
            <table id="titlesWillTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需意志</th>
                        <th>当前意志</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 魅力称号 -->
            <h3 style="margin-top: 16px;">魅力称号</h3>
            <table id="titlesCharismaTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需魅力</th>
                        <th>当前魅力</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “任务系统” 选项卡内容 ===== -->
        <div class="tab-content" id="tasksTab">
            <h3>创建新任务</h3>
            <form id="taskForm">
                <div class="form-group">
                    <label>名称</label>
                    <input type="text" id="taskName" required>
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <textarea id="taskDescription" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>类型</label>
                    <select id="taskType" required>
                        <option value="">请选择</option>
                        <option value="art">幻构师计划</option>
                        <option value="truth">真理之路计划</option>
                        <option value="dawn">晨曦之约计划</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>周期</label>
                    <select id="taskCycle" required>
                        <option value="">请选择</option>
                        <option value="short">短期</option>
                        <option value="long">长期</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标类型</label>
                    <select id="taskTargetType" required>
                        <option value="">请选择</option>
                        <option value="study">学习时长（分钟）</option>
                        <option value="specialRead">专著页数（页）</option>
                        <option value="art">绘画时长（分钟）</option>
                        <option value="exercise">运动时长（分钟）</option>
                        <option value="bookRead">阅书页数（页）</option>
                        <option value="videoWatch">阅影时长（分钟）</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标数值</label>
                    <input type="number" id="taskTargetValue" min="1" required>
                </div>
                <div class="form-group">
                    <label>截止日期</label>
                    <input type="date" id="taskDeadline">
                </div>
                <div id="penaltyContainer">
                    <h4>惩罚</h4>
                    <button type="button" id="addPenaltyBtn" class="small-btn">添加惩罚</button>
                    <!-- 动态插入惩罚条目 -->
                </div>
                <div id="rewardContainer" style="margin-top: 12px;">
                    <h4>奖励</h4>
                    <button type="button" id="addRewardBtn" class="small-btn">添加奖励</button>
                    <!-- 动态插入奖励条目 -->
                </div>
                <button type="button" id="createTaskBtn" class="small-btn" style="margin-top: 12px;">创建任务</button>
            </form>

            <!-- 任务统计 -->
            <div class="status-card" style="margin-top: 20px;">
                <h3>任务统计</h3>
                <div class="status-section">
                    <div class="status-box">
                        <h4>总体统计</h4>
                        <p>总任务数：<span id="taskStatsTotal">0</span></p>
                        <p>未完成：<span id="taskStatsPending">0</span></p>
                        <p>已完成：<span id="taskStatsCompleted">0</span></p>
                        <p>已失败：<span id="taskStatsFailed">0</span></p>
                    </div>
                    <div class="status-box">
                        <h4>完成率</h4>
                        <p>完成率：<span id="taskStatsCompletionRate">0%</span></p>
                        <p>失败率：<span id="taskStatsFailureRate">0%</span></p>
                        <p>成功率：<span id="taskStatsSuccessRate">0%</span></p>
                    </div>
                </div>
            </div>

            <!-- 任务筛选控件 -->
            <div class="task-filters" style="margin-bottom: 16px; padding: 12px; background: var(--card-bg); border-radius: 8px; border: 1px solid var(--border-color);">
                <h4 style="margin: 0 0 12px 0;">任务筛选</h4>
                <div class="form-inline" style="gap: 12px; align-items: center;">
                    <div class="form-group">
                        <label>类型筛选：</label>
                        <select id="taskTypeFilter" style="min-width: 120px;">
                            <option value="">全部类型</option>
                            <option value="art">幻构师计划</option>
                            <option value="truth">真理之路计划</option>
                            <option value="dawn">晨曦之约计划</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>周期筛选：</label>
                        <select id="taskCycleFilter" style="min-width: 100px;">
                            <option value="">全部周期</option>
                            <option value="short">短期</option>
                            <option value="long">长期</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="showDescriptionToggle" checked>
                        <label for="showDescriptionToggle">显示描述</label>
                    </div>
                    <button type="button" id="clearFiltersBtn" class="small-btn">清除筛选</button>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h3 style="margin: 0;">未完成任务</h3>
                <div class="sort-controls">
                    <label>排序：</label>
                    <select id="pendingTasksSortSelect" class="sort-select">
                        <option value="created_desc">创建时间倒序（最新在前）</option>
                        <option value="created_asc">创建时间正序（最旧在前）</option>
                        <option value="deadline_asc">截止日期正序（最近在前）</option>
                        <option value="deadline_desc">截止日期倒序（最远在前）</option>
                    </select>
                </div>
            </div>
            <table class="task-table" id="pendingTasksTable">
                <thead>
                    <tr>
                        <th>创建时间</th>
                        <th>名称</th>
                        <th class="description-column">描述</th>
                        <th>类型</th>
                        <th>周期</th>
                        <th>进度</th>
                        <th>剩余天数</th>
                        <th>奖惩</th>
                        <th>进度条</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="pendingTasksBody">
                    <!-- 动态插入 -->
                </tbody>
            </table>

            <div style="display: flex; justify-content: space-between; align-items: center; margin: 16px 0 12px 0;">
                <h3 style="margin: 0;">已完成/失败任务</h3>
                <div class="sort-controls">
                    <label>排序：</label>
                    <select id="completedTasksSortSelect" class="sort-select">
                        <option value="completion_desc">完成时间倒序（最新在前）</option>
                        <option value="completion_asc">完成时间正序（最旧在前）</option>
                        <option value="created_desc">创建时间倒序（最新在前）</option>
                        <option value="created_asc">创建时间正序（最旧在前）</option>
                    </select>
                </div>
            </div>
            <table class="task-table" id="completedTasksTable">
                <thead>
                    <tr>
                        <th>创建时间</th>
                        <th>名称</th>
                        <th class="description-column">描述</th>
                        <th>类型</th>
                        <th>周期</th>
                        <th>目标/完成</th>
                        <th>完成/失败日期</th>
                        <th>奖励/惩罚</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="completedTasksBody">
                    <!-- 动态插入 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- ===== 规则说明 弹出框 ===== -->
    <div class="modal" id="statusInfoModal">
        <div class="modal-content">
            <span class="modal-close" id="statusInfoClose">&times;</span>
            <h2>基础属性提升规则 &amp; 称号获取规则</h2>
            <h3>基础属性提升方法</h3>
            <ul>
                <li>智力：学习 1 小时 +1 智力。</li>
                <li>体力：运动 1 小时 +1 体力。</li>
                <li>知识：阅读专著 1 页 +0.1 知识。</li>
                <li>阅识：阅读 1 页其它书籍 +0.1；观看影视 1 小时 +10。</li>
                <li>意志：连续执行某行动 3 天+ 每天 +1；7 天+ 每天 +2；30 天+ 每天 +3；失败 1 天 –1；连续失败 ≥3 天 每天 –2；连续失败 ≥7 天 每天 –3。</li>
                <li>魅力：其它五种属性提升之和 ×0.1。</li>
                <li>幻构师经验：初始 0；绘画 1 小时 +10 经验。</li>
                <li><strong>自定义项目：</strong>可在每日记录中添加自定义活动，设置项目名称、数值、目标属性和换算规则。属性增量 = 数值 × 换算率，同样受称号效率加成影响。</li>
            </ul>
            <h3>幻构师计划</h3>
            <ul>
                <li>Lv.1 描形学徒（总需求 1500）：初级 (1–450)、中级 (451–1050)、高级 (1051–1500)</li>
                <li>Lv.2 构素学者（总需求 3000）：初级 (1501–2400)、中级 (2401–3600)、高级 (3601–4500)</li>
                <li>Lv.3 灵绘使徒（总需求 5000）：初级 (4501–6000)、中级 (6001–8000)、高级 (8001–9500)</li>
                <li>Lv.4 影纹术士（总需求 8000）：初级 (9501–11900)、中级 (11901–15100)、高级 (15101–17500)</li>
                <li>Lv.5 心象织者（总需求 12000）：初级 (17501–21100)、中级 (21101–25900)、高级 (25901–29500)</li>
                <li>Lv.6 空境画匠（总需求 18000）：初级 (29501–34900)、中级 (34901–42100)、高级 (42101–47500)</li>
                <li>Lv.7 律令绘爵（总需求 26000）：初级 (47501–55300)、中级 (55301–65700)、高级 (65701–73500)</li>
                <li>Lv.8 幻构师（总需求 36000）：初级 (73501–84300)、中级 (84301–98700)、高级 (98701–109500)</li>
            </ul>
            <h3>真理之路计划</h3>
            <h4>知识侧</h4>
            <ul>
                <li>LV.1 灰袍学徒（总需求 150）：初级 (1–30)、中级 (31–75)、高级 (76–150)</li>
                <li>LV.2 白袍向导（总需求 500）：初级 (151–250)、中级 (251–400)、高级 (401–650)</li>
                <li>LV.3 墨衣学者（总需求 1500）：初级 (651–950)、中级 (951–1400)、高级 (1401–2150)</li>
                <li>LV.4 青衿贤者（总需求 4000）：初级 (2151–2950)、中级 (2951–4150)、高级 (4151–6150)</li>
                <li>LV.5 玄冕宗师（总需求 10000）：初级 (6151–8150)、中级 (8151–11150)、高级 (11151–16150)</li>
            </ul>
            <h4>智力侧</h4>
            <ul>
                <li>LV.1 褐衣明理（总需求 150）：初级 (1–30)、中级 (31–75)、高级 (76–150)</li>
                <li>LV.2 缁衣慎思（总需求 500）：初级 (151–250)、中级 (251–400)、高级 (401–650)</li>
                <li>LV.3 朱衣审辩（总需求 1500）：初级 (651–950)、中级 (951–1400)、高级 (1401–2150)</li>
                <li>LV.4 紫绶格物（总需求 4000）：初级 (2151–2950)、中级 (2951–4150)、高级 (4151–6150)</li>
                <li>LV.5 金章弘道（总需求 10000）：初级 (6151–8150)、中级 (8151–11150)、高级 (11151–16150)</li>
            </ul>
            <h3>晨曦之约计划</h3>
            <ul>
                <li>惩罚：未及时入睡 –1意志；未及时起床 –0.5体力。</li>
                <li>奖励：连续 ≥3 天：+0.5意志/+0.2体力；连续 ≥7 天：+1意志/+0.5体力；连续 ≥30 天：+2意志/+1体力；勾选早睡/早起额外 +1意志/+0.5体力。</li>
                <li>称号：</li>
                <ul>
                    <li>Lv.1 星辉学徒（7 天）：智力提升效率 +5%</li>
                    <li>Lv.2 晨风哨卫（30 天）：知识+智力提升效率 +5%</li>
                    <li>Lv.3 夜穹守誓（60 天）：全体属性提升效率 +5%</li>
                    <li>Lv.4 破晓骑士（90 天）：全体属性提升效率 +10%</li>
                    <li>Lv.5 黎明星使（120 天）：全体属性提升效率 +15%</li>
                    <li>Lv.6 永夜圣者（180 天）：全体属性提升效率 +20%</li>
                    <li>Lv.7 晨曦领主（365 天）：全体属性提升效率 +25%</li>
                    <li>Lv.8 时序主宰（730 天）：全体属性提升效率 +30%</li>
                </ul>
                <li>失约规则：打卡失败即失去当前称号并从头计算。</li>
            </ul>
            <h3>称号系统</h3>
            <h4>阅识称号</h4>
            <ul>
                <li>Lv.1 历尘星火（50）：智力获取效率 +5%</li>
                <li>Lv.2 历溪观澜（200）：知识+智力获取效率 +5%</li>
                <li>Lv.3 历卷拓荒（500）：全体属性获取效率 +5%</li>
                <li>Lv.4 历镜寻真（800）：全体属性获取效率 +10%</li>
                <li>Lv.5 历川归海（1200）：全体属性获取效率 +15%</li>
                <li>Lv.6 历世洞明（2000）：全体属性获取效率 +20%</li>
                <li>Lv.7 历界织识（3000）：全体属性获取效率 +25%</li>
                <li>Lv.8 历象归藏（5000）：全体属性获取效率 +30%</li>
            </ul>
            <h4>意志称号</h4>
            <ul>
                <li>Lv.1 晨曦微志（50）：魅力增长效率 +5%</li>
                <li>Lv.2 坚石守心（200）：魅力增长效率 +10%</li>
                <li>Lv.3 荆棘先锋（500）：魅力增长效率 +15%</li>
                <li>Lv.4 钢铁铸意（800）：魅力增长效率 +20%</li>
                <li>Lv.5 风暴不屈（1200）：魅力增长效率 +25%</li>
                <li>Lv.6 星辰恒志（2000）：魅力增长效率 +30%</li>
                <li>Lv.7 炽魂永燃（3000）：魅力增长效率 +40%</li>
                <li>Lv.8 无朽之心（5000）：魅力增长效率 +50%</li>
            </ul>
            <h4>魅力称号</h4>
            <ul>
                <li>Lv.1 萤火微光（10）：全体属性提升效率 +5%</li>
                <li>Lv.2 晨露流辉（50）：全体属性提升效率 +10%</li>
                <li>Lv.3 星芒初绽（100）：全体属性提升效率 +15%</li>
                <li>Lv.4 银月颂光（200）：全体属性提升效率 +20%</li>
                <li>Lv.5 日冕凝华（300）：全体属性提升效率 +25%</li>
                <li>Lv.6 虹彩冠冕（500）：全体属性提升效率 +30%</li>
                <li>Lv.7 天穹律光（800）：全体属性提升效率 +40%</li>
                <li>Lv.8 万象圣辉（1200）：全体属性提升效率 +50%</li>
            </ul>
            <h3>自定义项目使用说明</h3>
            <ul>
                <li><strong>功能位置：</strong>在"每日记录"选项卡的表单末尾，点击"🔧 其它自定义项目"区域的"展开"按钮。</li>
                <li><strong>添加项目：</strong>点击"+ 添加自定义项目"按钮，可添加多个自定义活动项目。</li>
                <li><strong>配置说明：</strong></li>
                <ul>
                    <li>项目名称：自定义活动的名称（如"跑步"、"冥想"、"编程"等）</li>
                    <li>数值：当日该活动的数量（如时长、次数、页数等）</li>
                    <li>目标属性：选择该活动主要提升的属性（智力、知识、阅识、体力、意志、魅力、幻构师经验）</li>
                    <li>换算规则：每单位活动对应的属性点数（支持小数，如0.5表示每单位获得0.5属性点）</li>
                </ul>
                <li><strong>计算规则：</strong>属性增量 = 数值 × 换算率，计算结果会受到相应称号的效率加成影响。</li>
                <li><strong>模板保存：</strong>项目名称、目标属性和换算规则会自动保存为模板，方便重复使用。</li>
                <li><strong>数值重置：</strong>添加记录后，数值会自动重置为0，但模板配置会保留。</li>
                <li><strong>使用示例：</strong>项目名称"跑步"，数值"30"，目标属性"体力"，换算规则"0.5"，则获得15点体力增量。</li>
            </ul>
        </div>
    </div>

    <!-- ===== 编辑“每日记录” 的模态框 ===== -->
    <div class="modal edit-modal" id="editDailyModal">
        <div class="modal-content">
            <span class="modal-close" id="editDailyClose">&times;</span>
            <h3>编辑—每日记录</h3>
            <form id="editDailyForm">
                <input type="hidden" id="editDailyTimestamp">
                <div class="form-group date-group">
                    <label>日期</label>
                    <input type="date" id="editDailyDate">
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <label>绘画时长（分钟）</label>
                        <input type="number" id="editArtMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>学习时长（分钟）</label>
                        <input type="number" id="editStudyMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>阅读专著（页数）</label>
                        <input type="number" id="editSpecialReadPages" min="0">
                    </div>
                    <div class="form-group">
                        <label>运动时长（分钟）</label>
                        <input type="number" id="editExerciseMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>阅书页数（页数）</label>
                        <input type="number" id="editBookReadMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>阅影时长（分钟）</label>
                        <input type="number" id="editVideoWatchMinutes" min="0">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillArt"><label for="editChkWillArt">绘画参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillStudy"><label for="editChkWillStudy">学习参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillExercise"><label for="editChkWillExercise">运动参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillBook"><label for="editChkWillBook">阅书参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillVideo"><label for="editChkWillVideo">阅影参与意志</label>
                    </div>
                </div>
                <button type="button" class="save-btn" id="saveEditDailyBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 编辑“晨曦之约” 的模态框 ===== -->
    <div class="modal edit-modal" id="editDawnModal">
        <div class="modal-content">
            <span class="modal-close" id="editDawnClose">&times;</span>
            <h3>编辑—晨曦之约记录</h3>
            <form id="editDawnForm">
                <input type="hidden" id="editDawnDate">
                <div class="form-group date-group">
                    <label>日期</label>
                    <input type="date" id="editDawnNewDate">
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkSleptOnTime"><label for="editChkSleptOnTime">及时入睡</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkWokeOnTime"><label for="editChkWokeOnTime">及时起床</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkSpecialCase"><label for="editChkSpecialCase">特殊情况（算作成功）</label>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="editChkEarlySleep"><label for="editChkEarlySleep">早睡 +1意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkEarlyRise"><label for="editChkEarlyRise">早起 +0.5体力</label>
                    </div>
                </div>
                <button type="button" class="save-btn" id="saveEditDawnBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 编辑“每日总结” 的模态框 ===== -->
    <div class="modal edit-modal" id="editSummaryModal">
        <div class="modal-content">
            <span class="modal-close" id="editSummaryClose">&times;</span>
            <h3>编辑—每日总结</h3>
            <form id="editSummaryForm">
                <input type="hidden" id="editSummaryOldDate">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="editSummaryDate" required>
                </div>
                <div class="form-group">
                    <label>总结内容</label>
                    <textarea id="editSummaryContent" rows="3"></textarea>
                </div>
                <button type="button" class="save-btn" id="saveEditSummaryBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 新增：编辑“已完成/失败任务” 的模态框 ===== -->
    <div class="modal edit-task-modal" id="editTaskModal">
        <div class="modal-content">
            <span class="modal-close" id="editTaskClose">&times;</span>
            <h3>编辑—任务</h3>
            <form id="editTaskForm">
                <input type="hidden" id="editTaskId">
                <div class="form-group">
                    <label>创建时间</label>
                    <input type="datetime-local" id="editTaskCreatedTimestamp" required>
                </div>
                <div class="form-group">
                    <label>名称</label>
                    <input type="text" id="editTaskName" required>
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <textarea id="editTaskDescription" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>类型</label>
                    <select id="editTaskType" required>
                        <option value="art">幻构师计划</option>
                        <option value="truth">真理之路计划</option>
                        <option value="dawn">晨曦之约计划</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>周期</label>
                    <select id="editTaskCycle" required>
                        <option value="short">短期</option>
                        <option value="long">长期</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标类型</label>
                    <select id="editTaskTargetType" required>
                        <option value="study">学习时长（分钟）</option>
                        <option value="specialRead">专著页数（页）</option>
                        <option value="art">绘画时长（分钟）</option>
                        <option value="exercise">运动时长（分钟）</option>
                        <option value="bookRead">阅书页数（页）</option>
                        <option value="videoWatch">阅影时长（分钟）</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标数值</label>
                    <input type="number" id="editTaskTargetValue" min="1" required>
                </div>
                <div class="form-group">
                    <label>截止日期</label>
                    <input type="date" id="editTaskDeadline">
                </div>
                <div id="editPenaltyContainer">
                    <h4>惩罚</h4>
                    <!-- 动态插入惩罚条目 -->
                    <button type="button" id="addEditPenaltyBtn" class="add-btn">添加惩罚</button>
                </div>
                <div id="editRewardContainer" style="margin-top: 12px;">
                    <h4>奖励</h4>
                    <!-- 动态插入奖励条目 -->
                    <button type="button" id="addEditRewardBtn" class="add-btn">添加奖励</button>
                </div>
                <div class="form-group">
                    <label>状态</label>
                    <select id="editTaskStatus" required>
                        <option value="pending">未完成</option>
                        <option value="completed">已完成</option>
                        <option value="failed">已失败</option>
                    </select>
                </div>
                <div class="form-group date-group">
                    <label>完成/失败日期</label>
                    <input type="date" id="editTaskCompletionDate">
                </div>
                <button type="button" class="save-task-btn" id="saveEditTaskBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 设置开始日期模态框 ===== -->
    <div class="modal" id="setStartDateModal">
        <div class="modal-content">
            <span class="modal-close" id="setStartDateClose">&times;</span>
            <h3>设置计划开始日期</h3>
            <form id="setStartDateForm">
                <div class="form-group">
                    <label>计划开始日期</label>
                    <input type="date" id="inputStartDate" required>
                </div>
                <div class="form-group">
                    <p style="color: #666; font-size: 0.9em; margin: 10px 0;">
                        注意：修改开始日期将影响计划启动天数的计算。<br>
                        当前设置的开始日期：<span id="currentStartDate"></span>
                    </p>
                </div>
                <button type="button" id="saveStartDateBtn" class="save-btn">保存设置</button>
            </form>
        </div>
    </div>

    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 上一部分在此结束后，直接插入下面的 <script> 标签 -->
    <script>
        /********************
         * 全局数据与常量 *
         ********************/
        // 计划开始日期（可通过用户设置或导入覆盖）
        let planStartDate = new Date();

        // 数据存储结构
        let data = {
            startDate: planStartDate.toISOString().substr(0, 10),
            initial: {
                intelligence: 0,
                knowledge: 0,
                readExp: 0,
                stamina: 0,
                will: 0,
                charisma: 0,
                artExp: 0
            },
            dawn: {
                consecutiveSuccess: 0,
                consecutiveFail: 0,
                history: []
            },
            dailyRecords: [],
            summaries: [],
            overallRecords: [],
            tasks: []
        };

        // 撤回功能相关变量
        let operationHistory = [];
        const MAX_HISTORY_SIZE = 50; // 最多保存50步操作历史

        /*******************************
         * 撤回功能实现 *
         *******************************/

        // 保存操作前的状态
        function saveOperationState(operationType, description) {
            // 深拷贝当前数据状态
            const currentState = JSON.parse(JSON.stringify(data));

            const operation = {
                timestamp: new Date().toISOString(),
                type: operationType,
                description: description,
                dataState: currentState
            };

            operationHistory.push(operation);

            // 限制历史记录大小
            if (operationHistory.length > MAX_HISTORY_SIZE) {
                operationHistory.shift(); // 移除最旧的记录
            }

            updateUndoButton();
        }

        // 执行撤回操作
        function performUndo() {
            if (operationHistory.length === 0) {
                alert('没有可撤回的操作');
                return;
            }

            const lastOperation = operationHistory.pop();

            // 恢复到上一个状态
            data = JSON.parse(JSON.stringify(lastOperation.dataState));
            planStartDate = parseDate(data.startDate);

            // 重新渲染界面
            renderAll();
            updateUndoButton();

            // 显示撤回信息
            alert(`已撤回操作：${lastOperation.description}`);
        }

        // 更新撤回按钮状态
        function updateUndoButton() {
            const undoBtn = document.getElementById('undoBtn');
            if (undoBtn) {
                undoBtn.disabled = operationHistory.length === 0;
                undoBtn.title = operationHistory.length > 0
                    ? `撤回：${operationHistory[operationHistory.length - 1].description}`
                    : '没有可撤回的操作';
            }
        }

        // 清空操作历史（用于导入数据等场景）
        function clearOperationHistory() {
            operationHistory = [];
            updateUndoButton();
        }

        // 各等级与称号定义（与第一部分相同，可省略详细注释）
        const ART_LEVELS = [
            { level: 'Lv.1 描形学徒', totalReq: 1500, stages: [{ name: '初级', min: 1, max: 450 }, { name: '中级', min: 451, max: 1050 }, { name: '高级', min: 1051, max: 1500 }] },
            { level: 'Lv.2 构素学者', totalReq: 3000, stages: [{ name: '初级', min: 1501, max: 2400 }, { name: '中级', min: 2401, max: 3600 }, { name: '高级', min: 3601, max: 4500 }] },
            { level: 'Lv.3 灵绘使徒', totalReq: 5000, stages: [{ name: '初级', min: 4501, max: 6000 }, { name: '中级', min: 6001, max: 8000 }, { name: '高级', min: 8001, max: 9500 }] },
            { level: 'Lv.4 影纹术士', totalReq: 8000, stages: [{ name: '初级', min: 9501, max: 11900 }, { name: '中级', min: 11901, max: 15100 }, { name: '高级', min: 15101, max: 17500 }] },
            { level: 'Lv.5 心象织者', totalReq: 12000, stages: [{ name: '初级', min: 17501, max: 21100 }, { name: '中级', min: 21101, max: 25900 }, { name: '高级', min: 25901, max: 29500 }] },
            { level: 'Lv.6 空境画匠', totalReq: 18000, stages: [{ name: '初级', min: 29501, max: 34900 }, { name: '中级', min: 34901, max: 42100 }, { name: '高级', min: 42101, max: 47500 }] },
            { level: 'Lv.7 律令绘爵', totalReq: 26000, stages: [{ name: '初级', min: 47501, max: 55300 }, { name: '中级', min: 55301, max: 65700 }, { name: '高级', min: 65701, max: 73500 }] },
            { level: 'Lv.8 幻构师', totalReq: 36000, stages: [{ name: '初级', min: 73501, max: 84300 }, { name: '中级', min: 84301, max: 98700 }, { name: '高级', min: 98701, max: 109500 }] }
        ];
        const TRUTH_KNOWLEDGE_LEVELS = [
            { level: 'LV.1 灰袍学徒', totalReq: 150, stages: [{ name: '初级', min: 1, max: 30 }, { name: '中级', min: 31, max: 75 }, { name: '高级', min: 76, max: 150 }] },
            { level: 'LV.2 白袍向导', totalReq: 500, stages: [{ name: '初级', min: 151, max: 250 }, { name: '中级', min: 251, max: 400 }, { name: '高级', min: 401, max: 650 }] },
            { level: 'LV.3 墨衣学者', totalReq: 1500, stages: [{ name: '初级', min: 651, max: 950 }, { name: '中级', min: 951, max: 1400 }, { name: '高级', min: 1401, max: 2150 }] },
            { level: 'LV.4 青衿贤者', totalReq: 4000, stages: [{ name: '初级', min: 2151, max: 2950 }, { name: '中级', min: 2951, max: 4150 }, { name: '高级', min: 4151, max: 6150 }] },
            { level: 'LV.5 玄冕宗师', totalReq: 10000, stages: [{ name: '初级', min: 6151, max: 8150 }, { name: '中级', min: 8151, max: 11150 }, { name: '高级', min: 11151, max: 16150 }] }
        ];
        const TRUTH_INTELLIGENCE_LEVELS = [
            { level: 'LV.1 褐衣明理', totalReq: 150, stages: [{ name: '初级', min: 1, max: 30 }, { name: '中级', min: 31, max: 75 }, { name: '高级', min: 76, max: 150 }] },
            { level: 'LV.2 缁衣慎思', totalReq: 500, stages: [{ name: '初级', min: 151, max: 250 }, { name: '中级', min: 251, max: 400 }, { name: '高级', min: 401, max: 650 }] },
            { level: 'LV.3 朱衣审辩', totalReq: 1500, stages: [{ name: '初级', min: 651, max: 950 }, { name: '中级', min: 951, max: 1400 }, { name: '高级', min: 1401, max: 2150 }] },
            { level: 'LV.4 紫绶格物', totalReq: 4000, stages: [{ name: '初级', min: 2151, max: 2950 }, { name: '中级', min: 2951, max: 4150 }, { name: '高级', min: 4151, max: 6150 }] },
            { level: 'LV.5 金章弘道', totalReq: 10000, stages: [{ name: '初级', min: 6151, max: 8150 }, { name: '中级', min: 8151, max: 11150 }, { name: '高级', min: 11151, max: 16150 }] }
        ];

        const TITLES_DAWN = [
            { level: 1, name: '星辉学徒', reqDays: 7, reward: { type: 'intelligenceEfficiency', value: 0.05 } },
            { level: 2, name: '晨风哨卫', reqDays: 30, reward: { type: 'knowledgeEfficiency', value: 0.05, extra: { type: 'intelligenceEfficiency', value: 0.05 } } },
            { level: 3, name: '夜穹守誓', reqDays: 60, reward: { type: 'allAttributesEfficiency', value: 0.05 } },
            { level: 4, name: '破晓骑士', reqDays: 90, reward: { type: 'allAttributesEfficiency', value: 0.10 } },
            { level: 5, name: '黎明星使', reqDays: 120, reward: { type: 'allAttributesEfficiency', value: 0.15 } },
            { level: 6, name: '永夜圣者', reqDays: 180, reward: { type: 'allAttributesEfficiency', value: 0.20 } },
            { level: 7, name: '晨曦领主', reqDays: 365, reward: { type: 'allAttributesEfficiency', value: 0.25 } },
            { level: 8, name: '时序主宰', reqDays: 730, reward: { type: 'allAttributesEfficiency', value: 0.30 } }
        ];
        const TITLES_READ_EXP = [
            { level: 1, name: '历尘星火', req: 200, reward: { type: 'intelligenceEfficiency', value: 0.05 } },
            { level: 2, name: '历溪观澜', req: 1000, reward: { type: 'knowledgeEfficiency', value: 0.05, extra: { type: 'intelligenceEfficiency', value: 0.05 } } },
            { level: 3, name: '历卷拓荒', req: 3000, reward: { type: 'allAttributesEfficiency', value: 0.05 } },
            { level: 4, name: '历镜寻真', req: 6000, reward: { type: 'allAttributesEfficiency', value: 0.10 } },
            { level: 5, name: '历川归海', req: 10000, reward: { type: 'allAttributesEfficiency', value: 0.15 } },
            { level: 6, name: '历世洞明', req: 15000, reward: { type: 'allAttributesEfficiency', value: 0.20 } },
            { level: 7, name: '历界织识', req: 25000, reward: { type: 'allAttributesEfficiency', value: 0.25 } },
            { level: 8, name: '历象归藏', req: 50000, reward: { type: 'allAttributesEfficiency', value: 0.30 } }
        ];
        const TITLES_WILL = [
            { level: 1, name: '晨曦微志', req: 50, reward: { type: 'charismaEfficiency', value: 0.05 } },
            { level: 2, name: '坚石守心', req: 200, reward: { type: 'charismaEfficiency', value: 0.10 } },
            { level: 3, name: '荆棘先锋', req: 500, reward: { type: 'charismaEfficiency', value: 0.15 } },
            { level: 4, name: '钢铁铸意', req: 800, reward: { type: 'charismaEfficiency', value: 0.20 } },
            { level: 5, name: '风暴不屈', req: 1200, reward: { type: 'charismaEfficiency', value: 0.25 } },
            { level: 6, name: '星辰恒志', req: 2000, reward: { type: 'charismaEfficiency', value: 0.30 } },
            { level: 7, name: '炽魂永燃', req: 3000, reward: { type: 'charismaEfficiency', value: 0.40 } },
            { level: 8, name: '无朽之心', req: 5000, reward: { type: 'charismaEfficiency', value: 0.50 } }
        ];
        const TITLES_CHARISMA = [
            { level: 1, name: '萤火微光', req: 10, reward: { type: 'allAttributesEfficiency', value: 0.05 } },
            { level: 2, name: '晨露流辉', req: 50, reward: { type: 'allAttributesEfficiency', value: 0.10 } },
            { level: 3, name: '星芒初绽', req: 100, reward: { type: 'allAttributesEfficiency', value: 0.15 } },
            { level: 4, name: '银月颂光', req: 200, reward: { type: 'allAttributesEfficiency', value: 0.20 } },
            { level: 5, name: '日冕凝华', req: 300, reward: { type: 'allAttributesEfficiency', value: 0.25 } },
            { level: 6, name: '虹彩冠冕', req: 500, reward: { type: 'allAttributesEfficiency', value: 0.30 } },
            { level: 7, name: '天穹律光', req: 800, reward: { type: 'allAttributesEfficiency', value: 0.40 } },
            { level: 8, name: '万象圣辉', req: 1200, reward: { type: 'allAttributesEfficiency', value: 0.50 } }
        ];

        // 当前选中标签
        let currentTab = 'dailyRecordTab';
        // 分页设置
        const PAGE_SIZE = 10;
        let dailyHistoryPage = 1, dawnHistoryPage = 1, summaryHistoryPage = 1, overallHistoryPage = 1;

        // 图表实例
        let basicAttrChart = null;
        let artExpChart = null;

        // 图表配置
        const chartConfig = {
            basicAttr: {
                type: 'line',
                range: 'day',
                mode: 'delta', // 新增：delta（增量）或 total（总量）
                data: {
                    labels: [],
                    datasets: [
                        { label: '智力', data: [], borderColor: '#FF6384', fill: false },
                        { label: '知识', data: [], borderColor: '#36A2EB', fill: false },
                        { label: '阅识', data: [], borderColor: '#FFCE56', fill: false },
                        { label: '体力', data: [], borderColor: '#4BC0C0', fill: false },
                        { label: '意志', data: [], borderColor: '#9966FF', fill: false },
                        { label: '魅力', data: [], borderColor: '#FF9F40', fill: false }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { beginAtZero: true },
                        x: { display: true }
                    }
                }
            },
            artExp: {
                type: 'line',
                range: 'day',
                mode: 'delta', // 新增：delta（增量）或 total（总量）
                data: {
                    labels: [],
                    datasets: [{
                        label: '幻构师经验',
                        data: [],
                        borderColor: '#4facfe',
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { beginAtZero: true },
                        x: { display: true }
                    }
                }
            }
        };

        // 图表数据处理函数
        function processChartData(range = 'day', type = 'basicAttr') {
            let now = new Date();
            let currentYear = now.getFullYear();
            let currentMonth = now.getMonth();
            let labels = [];
            let datasets = [];
            let config = chartConfig[type];
            let mode = config.mode || 'delta';

            // 初始化数据集
            if (type === 'basicAttr') {
                let suffix = mode === 'total' ? '（总量）' : '（增量）';
                datasets = [
                    { label: '智力' + suffix, data: [] },
                    { label: '知识' + suffix, data: [] },
                    { label: '阅识' + suffix, data: [] },
                    { label: '体力' + suffix, data: [] },
                    { label: '意志' + suffix, data: [] },
                    { label: '魅力' + suffix, data: [] }
                ];
            } else {
                let suffix = mode === 'total' ? '（总量）' : '（增量）';
                datasets = [{ label: '幻构师经验' + suffix, data: [] }];
            }

            // 获取所有日期的属性增量（包括任务系统奖励/惩罚）
            function getDateDeltas(dateStr) {
                let deltas = {
                    intDelta: 0, knowledgeDelta: 0, readExpDelta: 0,
                    staminaDelta: 0, willDelta: 0, charismaDelta: 0, artExpDelta: 0
                };

                // 1. 累加每日记录的增量
                data.dailyRecords.forEach(record => {
                    if (record.date === dateStr) {
                        deltas.intDelta += record.deltas.intDelta;
                        deltas.knowledgeDelta += record.deltas.knowledgeDelta;
                        deltas.readExpDelta += record.deltas.readExpDelta;
                        deltas.staminaDelta += record.deltas.staminaDelta;
                        deltas.willDelta += record.deltas.willDelta;
                        deltas.charismaDelta += record.deltas.charismaDelta;
                        deltas.artExpDelta += record.deltas.artExpDelta;
                    }
                });

                // 2. 累加晨曦之约记录的增量
                data.dawn.history.forEach(record => {
                    if (record.date === dateStr) {
                        deltas.willDelta += record.willDelta || 0;
                        deltas.staminaDelta += record.staminaDelta || 0;
                    }
                });

                // 3. 累加任务系统在该日期完成/失败的奖励/惩罚
                let taskCharismaDelta = 0; // 任务系统直接的魅力奖励/惩罚
                data.tasks.forEach(task => {
                    if (task.completionDate === dateStr) {
                        if (task.status === 'completed') {
                            task.rewards.forEach(rw => {
                                if (rw.type === 'intelligence') deltas.intDelta += rw.value;
                                else if (rw.type === 'knowledge') deltas.knowledgeDelta += rw.value;
                                else if (rw.type === 'readExp') deltas.readExpDelta += rw.value;
                                else if (rw.type === 'stamina') deltas.staminaDelta += rw.value;
                                else if (rw.type === 'will') deltas.willDelta += rw.value;
                                else if (rw.type === 'charisma') taskCharismaDelta += rw.value;
                                else if (rw.type === 'artExp') deltas.artExpDelta += rw.value;
                            });
                        } else if (task.status === 'failed') {
                            task.penalties.forEach(pn => {
                                if (pn.type === 'intelligence') deltas.intDelta -= pn.value;
                                else if (pn.type === 'knowledge') deltas.knowledgeDelta -= pn.value;
                                else if (pn.type === 'readExp') deltas.readExpDelta -= pn.value;
                                else if (pn.type === 'stamina') deltas.staminaDelta -= pn.value;
                                else if (pn.type === 'will') deltas.willDelta -= pn.value;
                                else if (pn.type === 'charisma') taskCharismaDelta -= pn.value;
                                else if (pn.type === 'artExp') deltas.artExpDelta -= pn.value;
                            });
                        }
                    }
                });

                // 4. 计算魅力的正确增量
                // 魅力增量 = 基于其他属性变化的增量 + 任务系统直接的魅力奖励/惩罚
                // 基于其他属性的魅力增量 = 0.1 × (其他属性增量之和) × 效率加成

                // 获取当前日期前的属性状态来计算效率加成
                let tempData = {
                    ...data,
                    dailyRecords: data.dailyRecords.filter(r => r.date < dateStr),
                    dawn: {
                        ...data.dawn,
                        history: data.dawn.history.filter(r => r.date < dateStr)
                    },
                    tasks: data.tasks.filter(t => !t.completionDate || t.completionDate < dateStr)
                };
                let prevAttrs = calculateAttributesUsing(tempData);
                let eff = calculateTitlesEfficiencies(prevAttrs);

                // 基于其他属性变化的魅力增量
                let baseCharismaDelta = 0.1 * (deltas.intDelta + deltas.knowledgeDelta + deltas.readExpDelta + deltas.staminaDelta + deltas.willDelta);
                let charismaDeltaFromOthers = baseCharismaDelta * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));

                // 任务系统直接的魅力奖励/惩罚也需要应用效率加成
                let taskCharismaDeltaWithEfficiency = taskCharismaDelta * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));

                // 总魅力增量 = 基于其他属性的增量 + 任务直接奖励/惩罚
                deltas.charismaDelta = charismaDeltaFromOthers + taskCharismaDeltaWithEfficiency;

                return deltas;
            }

            // 根据不同时间范围处理数据
            switch(range) {
                case 'day':
                    // 当年按天统计
                    // 首先收集所有相关日期
                    let allDates = new Set();
                    data.dailyRecords.forEach(record => {
                        let recordDate = new Date(record.date);
                        if (recordDate.getFullYear() === currentYear) {
                            allDates.add(record.date);
                        }
                    });
                    data.dawn.history.forEach(record => {
                        let recordDate = new Date(record.date);
                        if (recordDate.getFullYear() === currentYear) {
                            allDates.add(record.date);
                        }
                    });
                    data.tasks.forEach(task => {
                        if (task.completionDate) {
                            let recordDate = new Date(task.completionDate);
                            if (recordDate.getFullYear() === currentYear) {
                                allDates.add(task.completionDate);
                            }
                        }
                    });

                    // 按日期排序并处理数据
                    let sortedDates = Array.from(allDates).sort();
                    let cumulativeAttrs = { int: 0, knowledge: 0, readExp: 0, stamina: 0, will: 0, charisma: 0, artExp: 0 };

                    sortedDates.forEach(dateStr => {
                        let deltas = getDateDeltas(dateStr);
                        labels.push(dateStr);

                        if (mode === 'total') {
                            // 累积总量模式
                            cumulativeAttrs.int += deltas.intDelta;
                            cumulativeAttrs.knowledge += deltas.knowledgeDelta;
                            cumulativeAttrs.readExp += deltas.readExpDelta;
                            cumulativeAttrs.stamina += deltas.staminaDelta;
                            cumulativeAttrs.will += deltas.willDelta;
                            cumulativeAttrs.charisma += deltas.charismaDelta;
                            cumulativeAttrs.artExp += deltas.artExpDelta;

                            if (type === 'basicAttr') {
                                datasets[0].data.push(cumulativeAttrs.int);
                                datasets[1].data.push(cumulativeAttrs.knowledge);
                                datasets[2].data.push(cumulativeAttrs.readExp);
                                datasets[3].data.push(cumulativeAttrs.stamina);
                                datasets[4].data.push(cumulativeAttrs.will);
                                datasets[5].data.push(cumulativeAttrs.charisma);
                            } else {
                                datasets[0].data.push(cumulativeAttrs.artExp);
                            }
                        } else {
                            // 增量模式
                            if (type === 'basicAttr') {
                                datasets[0].data.push(deltas.intDelta);
                                datasets[1].data.push(deltas.knowledgeDelta);
                                datasets[2].data.push(deltas.readExpDelta);
                                datasets[3].data.push(deltas.staminaDelta);
                                datasets[4].data.push(deltas.willDelta);
                                datasets[5].data.push(deltas.charismaDelta);
                            } else {
                                datasets[0].data.push(deltas.artExpDelta);
                            }
                        }
                    });
                    break;

                case 'month':
                    // 当年按月统计
                    for (let m = 0; m <= currentMonth; m++) {
                        labels.push(`${currentYear}-${String(m + 1).padStart(2, '0')}`);
                        datasets.forEach(ds => ds.data.push(0));
                    }

                    // 收集当年所有有数据的日期
                    let currentYearDates = new Set();
                    data.dailyRecords.forEach(record => {
                        let recordDate = new Date(record.date);
                        if (recordDate.getFullYear() === currentYear) {
                            currentYearDates.add(record.date);
                        }
                    });
                    data.dawn.history.forEach(record => {
                        let recordDate = new Date(record.date);
                        if (recordDate.getFullYear() === currentYear) {
                            currentYearDates.add(record.date);
                        }
                    });
                    data.tasks.forEach(task => {
                        if (task.completionDate) {
                            let recordDate = new Date(task.completionDate);
                            if (recordDate.getFullYear() === currentYear) {
                                currentYearDates.add(task.completionDate);
                            }
                        }
                    });

                    // 按日期统计并累加到对应月份
                    currentYearDates.forEach(dateStr => {
                        let recordDate = new Date(dateStr);
                        let monthIdx = recordDate.getMonth();
                        let deltas = getDateDeltas(dateStr);

                        if (type === 'basicAttr') {
                            datasets[0].data[monthIdx] += deltas.intDelta;
                            datasets[1].data[monthIdx] += deltas.knowledgeDelta;
                            datasets[2].data[monthIdx] += deltas.readExpDelta;
                            datasets[3].data[monthIdx] += deltas.staminaDelta;
                            datasets[4].data[monthIdx] += deltas.willDelta;
                            datasets[5].data[monthIdx] += deltas.charismaDelta;
                        } else {
                            datasets[0].data[monthIdx] += deltas.artExpDelta;
                        }
                    });
                    break;

                case 'month-history':
                    // 历史按月统计
                    let monthData = {};

                    // 收集所有有数据的日期
                    let allHistoryDates = new Set();
                    data.dailyRecords.forEach(record => allHistoryDates.add(record.date));
                    data.dawn.history.forEach(record => allHistoryDates.add(record.date));
                    data.tasks.forEach(task => {
                        if (task.completionDate) allHistoryDates.add(task.completionDate);
                    });

                    // 按日期统计并累加到对应年月
                    allHistoryDates.forEach(dateStr => {
                        let yearMonth = dateStr.substr(0, 7);
                        if (!monthData[yearMonth]) {
                            monthData[yearMonth] = type === 'basicAttr' ?
                                {int: 0, knowledge: 0, readExp: 0, stamina: 0, will: 0, charisma: 0} :
                                {artExp: 0};
                        }

                        let deltas = getDateDeltas(dateStr);
                        if (type === 'basicAttr') {
                            monthData[yearMonth].int += deltas.intDelta;
                            monthData[yearMonth].knowledge += deltas.knowledgeDelta;
                            monthData[yearMonth].readExp += deltas.readExpDelta;
                            monthData[yearMonth].stamina += deltas.staminaDelta;
                            monthData[yearMonth].will += deltas.willDelta;
                            monthData[yearMonth].charisma += deltas.charismaDelta;
                        } else {
                            monthData[yearMonth].artExp += deltas.artExpDelta;
                        }
                    });

                    labels = Object.keys(monthData).sort();
                    if (type === 'basicAttr') {
                        labels.forEach(yearMonth => {
                            datasets[0].data.push(monthData[yearMonth].int);
                            datasets[1].data.push(monthData[yearMonth].knowledge);
                            datasets[2].data.push(monthData[yearMonth].readExp);
                            datasets[3].data.push(monthData[yearMonth].stamina);
                            datasets[4].data.push(monthData[yearMonth].will);
                            datasets[5].data.push(monthData[yearMonth].charisma);
                        });
                    } else {
                        labels.forEach(yearMonth => {
                            datasets[0].data.push(monthData[yearMonth].artExp);
                        });
                    }
                    break;

                case 'year':
                    // 按年统计
                    let yearData = {};

                    // 收集所有有数据的日期
                    let allYearDates = new Set();
                    data.dailyRecords.forEach(record => allYearDates.add(record.date));
                    data.dawn.history.forEach(record => allYearDates.add(record.date));
                    data.tasks.forEach(task => {
                        if (task.completionDate) allYearDates.add(task.completionDate);
                    });

                    // 按日期统计并累加到对应年份
                    allYearDates.forEach(dateStr => {
                        let year = dateStr.substr(0, 4);
                        if (!yearData[year]) {
                            yearData[year] = type === 'basicAttr' ?
                                {int: 0, knowledge: 0, readExp: 0, stamina: 0, will: 0, charisma: 0} :
                                {artExp: 0};
                        }

                        let deltas = getDateDeltas(dateStr);
                        if (type === 'basicAttr') {
                            yearData[year].int += deltas.intDelta;
                            yearData[year].knowledge += deltas.knowledgeDelta;
                            yearData[year].readExp += deltas.readExpDelta;
                            yearData[year].stamina += deltas.staminaDelta;
                            yearData[year].will += deltas.willDelta;
                            yearData[year].charisma += deltas.charismaDelta;
                        } else {
                            yearData[year].artExp += deltas.artExpDelta;
                        }
                    });

                    labels = Object.keys(yearData).sort();
                    if (type === 'basicAttr') {
                        labels.forEach(year => {
                            datasets[0].data.push(yearData[year].int);
                            datasets[1].data.push(yearData[year].knowledge);
                            datasets[2].data.push(yearData[year].readExp);
                            datasets[3].data.push(yearData[year].stamina);
                            datasets[4].data.push(yearData[year].will);
                            datasets[5].data.push(yearData[year].charisma);
                        });
                    } else {
                        labels.forEach(year => {
                            datasets[0].data.push(yearData[year].artExp);
                        });
                    }
                    break;
            }

            return { labels, datasets };
        }

        // 切换图表模式（增量/总量）
        function toggleChartMode(chartType, mode) {
            let config = chartConfig[chartType];
            config.mode = mode;

            // 更新按钮状态
            let deltaBtn = document.getElementById(chartType + 'DeltaBtn');
            let totalBtn = document.getElementById(chartType + 'TotalBtn');

            if (mode === 'delta') {
                deltaBtn.classList.add('active');
                totalBtn.classList.remove('active');
            } else {
                totalBtn.classList.add('active');
                deltaBtn.classList.remove('active');
            }

            // 重新渲染图表
            let chartInstance = chartType === 'basicAttr' ? basicAttrChart : artExpChart;
            updateChart(chartType, chartInstance);
        }

        // 更新图表
        function updateChart(type, chartInstance) {
            let config = chartConfig[type];
            let { labels, datasets } = processChartData(config.range, type);

            if (chartInstance) {
                chartInstance.data.labels = labels;
                chartInstance.data.datasets = datasets;
                chartInstance.update();
            }
        }

        // 初始化图表
        function initCharts() {
            if (basicAttrChart) basicAttrChart.destroy();
            if (artExpChart) artExpChart.destroy();

            let basicAttrCtx = document.getElementById('basicAttrChart').getContext('2d');
            let artExpCtx = document.getElementById('artExpChart').getContext('2d');

            basicAttrChart = new Chart(basicAttrCtx, {
                type: chartConfig.basicAttr.type,
                data: chartConfig.basicAttr.data,
                options: chartConfig.basicAttr.options
            });

            artExpChart = new Chart(artExpCtx, {
                type: chartConfig.artExp.type,
                data: chartConfig.artExp.data,
                options: chartConfig.artExp.options
            });

            updateChart('basicAttr', basicAttrChart);
            updateChart('artExp', artExpChart);
        }

        // 切换图表类型和时间范围
        document.addEventListener('DOMContentLoaded', function() {
            // 图表类型切换
            document.querySelectorAll('[data-chart]').forEach(btn => {
                btn.addEventListener('click', function() {
                    let chartType = this.dataset.chart;
                    let target = this.dataset.target;
                    let config = chartConfig[target];

                    // 更新按钮状态
                    this.parentElement.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 更新图表类型
                    config.type = chartType;
                    let chart = target === 'basicAttr' ? basicAttrChart : artExpChart;
                    if (chart) {
                        chart.destroy();
                        let ctx = document.getElementById(`${target}Chart`).getContext('2d');
                        if (target === 'basicAttr') {
                            basicAttrChart = new Chart(ctx, {
                                type: chartType,
                                data: config.data,
                                options: config.options
                            });
                            updateChart('basicAttr', basicAttrChart);
                        } else {
                            artExpChart = new Chart(ctx, {
                                type: chartType,
                                data: config.data,
                                options: config.options
                            });
                            updateChart('artExp', artExpChart);
                        }
                    }
                });
            });

            // 时间范围切换
            document.querySelectorAll('[data-range]').forEach(btn => {
                btn.addEventListener('click', function() {
                    let range = this.dataset.range;
                    let target = this.dataset.target;
                    let config = chartConfig[target];

                    // 更新按钮状态
                    this.parentElement.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 更新时间范围
                    config.range = range;
                    let chart = target === 'basicAttr' ? basicAttrChart : artExpChart;
                    if (chart) {
                        updateChart(target, chart);
                    }
                });
            });

            // 初始化图表
            initCharts();

            // 每日总记录相关事件监听器
            // 刷新按钮
            if (document.getElementById('refreshOverviewBtn')) {
                document.getElementById('refreshOverviewBtn').addEventListener('click', () => {
                    renderDailyOverview();
                });
            }

            // 详细历史记录切换
            if (document.getElementById('toggleDetailedHistoryBtn')) {
                document.getElementById('toggleDetailedHistoryBtn').addEventListener('click', () => {
                    let container = document.getElementById('detailedHistoryContainer');
                    let isVisible = container.style.display !== 'none';

                    if (isVisible) {
                        container.style.display = 'none';
                        document.getElementById('toggleDetailedHistoryBtn').textContent = '📊 查看详细历史记录';
                    } else {
                        container.style.display = 'block';
                        document.getElementById('toggleDetailedHistoryBtn').textContent = '📊 隐藏详细历史记录';
                        renderDetailedHistory();
                    }
                });
            }

            // 历史记录天数选择器
            if (document.getElementById('historyDaysSelect')) {
                document.getElementById('historyDaysSelect').addEventListener('change', () => {
                    renderDetailedHistory();
                });
            }

            // 任务系统按钮事件委托
            document.addEventListener('click', function(e) {
                // 编辑任务按钮
                if (e.target.classList.contains('editTaskBtn')) {
                    let taskId = parseInt(e.target.dataset.id);
                    openEditTaskModal(taskId);
                }
                // 完成任务按钮
                else if (e.target.classList.contains('complete-btn')) {
                    let taskId = parseInt(e.target.dataset.id);
                    completeTask(taskId);
                }
                // 失败任务按钮
                else if (e.target.classList.contains('fail-btn')) {
                    let taskId = parseInt(e.target.dataset.id);
                    failTask(taskId);
                }
                // 删除任务按钮
                else if (e.target.classList.contains('delete-btn')) {
                    let taskId = parseInt(e.target.dataset.id);
                    deleteTask(taskId);
                }
            });

            // 阶段性总结切换按钮事件处理
            document.getElementById('basicAttrDeltaBtn').addEventListener('click', function() {
                toggleChartMode('basicAttr', 'delta');
            });
            document.getElementById('basicAttrTotalBtn').addEventListener('click', function() {
                toggleChartMode('basicAttr', 'total');
            });
            document.getElementById('artExpDeltaBtn').addEventListener('click', function() {
                toggleChartMode('artExp', 'delta');
            });
            document.getElementById('artExpTotalBtn').addEventListener('click', function() {
                toggleChartMode('artExp', 'total');
            });
        });

        // 在数据更新时重新渲染图表
        function renderAll() {
            renderStatus();
            renderDailyHistory();
            renderDawnHistory();
            renderSummaryHistory();
            renderDailyOverview();
            renderArtPlan();
            renderTruthPlan();
            renderTitles();
            renderTasks();
            if (basicAttrChart && artExpChart) {
                updateChart('basicAttr', basicAttrChart);
                updateChart('artExp', artExpChart);
            }
        }

        /*****************************
         * 工具函数：日期与时间处理 *
         *****************************/
        function formatDate(date) {
            let y = date.getFullYear();
            let m = String(date.getMonth() + 1).padStart(2, '0');
            let d = String(date.getDate()).padStart(2, '0');
            return `${y}-${m}-${d}`;
        }
        function formatTimestamp() {
            let now = new Date();
            let y = now.getFullYear();
            let m = String(now.getMonth() + 1).padStart(2, '0');
            let d = String(now.getDate()).padStart(2, '0');
            let hh = String(now.getHours()).padStart(2, '0');
            let mm = String(now.getMinutes()).padStart(2, '0');
            let ss = String(now.getSeconds()).padStart(2, '0');
            return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
        }
        function parseDate(str) {
            let parts = str.split('-');
            return new Date(parts[0], parts[1] - 1, parts[2]);
        }
        function daysBetween(start, end) {
            let msPerDay = 24 * 3600 * 1000;
            let st = Date.UTC(start.getFullYear(), start.getMonth(), start.getDate());
            let en = Date.UTC(end.getFullYear(), end.getMonth(), end.getDate());
            return Math.floor((en - st) / msPerDay) + 1;
        }
        function getPriorDate(dateStr) {
            let d = parseDate(dateStr);
            d.setDate(d.getDate() - 1);
            return formatDate(d);
        }
        function getBeijingTimeString() {
            let now = new Date();
            let utc = now.getTime() + now.getTimezoneOffset() * 60000;
            let bj = new Date(utc + 8 * 3600000);
            let y = bj.getFullYear();
            let m = String(bj.getMonth() + 1).padStart(2, '0');
            let d = String(bj.getDate()).padStart(2, '0');
            let hh = String(bj.getHours()).padStart(2, '0');
            let mm = String(bj.getMinutes()).padStart(2, '0');
            let ss = String(bj.getSeconds()).padStart(2, '0');
            return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
        }
        function updateHeaderTime() {
            document.getElementById('currentDatetime').innerText = getBeijingTimeString();
            let today = new Date();
            let start = parseDate(data.startDate);
            let days = daysBetween(start, today);
            document.getElementById('planDays').innerText = `计划启动天数：${days}`;
        }
        setInterval(updateHeaderTime, 1000);
        updateHeaderTime();

        /*******************************
         * 导入/导出/保存/重置功能 *
         *******************************/
        function saveData() {
            localStorage.setItem('lifeGameData', JSON.stringify(data));
            alert('数据已保存');
        }
        function loadData() {
            let stored = localStorage.getItem('lifeGameData');
            if (stored) {
                data = JSON.parse(stored);
                planStartDate = parseDate(data.startDate);

                // 初始化nextTaskId为现有任务的最大ID + 1
                if (data.tasks && data.tasks.length > 0) {
                    let maxId = Math.max(...data.tasks.map(task => task.id || 0));
                    nextTaskId = maxId + 1;
                } else {
                    nextTaskId = 1;
                }
            }
            clearOperationHistory(); // 清空操作历史
        }
        function exportData() {
            let blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            let url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.href = url;
            a.download = 'lifeGameData.json';
            a.click();
            URL.revokeObjectURL(url);
        }
        function importData(file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let imp = JSON.parse(e.target.result);
                    data = imp;
                    planStartDate = parseDate(data.startDate);
                    clearOperationHistory(); // 清空操作历史
                    renderAll();
                    alert('导入成功');
                } catch {
                    alert('导入失败：格式错误');
                }
            };
            reader.readAsText(file);
        }
        function resetData() {
            if (!confirm('确认重置？所有数据将清空，但会保持当前的计划开始日期设置。')) return;
            localStorage.removeItem('lifeGameData');
            // 保持当前的开始日期设置
            let currentStartDate = data.startDate;
            data = {
                startDate: currentStartDate,
                initial: { intelligence: 0, knowledge: 0, readExp: 0, stamina: 0, will: 0, charisma: 0, artExp: 0 },
                dawn: { consecutiveSuccess: 0, consecutiveFail: 0, history: [] },
                dailyRecords: [], summaries: [], overallRecords: [], tasks: []
            };
            planStartDate = parseDate(currentStartDate);
            clearOperationHistory(); // 清空操作历史
            renderAll();
        }
        document.getElementById('saveBtn').addEventListener('click', saveData);
        document.getElementById('exportBtn').addEventListener('click', exportData);
        document.getElementById('importBtn').addEventListener('click', () => {
            let fi = document.createElement('input');
            fi.type = 'file';
            fi.accept = 'application/json';
            fi.onchange = e => {
                let f = e.target.files[0];
                if (f) importData(f);
            };
            fi.click();
        });
        document.getElementById('resetBtn').addEventListener('click', resetData);

        /*******************************
         * 设置开始日期功能 *
         *******************************/
        function openSetStartDateModal() {
            document.getElementById('inputStartDate').value = data.startDate;
            document.getElementById('currentStartDate').innerText = data.startDate;
            document.getElementById('setStartDateModal').style.display = 'flex';
        }

        function setStartDate() {
            let newStartDate = document.getElementById('inputStartDate').value;
            if (!newStartDate) {
                alert('请选择开始日期');
                return;
            }

            data.startDate = newStartDate;
            planStartDate = parseDate(newStartDate);
            saveData();
            renderAll();
            document.getElementById('setStartDateModal').style.display = 'none';
            alert('开始日期已更新');
        }

        document.getElementById('setStartDateBtn').addEventListener('click', openSetStartDateModal);
        document.getElementById('saveStartDateBtn').addEventListener('click', setStartDate);
        document.getElementById('setStartDateClose').addEventListener('click', () => {
            document.getElementById('setStartDateModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('setStartDateModal')) {
                document.getElementById('setStartDateModal').style.display = 'none';
            }
        });

        /*******************************
         * 计算称号加成比例 *
         *******************************/
        function calculateTitlesEfficiencies(attrs) {
            let eff = {
                intelligenceEfficiency: 0,
                knowledgeEfficiency: 0,
                allAttributesEfficiency: 0,
                charismaEfficiency: 0
            };
            let cs = data.dawn.consecutiveSuccess;
            TITLES_DAWN.forEach(t => {
                if (cs >= t.reqDays) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                    else eff[r.type] += r.value;
                    if (r.extra) {
                        eff[r.extra.type] += r.extra.value;
                    }
                }
            });
            let re = attrs.readExp;
            TITLES_READ_EXP.forEach(t => {
                if (re >= t.req) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                    else eff[r.type] += r.value;
                    if (r.extra) {
                        eff[r.extra.type] += r.extra.value;
                    }
                }
            });
            let wi = attrs.will;
            TITLES_WILL.forEach(t => {
                if (wi >= t.req) {
                    let r = t.reward;
                    if (r.type === 'charismaEfficiency') eff.charismaEfficiency += r.value;
                }
            });
            let cha = attrs.charisma;
            TITLES_CHARISMA.forEach(t => {
                if (cha >= t.req) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                }
            });
            return eff;
        }

        /***********************************
         * 计算所有属性（含任务惩奖励） *
         ***********************************/
        function calculateAttributes() {
            let attrs = {
                intelligence: data.initial.intelligence,
                knowledge: data.initial.knowledge,
                readExp: data.initial.readExp,
                stamina: data.initial.stamina,
                will: data.initial.will,
                charisma: data.initial.charisma,
                artExp: data.initial.artExp
            };

            // 1. 累加“每日记录”里每条 rec.deltas
            data.dailyRecords.forEach(rec => {
                attrs.artExp += rec.deltas.artExpDelta;
                attrs.intelligence += rec.deltas.intDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.readExp += rec.deltas.readExpDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.charisma += rec.deltas.charismaDelta;
            });

            // 2. 累加 “晨曦之约打卡” 中每条打卡带来的 willDelta/staminaDelta
            data.dawn.history.forEach(rec => {
                attrs.will += rec.willDelta;
                attrs.stamina += rec.staminaDelta;
            });

            // 3. 任务系统：检查超期→失败，并累加奖励/惩罚
            let todayStr = formatDate(new Date());
            data.tasks.forEach(task => {
                if (task.status === 'pending' && task.deadline) {
                    let dl = parseDate(task.deadline);
                    let td = parseDate(todayStr);
                    if (td > dl) {
                        task.status = 'failed';
                        task.completionDate = todayStr;
                    }
                }
            });
            data.tasks.forEach(task => {
                if (task.status === 'completed') {
                    task.rewards.forEach(rw => {
                        if (['intelligence','knowledge','readExp','stamina','will','charisma','artExp'].includes(rw.type)) {
                            attrs[rw.type] += rw.value;
                        }
                    });
                }
                if (task.status === 'failed') {
                    task.penalties.forEach(pn => {
                        if (['intelligence','knowledge','readExp','stamina','will','charisma','artExp'].includes(pn.type)) {
                            attrs[pn.type] -= pn.value;
                        }
                    });
                }
            });

            // 4. 叠加“称号加成”对魅力之类的影响
            let eff = calculateTitlesEfficiencies(attrs);
            let rawCha = 0.1 * (attrs.intelligence + attrs.knowledge + attrs.readExp + attrs.stamina + attrs.will);
            attrs.charisma = rawCha * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));

            return attrs;
        }

        /***********************************
         * 渲染 “当前状态” 相关逻辑 *
         ***********************************/
        function calculateCareerLevels(attrs) {
            // 幻构师
            let artExp = attrs.artExp;
            let artLevelStr = 'Lv.1 描形学徒（初级）';
            for (let lvlInfo of ART_LEVELS) {
                if (artExp <= lvlInfo.totalReq) {
                    let stageName = '未达成';
                    lvlInfo.stages.forEach(st => {
                        if (artExp >= st.min && artExp <= st.max) stageName = st.name;
                    });
                    artLevelStr = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (artExp > ART_LEVELS[ART_LEVELS.length - 1].totalReq) {
                artLevelStr = `Lv.8 幻构师（高级）`;
            }
            // 真理之路 知识侧
            let kn = attrs.knowledge;
            let truthKn = 'LV.1 灰袍学徒（初级）';
            for (let lvlInfo of TRUTH_KNOWLEDGE_LEVELS) {
                if (kn <= lvlInfo.totalReq) {
                    let stageName = '未达成';
                    lvlInfo.stages.forEach(st => {
                        if (kn >= st.min && kn <= st.max) stageName = st.name;
                    });
                    truthKn = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (kn > TRUTH_KNOWLEDGE_LEVELS[TRUTH_KNOWLEDGE_LEVELS.length - 1].totalReq) {
                truthKn = `LV.5 玄冕宗师（高级）`;
            }
            // 真理之路 智力侧
            let it = attrs.intelligence;
            let truthIt = 'LV.1 褐衣明理（初级）';
            for (let lvlInfo of TRUTH_INTELLIGENCE_LEVELS) {
                if (it <= lvlInfo.totalReq) {
                    let stageName = '未达成';
                    lvlInfo.stages.forEach(st => {
                        if (it >= st.min && it <= st.max) stageName = st.name;
                    });
                    truthIt = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (it > TRUTH_INTELLIGENCE_LEVELS[TRUTH_INTELLIGENCE_LEVELS.length - 1].totalReq) {
                truthIt = `LV.5 金章弘道（高级）`;
            }
            return { artLevelStr, truthKn, truthIt };
        }

        function calculateTitlesDisplay(attrs) {
            let display = { dawn: '无', readExp: '无', will: '无', charisma: '无' };
            let cs = data.dawn.consecutiveSuccess;
            for (let i = TITLES_DAWN.length - 1; i >= 0; i--) {
                if (cs >= TITLES_DAWN[i].reqDays) {
                    display.dawn = TITLES_DAWN[i].name;
                    break;
                }
            }
            let re = attrs.readExp;
            for (let i = TITLES_READ_EXP.length - 1; i >= 0; i--) {
                if (re >= TITLES_READ_EXP[i].req) {
                    display.readExp = TITLES_READ_EXP[i].name;
                    break;
                }
            }
            let wi = attrs.will;
            for (let i = TITLES_WILL.length - 1; i >= 0; i--) {
                if (wi >= TITLES_WILL[i].req) {
                    display.will = TITLES_WILL[i].name;
                    break;
                }
            }
            let cha = attrs.charisma;
            for (let i = TITLES_CHARISMA.length - 1; i >= 0; i--) {
                if (cha >= TITLES_CHARISMA[i].req) {
                    display.charisma = TITLES_CHARISMA[i].name;
                    break;
                }
            }
            return display;
        }

        function renderStatus() {
            let attrs = calculateAttributes();

            document.getElementById('attrInt').innerText = attrs.intelligence.toFixed(2);
            document.getElementById('attrKnowledge').innerText = attrs.knowledge.toFixed(2);
            document.getElementById('attrReadExp').innerText = attrs.readExp.toFixed(2);
            document.getElementById('attrStamina').innerText = attrs.stamina.toFixed(2);
            document.getElementById('attrWill').innerText = attrs.will.toFixed(2);
            document.getElementById('attrCharisma').innerText = attrs.charisma.toFixed(2);
            document.getElementById('attrArtExp').innerText = attrs.artExp.toFixed(0);

            let career = calculateCareerLevels(attrs);
            document.getElementById('careerArtLevel').innerText = career.artLevelStr;
            document.getElementById('careerTruthKnowledge').innerText = career.truthKn;
            document.getElementById('careerTruthIntelligence').innerText = career.truthIt;

            let titles = calculateTitlesDisplay(attrs);
            document.getElementById('titleDawn').innerText = titles.dawn;
            document.getElementById('titleReadExp').innerText = titles.readExp;
            document.getElementById('titleWill').innerText = titles.will;
            document.getElementById('titleCharisma').innerText = titles.charisma;
        }

        /******************************
         * 选项卡切换逻辑 *
         ******************************/
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                if (tab.classList.contains('active')) return;
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
                currentTab = tab.dataset.tab;
            });
        });

        /***********************************
         * “每日记录” 功能实现 *
         ***********************************/
        function addDailyRecord() {
            let inputDate = document.getElementById('inputDailyDate').value;
            if (!inputDate) {
                alert('请选择记录日期');
                return;
            }

            // 保存操作前状态
            saveOperationState('add_daily_record', `添加每日记录 (${inputDate})`);

            let date = inputDate;
            let ts = `${date} ${formatTimestamp().substr(11)}`; // 使用输入的日期 + 当前时间

            let artMin = parseInt(document.getElementById('inputArtMinutes').value) || 0;
            let studyMin = parseInt(document.getElementById('inputStudyMinutes').value) || 0;
            let specialReadPages = parseInt(document.getElementById('inputSpecialReadPages').value) || 0;
            let exerciseMin = parseInt(document.getElementById('inputExerciseMinutes').value) || 0;
            let bookReadMin = parseInt(document.getElementById('inputBookReadMinutes').value) || 0;
            let videoWatchMin = parseInt(document.getElementById('inputVideoWatchMinutes').value) || 0;
            let chkWillArt = document.getElementById('chkWillArt').checked;
            let chkWillStudy = document.getElementById('chkWillStudy').checked;
            let chkWillExercise = document.getElementById('chkWillExercise').checked;
            let chkWillBook = document.getElementById('chkWillBook').checked;
            let chkWillVideo = document.getElementById('chkWillVideo').checked;

            // 获取自定义项目数据
            let customItems = getCustomItemsData();

            data.dailyRecords.push({
                timestamp: ts,
                date,
                artMin, studyMin, specialReadPages, exerciseMin, bookReadMin, videoWatchMin,
                chkWillArt, chkWillStudy, chkWillExercise, chkWillBook, chkWillVideo,
                customItems: customItems, // 添加自定义项目数据
                deltas: {
                    artExpDelta: 0,
                    intDelta: 0,
                    knowledgeDelta: 0,
                    readExpDelta: 0,
                    staminaDelta: 0,
                    willDelta: 0,
                    charismaDelta: 0
                },
                titleBonusStr: '',
                consecutive: { art: 0, study: 0, exercise: 0, book: 0, video: 0 },
                consecutiveStatsStr: ''
            });

            recomputeAllDailyRecords();

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            renderAll();

            // 清空表单数值，但保留日期和模板
            document.getElementById('inputArtMinutes').value = 0;
            document.getElementById('inputStudyMinutes').value = 0;
            document.getElementById('inputSpecialReadPages').value = 0;
            document.getElementById('inputExerciseMinutes').value = 0;
            document.getElementById('inputBookReadMinutes').value = 0;
            document.getElementById('inputVideoWatchMinutes').value = 0;

            // 清空意志参与复选框
            document.getElementById('chkWillArt').checked = false;
            document.getElementById('chkWillStudy').checked = false;
            document.getElementById('chkWillExercise').checked = false;
            document.getElementById('chkWillBook').checked = false;
            document.getElementById('chkWillVideo').checked = false;

            // 清空自定义项目的数值，但保留模板
            document.querySelectorAll('.custom-item [data-field="value"]').forEach(input => {
                input.value = 0;
            });
        }
        document.getElementById('addDailyRecordBtn').addEventListener('click', addDailyRecord);

        /***********************************
         * 自定义项目功能实现 *
         ***********************************/
        let customItemsVisible = false;
        let customItemCounter = 0;

        // 切换自定义项目区域显示/隐藏
        document.getElementById('toggleCustomItemsBtn').addEventListener('click', function() {
            const container = document.getElementById('customItemsContainer');
            const btn = document.getElementById('toggleCustomItemsBtn');

            customItemsVisible = !customItemsVisible;
            container.style.display = customItemsVisible ? 'block' : 'none';
            btn.textContent = customItemsVisible ? '收起' : '展开';

            if (customItemsVisible) {
                loadCustomItemsTemplates();
            }
        });

        // 添加自定义项目
        document.getElementById('addCustomItemBtn').addEventListener('click', function() {
            addCustomItem();
        });

        // 添加一个自定义项目输入行
        function addCustomItem(name = '', value = 0, attribute = 'intelligence', rate = 1) {
            const list = document.getElementById('customItemsList');
            const itemId = `customItem_${customItemCounter++}`;

            const itemDiv = document.createElement('div');
            itemDiv.className = 'custom-item';
            itemDiv.dataset.itemId = itemId;

            itemDiv.innerHTML = `
                <div class="custom-item-field">
                    <label>项目名称</label>
                    <input type="text" placeholder="如：跑步、冥想、编程" value="${name}" data-field="name">
                </div>
                <div class="custom-item-field">
                    <label>数值</label>
                    <input type="number" placeholder="数量" min="0" value="${value}" data-field="value">
                </div>
                <div class="custom-item-field">
                    <label>目标属性</label>
                    <select data-field="attribute">
                        <option value="intelligence" ${attribute === 'intelligence' ? 'selected' : ''}>智力</option>
                        <option value="knowledge" ${attribute === 'knowledge' ? 'selected' : ''}>知识</option>
                        <option value="readExp" ${attribute === 'readExp' ? 'selected' : ''}>阅识</option>
                        <option value="stamina" ${attribute === 'stamina' ? 'selected' : ''}>体力</option>
                        <option value="will" ${attribute === 'will' ? 'selected' : ''}>意志</option>
                        <option value="charisma" ${attribute === 'charisma' ? 'selected' : ''}>魅力</option>
                        <option value="artExp" ${attribute === 'artExp' ? 'selected' : ''}>幻构师经验</option>
                    </select>
                </div>
                <div class="custom-item-field">
                    <label>换算率</label>
                    <input type="number" placeholder="如：0.5" min="0" step="0.1" value="${rate}" data-field="rate">
                </div>
                <div class="custom-item-field">
                    <label>&nbsp;</label>
                    <button type="button" class="custom-item-remove" onclick="removeCustomItem('${itemId}')">删除</button>
                </div>
            `;

            list.appendChild(itemDiv);

            // 为输入框添加变化监听，自动保存模板
            itemDiv.querySelectorAll('input, select').forEach(input => {
                input.addEventListener('change', saveCustomItemsTemplates);
                input.addEventListener('input', saveCustomItemsTemplates);
            });

            saveCustomItemsTemplates();
        }

        // 删除自定义项目
        function removeCustomItem(itemId) {
            const item = document.querySelector(`[data-item-id="${itemId}"]`);
            if (item) {
                item.remove();
                saveCustomItemsTemplates();
            }
        }

        // 获取所有自定义项目的数据
        function getCustomItemsData() {
            const items = [];
            document.querySelectorAll('.custom-item').forEach(item => {
                const name = item.querySelector('[data-field="name"]').value.trim();
                const value = parseFloat(item.querySelector('[data-field="value"]').value) || 0;
                const attribute = item.querySelector('[data-field="attribute"]').value;
                const rate = parseFloat(item.querySelector('[data-field="rate"]').value) || 0;

                if (name && value > 0 && rate > 0) {
                    items.push({ name, value, attribute, rate });
                }
            });
            return items;
        }

        // 计算自定义项目的属性增量
        function calculateCustomItemsDeltas(customItems) {
            const deltas = {
                intelligence: 0,
                knowledge: 0,
                readExp: 0,
                stamina: 0,
                will: 0,
                charisma: 0,
                artExp: 0
            };

            customItems.forEach(item => {
                const delta = item.value * item.rate;
                if (deltas.hasOwnProperty(item.attribute)) {
                    deltas[item.attribute] += delta;
                }
            });

            return deltas;
        }

        // 保存自定义项目模板到localStorage
        function saveCustomItemsTemplates() {
            const templates = [];
            document.querySelectorAll('.custom-item').forEach(item => {
                const name = item.querySelector('[data-field="name"]').value.trim();
                const attribute = item.querySelector('[data-field="attribute"]').value;
                const rate = parseFloat(item.querySelector('[data-field="rate"]').value) || 0;

                if (name) {
                    templates.push({ name, attribute, rate });
                }
            });
            localStorage.setItem('customItemsTemplates', JSON.stringify(templates));
        }

        // 从localStorage加载自定义项目模板
        function loadCustomItemsTemplates() {
            const templates = JSON.parse(localStorage.getItem('customItemsTemplates') || '[]');

            // 清空现有项目
            document.getElementById('customItemsList').innerHTML = '';
            customItemCounter = 0;

            // 加载模板
            templates.forEach(template => {
                addCustomItem(template.name, 0, template.attribute, template.rate);
            });

            // 如果没有模板，添加一个空项目
            if (templates.length === 0) {
                addCustomItem();
            }
        }

        function recomputeAllDailyRecords() {
            data.dailyRecords.sort((a,b) => a.timestamp.localeCompare(b.timestamp));
            data.dailyRecords.forEach((rec, idx) => {
                let date = rec.date;
                let artMin = rec.artMin, studyMin = rec.studyMin, specialReadPages = rec.specialReadPages;
                let exerciseMin = rec.exerciseMin, bookReadMin = rec.bookReadMin, videoWatchMin = rec.videoWatchMin;
                let chkWillArt = rec.chkWillArt, chkWillStudy = rec.chkWillStudy;
                let chkWillExercise = rec.chkWillExercise, chkWillBook = rec.chkWillBook, chkWillVideo = rec.chkWillVideo;

                let baseArtExp = artMin * (10 / 60);
                let baseInt = (studyMin / 60) * 1;
                let baseKnowledge = specialReadPages * 0.1;
                let baseReadExp = bookReadMin * 0.1 + (videoWatchMin / 60) * 10;
                let baseStamina = (exerciseMin / 60) * 1;

                let tempData = {
                    ...data,
                    dailyRecords: data.dailyRecords.slice(0, idx),
                    dawn: data.dawn,
                    tasks: data.tasks
                };
                let prevAttrs = calculateAttributesUsing(tempData);
                let eff = calculateTitlesEfficiencies(prevAttrs);

                let artExpDelta = baseArtExp * (1 + (eff.allAttributesEfficiency || 0));
                let intDelta = baseInt * (1 + ((eff.intelligenceEfficiency || 0) + (eff.allAttributesEfficiency || 0)));
                let knowledgeDelta = baseKnowledge * (1 + ((eff.knowledgeEfficiency || 0) + (eff.allAttributesEfficiency || 0)));
                let readExpDelta = baseReadExp * (1 + (eff.allAttributesEfficiency || 0));
                let staminaDelta = baseStamina * (1 + (eff.allAttributesEfficiency || 0));

                // 计算自定义项目的属性增量
                let customDeltas = { intelligence: 0, knowledge: 0, readExp: 0, stamina: 0, will: 0, charisma: 0, artExp: 0 };
                if (rec.customItems && rec.customItems.length > 0) {
                    customDeltas = calculateCustomItemsDeltas(rec.customItems);
                    // 应用称号效率加成
                    artExpDelta += customDeltas.artExp * (1 + (eff.allAttributesEfficiency || 0));
                    intDelta += customDeltas.intelligence * (1 + ((eff.intelligenceEfficiency || 0) + (eff.allAttributesEfficiency || 0)));
                    knowledgeDelta += customDeltas.knowledge * (1 + ((eff.knowledgeEfficiency || 0) + (eff.allAttributesEfficiency || 0)));
                    readExpDelta += customDeltas.readExp * (1 + (eff.allAttributesEfficiency || 0));
                    staminaDelta += customDeltas.stamina * (1 + (eff.allAttributesEfficiency || 0));
                }

                let prevRec = null;
                if (idx > 0 && data.dailyRecords[idx - 1].date === getPriorDate(date)) {
                    prevRec = data.dailyRecords[idx - 1];
                }
                let consecutive = { art: 0, study: 0, exercise: 0, book: 0, video: 0 };
                if (artMin > 0)       consecutive.art = prevRec && prevRec.artMin > 0 ? prevRec.consecutive.art + 1 : 1;
                if (studyMin > 0)     consecutive.study = prevRec && prevRec.studyMin > 0 ? prevRec.consecutive.study + 1 : 1;
                if (exerciseMin > 0)  consecutive.exercise = prevRec && prevRec.exerciseMin > 0 ? prevRec.consecutive.exercise + 1 : 1;
                if (bookReadMin > 0)  consecutive.book = prevRec && prevRec.bookReadMin > 0 ? prevRec.consecutive.book + 1 : 1;
                if (videoWatchMin > 0)consecutive.video = prevRec && prevRec.videoWatchMin > 0 ? prevRec.consecutive.video + 1 : 1;

                let willDelta = 0;
                function calcWill(actionConsec, chk) {
                    if (!chk) return 0;
                    if (actionConsec >= 30) return 3;
                    if (actionConsec >= 7) return 2;
                    if (actionConsec >= 3) return 1;
                    return 0;
                }
                willDelta += calcWill(consecutive.art, chkWillArt);
                willDelta += calcWill(consecutive.study, chkWillStudy);
                willDelta += calcWill(consecutive.exercise, chkWillExercise);
                willDelta += calcWill(consecutive.book, chkWillBook);
                willDelta += calcWill(consecutive.video, chkWillVideo);

                if (prevRec) {
                    // 计算失败惩罚：失败 1 天 –1；连续失败 ≥3 天 每天 –2；连续失败 ≥7 天 每天 –3
                    function calcWillFailurePenalty(actionName, tVal, chk) {
                        if (!chk) return 0; // 如果没有勾选参与意志，则不计算
                        if (tVal > 0) return 0; // 如果今天有做，则没有失败

                        // 今天没做，计算连续失败天数
                        let consecutiveFailDays = 1; // 今天是失败的

                        // 从当前记录往前查找连续失败的天数
                        // 使用已经排序的记录数组，从当前索引往前查找
                        for (let i = idx - 1; i >= 0; i--) {
                            let historyRec = data.dailyRecords[i];

                            // 检查是否是连续的日期
                            let expectedDate = getPriorDate(data.dailyRecords[i + 1].date);
                            if (historyRec.date !== expectedDate) {
                                break; // 日期不连续，停止计算
                            }

                            let recValue = 0;
                            let recChk = false;
                            switch(actionName) {
                                case 'art': recValue = historyRec.artMin; recChk = historyRec.chkWillArt; break;
                                case 'study': recValue = historyRec.studyMin; recChk = historyRec.chkWillStudy; break;
                                case 'exercise': recValue = historyRec.exerciseMin; recChk = historyRec.chkWillExercise; break;
                                case 'book': recValue = historyRec.bookReadMin; recChk = historyRec.chkWillBook; break;
                                case 'video': recValue = historyRec.videoWatchMin; recChk = historyRec.chkWillVideo; break;
                            }

                            if (recChk && recValue === 0) {
                                consecutiveFailDays++;
                            } else {
                                break; // 遇到成功的记录或未参与意志的记录，停止计算
                            }
                        }

                        // 根据连续失败天数计算惩罚
                        if (consecutiveFailDays >= 7) return 3;
                        if (consecutiveFailDays >= 3) return 2;
                        return 1; // 失败1天
                    }

                    willDelta -= calcWillFailurePenalty('art', artMin, chkWillArt);
                    willDelta -= calcWillFailurePenalty('study', studyMin, chkWillStudy);
                    willDelta -= calcWillFailurePenalty('exercise', exerciseMin, chkWillExercise);
                    willDelta -= calcWillFailurePenalty('book', bookReadMin, chkWillBook);
                    willDelta -= calcWillFailurePenalty('video', videoWatchMin, chkWillVideo);
                }
                willDelta = willDelta * (1 + (eff.allAttributesEfficiency || 0));

                // 添加自定义项目的意志力增量
                willDelta += customDeltas.will * (1 + (eff.allAttributesEfficiency || 0));

                let rawCharmGain = 0.1 * (intDelta + knowledgeDelta + readExpDelta + staminaDelta + willDelta);
                let charismaDelta = rawCharmGain * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));

                let titleBonusStr = `称号加成: 智力+${(eff.intelligenceEfficiency*100).toFixed(1)}% 知识+${(eff.knowledgeEfficiency*100).toFixed(1)}% 全体+${(eff.allAttributesEfficiency*100).toFixed(1)}% 魅力+${(eff.charismaEfficiency*100).toFixed(1)}%`;
                let consecutiveStatsStr = `绘画:${consecutive.art}天 学习:${consecutive.study}天 运动:${consecutive.exercise}天 阅书:${consecutive.book}天 阅影:${consecutive.video}天`;

                rec.deltas.artExpDelta = artExpDelta;
                rec.deltas.intDelta = intDelta;
                rec.deltas.knowledgeDelta = knowledgeDelta;
                rec.deltas.readExpDelta = readExpDelta;
                rec.deltas.staminaDelta = staminaDelta;
                rec.deltas.willDelta = willDelta;
                rec.deltas.charismaDelta = charismaDelta;
                rec.titleBonusStr = titleBonusStr;
                rec.consecutive = { ...consecutive };
                rec.consecutiveStatsStr = consecutiveStatsStr;

                let oldTs = rec.timestamp;
                let timePart = oldTs.split(' ')[1];
                rec.timestamp = `${rec.date} ${timePart}`;
            });
        }

        function calculateAttributesUsing(tmpData) {
            let attrs = {
                intelligence: tmpData.initial.intelligence,
                knowledge: tmpData.initial.knowledge,
                readExp: tmpData.initial.readExp,
                stamina: tmpData.initial.stamina,
                will: tmpData.initial.will,
                charisma: tmpData.initial.charisma,
                artExp: tmpData.initial.artExp
            };
            tmpData.dailyRecords.forEach(rec => {
                attrs.artExp += rec.deltas.artExpDelta;
                attrs.intelligence += rec.deltas.intDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.readExp += rec.deltas.readExpDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.charisma += rec.deltas.charismaDelta;
            });
            tmpData.dawn.history.forEach(rec => {
                attrs.will += rec.willDelta;
                attrs.stamina += rec.staminaDelta;
            });
            tmpData.tasks.forEach(task => {
                if (task.status === 'completed') {
                    task.rewards.forEach(rw => {
                        if (['intelligence','knowledge','readExp','stamina','will','charisma','artExp'].includes(rw.type)) {
                            attrs[rw.type] += rw.value;
                        }
                    });
                }
                if (task.status === 'failed') {
                    task.penalties.forEach(pn => {
                        if (['intelligence','knowledge','readExp','stamina','will','charisma','artExp'].includes(pn.type)) {
                            attrs[pn.type] -= pn.value;
                        }
                    });
                }
            });
            let eff = calculateTitlesEfficiencies(attrs);
            let rawCha = 0.1 * (attrs.intelligence + attrs.knowledge + attrs.readExp + attrs.stamina + attrs.will);
            attrs.charisma = rawCha * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));
            return attrs;
        }

        function renderDailyHistory() {
            let container = document.getElementById('dailyHistoryCards');
            container.innerHTML = '';

            // 获取排序方式
            let sortOrder = document.getElementById('dailyHistorySortSelect').value;
            let recs = data.dailyRecords.slice();

            // 根据排序方式排序
            if (sortOrder === 'desc') {
                recs.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
            } else {
                recs.sort((a, b) => a.timestamp.localeCompare(b.timestamp));
            }
            let totalPages = Math.ceil(recs.length / PAGE_SIZE);
            if (dailyHistoryPage > totalPages) dailyHistoryPage = totalPages || 1;
            let start = (dailyHistoryPage - 1) * PAGE_SIZE;
            let pageRecs = recs.slice(start, start + PAGE_SIZE);

            pageRecs.forEach(rec => {
                // 生成自定义项目显示文本
                let customItemsStr = '';
                if (rec.customItems && rec.customItems.length > 0) {
                    customItemsStr = rec.customItems.map(item =>
                        `${item.name}: ${item.value} × ${item.rate}`
                    ).join('<br>');
                }

                // 格式化数值显示
                function formatDelta(value) {
                    if (value === 0) return { text: '0', class: 'zero' };
                    if (value > 0) return { text: `+${value.toFixed(1)}`, class: 'positive' };
                    return { text: value.toFixed(1), class: 'negative' };
                }

                let artExpDelta = formatDelta(rec.deltas.artExpDelta);
                let intDelta = formatDelta(rec.deltas.intDelta);
                let knowledgeDelta = formatDelta(rec.deltas.knowledgeDelta);
                let readExpDelta = formatDelta(rec.deltas.readExpDelta);
                let staminaDelta = formatDelta(rec.deltas.staminaDelta);
                let willDelta = formatDelta(rec.deltas.willDelta);
                let charismaDelta = formatDelta(rec.deltas.charismaDelta);

                let card = document.createElement('div');
                card.className = 'daily-record-card';
                card.innerHTML = `
                    <div class="record-header">
                        <div class="record-date">📅 ${rec.date}</div>
                        <div class="record-actions">
                            <button class="editDailyBtn" data-ts="${rec.timestamp}">编辑</button>
                            <button class="deleteDailyBtn" data-ts="${rec.timestamp}">删除</button>
                        </div>
                    </div>
                    <div class="record-content">
                        <div class="record-section">
                            <div class="section-title">📊 活动记录</div>
                            <div class="section-content">
                                <div class="data-item">
                                    <div class="data-label">学习</div>
                                    <div class="data-value">${rec.studyMin || 0}分</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">专著</div>
                                    <div class="data-value">${rec.specialReadPages || 0}页</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">绘画</div>
                                    <div class="data-value">${rec.artMin || 0}分</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">阅书</div>
                                    <div class="data-value">${rec.bookReadMin || 0}页</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">阅影</div>
                                    <div class="data-value">${rec.videoWatchMin || 0}分</div>
                                </div>
                                ${customItemsStr ? `
                                <div class="data-item full-width">
                                    <div class="data-label">自定义项目</div>
                                    <div class="data-value">${customItemsStr}</div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="record-section">
                            <div class="section-title">📈 属性增量</div>
                            <div class="section-content">
                                <div class="data-item">
                                    <div class="data-label">经验</div>
                                    <div class="data-value ${artExpDelta.class}">${artExpDelta.text}</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">智力</div>
                                    <div class="data-value ${intDelta.class}">${intDelta.text}</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">知识</div>
                                    <div class="data-value ${knowledgeDelta.class}">${knowledgeDelta.text}</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">阅识</div>
                                    <div class="data-value ${readExpDelta.class}">${readExpDelta.text}</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">体力</div>
                                    <div class="data-value ${staminaDelta.class}">${staminaDelta.text}</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">意志</div>
                                    <div class="data-value ${willDelta.class}">${willDelta.text}</div>
                                </div>
                                <div class="data-item">
                                    <div class="data-label">魅力</div>
                                    <div class="data-value ${charismaDelta.class}">${charismaDelta.text}</div>
                                </div>
                            </div>
                        </div>
                        <div class="record-section full-width">
                            <div class="section-title">🏆 称号加成</div>
                            <div class="info-text">${rec.titleBonusStr || '无称号加成'}</div>
                        </div>
                        <div class="record-section full-width">
                            <div class="section-title">🔥 连续统计</div>
                            <div class="info-text">${rec.consecutiveStatsStr || '无连续记录'}</div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
            let pag = document.getElementById('dailyHistoryPagination');
            pag.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === dailyHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => { dailyHistoryPage = i; renderDailyHistory(); });
                pag.appendChild(btn);
            }
            document.querySelectorAll('.deleteDailyBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    let ts = btn.dataset.ts;
                    if (!confirm(`删除此条记录？`)) return;

                    // 保存操作前状态
                    saveOperationState('delete_daily_record', `删除每日记录 (${ts.substr(0,10)})`);

                    data.dailyRecords = data.dailyRecords.filter(r => r.timestamp !== ts);
                    data.overallRecords = data.overallRecords.filter(r => r.date !== ts.substr(0,10));

                    // 保存数据到本地存储
                    localStorage.setItem('lifeGameData', JSON.stringify(data));

                    renderAll();
                });
            });
            document.querySelectorAll('.editDailyBtn').forEach(btn => {
                btn.addEventListener('click', () => openEditDailyModal(btn.dataset.ts));
            });
        }
        document.getElementById('toggleDailyHistoryBtn').addEventListener('click', () => {
            let c = document.getElementById('dailyHistoryContainer');
            c.style.display = c.style.display === 'none' ? 'block' : 'none';
            if (c.style.display === 'block') renderDailyHistory();
        });
        document.getElementById('dailyHistoryContainer').style.display = 'none';

        /***********************************
         * “晨曦之约计划” 功能实现 *
         ***********************************/
        function addDawnRecord() {
            let date = formatDate(new Date());
            if (data.dawn.history.some(r => r.date === date)) {
                alert('今日已打卡，若要修改请删除后重试');
                return;
            }

            // 保存操作前状态
            saveOperationState('add_dawn_record', `添加晨曦之约记录 (${date})`);
            let sleptOnTime = document.getElementById('chkSleptOnTime').checked;
            let wokeOnTime = document.getElementById('chkWokeOnTime').checked;
            let specialCase = document.getElementById('chkSpecialCase').checked;
            let earlySleep = document.getElementById('chkEarlySleep').checked;
            let earlyRise = document.getElementById('chkEarlyRise').checked;
            let success = specialCase ? true : (sleptOnTime && wokeOnTime);

            if (data.dawn.history.length > 0) {
                let yesterday = formatDate(new Date(Date.now() - 86400000));
                let yRec = data.dawn.history.find(r => r.date === yesterday);
                if (success) {
                    data.dawn.consecutiveSuccess = yRec && yRec.success ? yRec.consecutiveSuccess + 1 : 1;
                    data.dawn.consecutiveFail = 0;
                } else {
                    data.dawn.consecutiveFail = yRec && !yRec.success ? yRec.consecutiveFail + 1 : 1;
                    data.dawn.consecutiveSuccess = 0;
                }
            } else {
                data.dawn.consecutiveSuccess = success ? 1 : 0;
                data.dawn.consecutiveFail = success ? 0 : 1;
            }

            let willDelta = 0, staminaDelta = 0;
            if (success) {
                let cs = data.dawn.consecutiveSuccess;
                if (cs >= 30) { willDelta += 2; staminaDelta += 1; }
                else if (cs >= 7) { willDelta += 1; staminaDelta += 0.5; }
                else if (cs >= 3) { willDelta += 0.5; staminaDelta += 0.2; }
                if (earlySleep) willDelta += 1;
                if (earlyRise) staminaDelta += 0.5;
            } else {
                if (!sleptOnTime) willDelta -= 1;
                if (!wokeOnTime) staminaDelta -= 0.5;
                let cf = data.dawn.consecutiveFail;
                if (cf >= 7) willDelta -= 3;
                else if (cf >= 3) willDelta -= 2;
            }

            data.dawn.history.push({
                date, success, earlySleep, earlyRise, willDelta, staminaDelta,
                consecutiveSuccess: data.dawn.consecutiveSuccess, consecutiveFail: data.dawn.consecutiveFail
            });

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            renderAll();
        }
        document.getElementById('addDawnRecordBtn').addEventListener('click', addDawnRecord);

        function renderDawnHistory() {
            let tbody = document.getElementById('dawnHistoryBody');
            tbody.innerHTML = '';

            // 获取排序方式
            let sortOrder = document.getElementById('dawnHistorySortSelect').value;
            let recs = data.dawn.history.slice();

            // 根据排序方式排序
            if (sortOrder === 'desc') {
                recs.sort((a, b) => b.date.localeCompare(a.date));
            } else {
                recs.sort((a, b) => a.date.localeCompare(b.date));
            }
            let totalPages = Math.ceil(recs.length / PAGE_SIZE);
            if (dawnHistoryPage > totalPages) dawnHistoryPage = totalPages || 1;
            let start = (dawnHistoryPage - 1) * PAGE_SIZE;
            let pageRecs = recs.slice(start, start + PAGE_SIZE);
            pageRecs.forEach(rec => {
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.success ? '成功' : '失败'}</td>
                    <td>${rec.earlySleep ? '是':'否'}</td>
                    <td>${rec.earlyRise ? '是':'否'}</td>
                    <td>${rec.willDelta}</td>
                    <td>${rec.staminaDelta}</td>
                    <td>
                        <button class="editDawnBtn" data-date="${rec.date}">编辑</button>
                        <button class="deleteDawnBtn" data-date="${rec.date}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            let pag = document.getElementById('dawnHistoryPagination');
            pag.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === dawnHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => { dawnHistoryPage = i; renderDawnHistory(); });
                pag.appendChild(btn);
            }
            document.querySelectorAll('.deleteDawnBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    let dt = btn.dataset.date;
                    if (!confirm(`删除 ${dt} 打卡记录？`)) return;

                    // 保存操作前状态
                    saveOperationState('delete_dawn_record', `删除晨曦之约记录 (${dt})`);

                    data.dawn.history = data.dawn.history.filter(r => r.date !== dt);
                    recomputeDawnConsecutive();
                    data.overallRecords = data.overallRecords.filter(r => r.date !== dt);

                    // 保存数据到本地存储
                    localStorage.setItem('lifeGameData', JSON.stringify(data));

                    renderAll();
                });
            });
            document.querySelectorAll('.editDawnBtn').forEach(btn => {
                btn.addEventListener('click', () => openEditDawnModal(btn.dataset.date));
            });
        }
        function recomputeDawnConsecutive() {
            data.dawn.consecutiveSuccess = 0;
            data.dawn.consecutiveFail = 0;
            let sorted = data.dawn.history.slice().sort((a,b) => a.date.localeCompare(b.date));
            sorted.forEach(rec => {
                if (rec.success) {
                    data.dawn.consecutiveSuccess += 1;
                    rec.consecutiveSuccess = data.dawn.consecutiveSuccess;
                    data.dawn.consecutiveFail = 0;
                    rec.consecutiveFail = 0;
                } else {
                    data.dawn.consecutiveFail += 1;
                    rec.consecutiveFail = data.dawn.consecutiveFail;
                    data.dawn.consecutiveSuccess = 0;
                    rec.consecutiveSuccess = 0;
                }
                let willDelta = 0, staminaDelta = 0;
                if (rec.success) {
                    let cs = rec.consecutiveSuccess;
                    if (cs >= 30) { willDelta += 2; staminaDelta += 1; }
                    else if (cs >= 7) { willDelta += 1; staminaDelta += 0.5; }
                    else if (cs >= 3) { willDelta += 0.5; staminaDelta += 0.2; }
                    if (rec.earlySleep) willDelta += 1;
                    if (rec.earlyRise) staminaDelta += 0.5;
                } else {
                    if (!rec.sleptOnTime) willDelta -= 1;
                    if (!rec.wokeOnTime) staminaDelta -= 0.5;
                    let cf = rec.consecutiveFail;
                    if (cf >= 7) willDelta -= 3;
                    else if (cf >= 3) willDelta -= 2;
                }
                rec.willDelta = willDelta;
                rec.staminaDelta = staminaDelta;
            });
        }
        document.getElementById('toggleDawnHistoryBtn').addEventListener('click', () => {
            let c = document.getElementById('dawnHistoryContainer');
            c.style.display = c.style.display === 'none' ? 'block' : 'none';
            if (c.style.display === 'block') renderDawnHistory();
        });
        document.getElementById('dawnHistoryContainer').style.display = 'none';

        /*************************************
         * “每日个人总结” 功能实现 *
         *************************************/
        function addDailySummary() {
            let date = document.getElementById('inputSummaryDate').value;
            if (!date) {
                alert('请选择日期');
                return;
            }
            if (data.summaries.some(s => s.date === date)) {
                alert('该日期已有总结，若需修改请删除后添加');
                return;
            }

            // 保存操作前状态
            saveOperationState('add_summary', `添加阶段性总结 (${date})`);

            let content = document.getElementById('inputSummaryContent').value;
            data.summaries.push({ date, content });

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            renderAll();
        }
        document.getElementById('addDailySummaryBtn').addEventListener('click', addDailySummary);

        function renderSummaryHistory() {
            let tbody = document.getElementById('summaryHistoryBody');
            tbody.innerHTML = '';

            // 获取排序方式
            let sortOrder = document.getElementById('summaryHistorySortSelect').value;
            let recs = data.summaries.slice();

            // 根据排序方式排序
            if (sortOrder === 'desc') {
                recs.sort((a, b) => b.date.localeCompare(a.date));
            } else {
                recs.sort((a, b) => a.date.localeCompare(b.date));
            }
            let totalPages = Math.ceil(recs.length / PAGE_SIZE);
            if (summaryHistoryPage > totalPages) summaryHistoryPage = totalPages || 1;
            let start = (summaryHistoryPage - 1) * PAGE_SIZE;
            let pageRecs = recs.slice(start, start + PAGE_SIZE);
            pageRecs.forEach(rec => {
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.content}</td>
                    <td>
                        <button class="editSummaryBtn" data-date="${rec.date}">编辑</button>
                        <button class="deleteSummaryBtn" data-date="${rec.date}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            let pag = document.getElementById('summaryHistoryPagination');
            pag.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === summaryHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => { summaryHistoryPage = i; renderSummaryHistory(); });
                pag.appendChild(btn);
            }
            document.querySelectorAll('.editSummaryBtn').forEach(btn => {
                btn.addEventListener('click', () => openEditSummaryModal(btn.dataset.date));
            });
            document.querySelectorAll('.deleteSummaryBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    let dt = btn.dataset.date;
                    if (!confirm(`删除 ${dt} 的总结？`)) return;

                    // 保存操作前状态
                    saveOperationState('delete_summary', `删除阶段性总结 (${dt})`);

                    data.summaries = data.summaries.filter(r => r.date !== dt);
                    data.overallRecords = data.overallRecords.filter(r => r.date !== dt);

                    // 保存数据到本地存储
                    localStorage.setItem('lifeGameData', JSON.stringify(data));

                    renderAll();
                });
            });
        }
        document.getElementById('toggleSummaryHistoryBtn').addEventListener('click', () => {
            let c = document.getElementById('summaryHistoryContainer');
            c.style.display = c.style.display === 'none' ? 'block' : 'none';
            if (c.style.display === 'block') renderSummaryHistory();
        });
        document.getElementById('summaryHistoryContainer').style.display = 'none';

        /**************************************
         * “每日总记录” 功能实现 *
         **************************************/
        // 渲染每日总记录概览
        function renderDailyOverview() {
            // 更新当前日期显示
            document.getElementById('currentOverviewDate').textContent = formatDate(new Date());

            // 渲染今日属性增量卡片
            renderTodayAttributeCards();

            // 渲染属性历史表格
            renderAttributesHistoryTable();

            // 渲染今日职业进度卡片
            renderTodayCareerCards();

            // 渲染今日称号进度卡片
            renderTodayTitleCards();

            // 渲染任务概览
            renderTasksOverviewPanel();

            // 渲染总结概览
            renderSummaryOverviewPanel();
        }

        // 渲染今日属性增量卡片
        function renderTodayAttributeCards() {
            let today = formatDate(new Date());
            let deltas = getDateDeltas(today);

            // 更新卡片显示
            updateAttributeCard('todayIntDelta', deltas.intDelta);
            updateAttributeCard('todayKnowledgeDelta', deltas.knowledgeDelta);
            updateAttributeCard('todayReadExpDelta', deltas.readExpDelta);
            updateAttributeCard('todayStaminaDelta', deltas.staminaDelta);
            updateAttributeCard('todayWillDelta', deltas.willDelta);
            updateAttributeCard('todayCharismaDelta', deltas.charismaDelta);
            updateAttributeCard('todayArtExpDelta', deltas.artExpDelta);
        }

        // 更新属性卡片
        function updateAttributeCard(elementId, value) {
            let element = document.getElementById(elementId);
            if (element) {
                let formattedValue = value >= 0 ? `+${value.toFixed(2)}` : value.toFixed(2);
                element.textContent = formattedValue;
                element.className = 'attr-value ' + (value >= 0 ? 'positive' : 'negative');
            }
        }

        // 渲染属性历史表格
        function renderAttributesHistoryTable() {
            let tbody = document.getElementById('attributesHistoryBody');
            tbody.innerHTML = '';

            let recentDates = getRecentDates(7);

            recentDates.forEach(dateStr => {
                let deltas = getDateDeltas(dateStr);
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${dateStr}</td>
                    <td>${deltas.intDelta.toFixed(2)}</td>
                    <td>${deltas.knowledgeDelta.toFixed(2)}</td>
                    <td>${deltas.readExpDelta.toFixed(2)}</td>
                    <td>${deltas.staminaDelta.toFixed(2)}</td>
                    <td>${deltas.willDelta.toFixed(2)}</td>
                    <td>${deltas.charismaDelta.toFixed(2)}</td>
                    <td>${deltas.artExpDelta.toFixed(2)}</td>
                `;
                tbody.appendChild(tr);
            });

            if (recentDates.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="no-data-message">暂无数据</td></tr>';
            }
        }

        // 渲染今日职业进度卡片
        function renderTodayCareerCards() {
            let attrs = calculateAttributes();

            // 更新幻构师等级进度
            updateCareerProgress('art', attrs.artExp, ART_LEVELS);

            // 更新真理之路(知识)进度
            updateCareerProgress('truthKnowledge', attrs.knowledge, TRUTH_KNOWLEDGE_LEVELS);

            // 更新真理之路(智力)进度
            updateCareerProgress('truthIntelligence', attrs.intelligence, TRUTH_INTELLIGENCE_LEVELS);
        }

        // 更新职业进度条
        function updateCareerProgress(type, currentValue, levels) {
            let levelInfo = getCurrentLevelInfo(currentValue, levels);
            let progressInfo = calculateProgressInfo(currentValue, levelInfo);

            // 更新等级名称
            let nameElement = document.getElementById(type === 'art' ? 'artLevelName' :
                                                   type === 'truthKnowledge' ? 'truthKnowledgeLevelName' :
                                                   'truthIntelligenceLevelName');
            nameElement.textContent = levelInfo.fullName;

            // 更新进度条
            let progressBar = document.getElementById(type === 'art' ? 'artLevelProgressBar' :
                                                    type === 'truthKnowledge' ? 'truthKnowledgeProgressBar' :
                                                    'truthIntelligenceProgressBar');
            progressBar.style.width = progressInfo.percentage + '%';

            // 更新百分比显示
            let percentageElement = document.getElementById(type === 'art' ? 'artLevelPercentage' :
                                                          type === 'truthKnowledge' ? 'truthKnowledgePercentage' :
                                                          'truthIntelligencePercentage');
            percentageElement.textContent = progressInfo.percentage.toFixed(1) + '%';

            // 更新提示信息
            let tooltipElement = document.getElementById(type === 'art' ? 'artLevelTooltip' :
                                                       type === 'truthKnowledge' ? 'truthKnowledgeTooltip' :
                                                       'truthIntelligenceTooltip');
            tooltipElement.textContent = `当前${type === 'art' ? '经验' : type === 'truthKnowledge' ? '知识' : '智力'}: ${currentValue.toFixed(1)} / ${progressInfo.nextTarget}`;
        }



        // 渲染今日称号进度卡片
        function renderTodayTitleCards() {
            let attrs = calculateAttributes();
            let consecutiveDays = data.dawn.consecutiveSuccess;

            // 更新晨曦之约称号进度
            updateTitleProgress('dawn', consecutiveDays, TITLES_DAWN, '连续成功', '天');

            // 更新阅识称号进度
            updateTitleProgress('readExp', attrs.readExp, TITLES_READ_EXP, '当前阅识', '');

            // 更新意志称号进度
            updateTitleProgress('will', attrs.will, TITLES_WILL, '当前意志', '');

            // 更新魅力称号进度
            updateTitleProgress('charisma', attrs.charisma, TITLES_CHARISMA, '当前魅力', '');
        }

        // 更新称号进度条
        function updateTitleProgress(type, currentValue, titles, valueLabel, unit) {
            let titleInfo = getCurrentTitleInfo(currentValue, titles);
            let progressInfo = calculateTitleProgressInfo(currentValue, titleInfo);

            // 更新称号名称
            let nameElement = document.getElementById(type + 'TitleName');
            nameElement.textContent = titleInfo.name || '无称号';

            // 更新进度条
            let progressBar = document.getElementById(type + 'TitleProgressBar');
            progressBar.style.width = progressInfo.percentage + '%';

            // 更新百分比显示
            let percentageElement = document.getElementById(type + 'TitlePercentage');
            percentageElement.textContent = progressInfo.percentage.toFixed(1) + '%';

            // 更新提示信息
            let tooltipElement = document.getElementById(type + 'TitleTooltip');
            tooltipElement.textContent = `${valueLabel}: ${currentValue.toFixed(type === 'dawn' ? 0 : 1)} / ${progressInfo.nextTarget} ${unit}`;
        }



        // 渲染任务概览面板
        function renderTasksOverviewPanel() {
            let container = document.getElementById('tasksOverviewPanel');
            container.innerHTML = '';

            let pendingTasks = data.tasks.filter(t => t.status === 'pending');

            if (pendingTasks.length === 0) {
                container.innerHTML = '<div class="no-data-message"><div class="icon">📋</div>暂无未完成任务</div>';
                return;
            }

            pendingTasks.forEach(task => {
                let progressPercent = Math.min((task.progressValue / task.targetValue) * 100, 100);
                let remainingDays = '';
                let urgencyClass = '';

                if (task.deadline) {
                    let days = daysBetween(new Date(), parseDate(task.deadline)) - 1;
                    if (days < 0) {
                        remainingDays = '已过期';
                        urgencyClass = 'urgent';
                    } else if (days <= 3) {
                        remainingDays = `剩余${days}天`;
                        urgencyClass = 'warning';
                    } else {
                        remainingDays = `剩余${days}天`;
                    }
                }

                let progressClass = '';
                if (progressPercent >= 80) progressClass = '';
                else if (progressPercent >= 50) progressClass = 'warning';
                else progressClass = 'danger';

                let taskCard = document.createElement('div');
                taskCard.className = `task-overview-card ${urgencyClass}`;
                taskCard.innerHTML = `
                    <div class="task-header">
                        <div class="task-title">${task.name}</div>
                        <div class="task-type">${task.type}</div>
                    </div>
                    <div class="task-progress-info">
                        <span>进度: ${task.progressValue}/${task.targetValue} (${progressPercent.toFixed(1)}%)</span>
                        ${remainingDays ? `<span>${remainingDays}</span>` : ''}
                    </div>
                    <div class="task-progress-bar">
                        <div class="task-progress-fill ${progressClass}" style="width: ${progressPercent}%"></div>
                    </div>
                `;
                container.appendChild(taskCard);
            });
        }

        // 渲染总结概览面板
        function renderSummaryOverviewPanel() {
            let container = document.getElementById('summaryOverviewPanel');
            let today = formatDate(new Date());
            let todaySummary = data.summaries.find(s => s.date === today);

            if (todaySummary && todaySummary.content.trim()) {
                container.innerHTML = `<div class="summary-content">${todaySummary.content}</div>`;
            } else {
                container.innerHTML = '<div class="summary-empty">今日暂无个人总结</div>';
            }
        }

        // 获取最近N天有数据的日期
        function getRecentDates(days) {
            let dates = [];
            let today = new Date();

            for (let i = 0; i < days; i++) {
                let date = new Date(today);
                date.setDate(today.getDate() - i);
                let dateStr = formatDate(date);

                // 检查该日期是否有数据
                let hasData = data.dailyRecords.some(r => r.date === dateStr) ||
                             data.dawn.history.some(r => r.date === dateStr) ||
                             data.summaries.some(r => r.date === dateStr) ||
                             data.tasks.some(t => t.completionDate === dateStr);

                if (hasData) {
                    dates.push(dateStr);
                }
            }

            return dates.reverse(); // 最早的日期在前
        }

        // 获取职业水平增量
        function getCareerDeltas(dateStr) {
            // 计算该日期前后的职业水平变化
            let prevData = {
                ...data,
                dailyRecords: data.dailyRecords.filter(r => r.date < dateStr),
                dawn: {
                    ...data.dawn,
                    history: data.dawn.history.filter(r => r.date < dateStr)
                },
                tasks: data.tasks.filter(t => !t.completionDate || t.completionDate < dateStr)
            };

            let currData = {
                ...data,
                dailyRecords: data.dailyRecords.filter(r => r.date <= dateStr),
                dawn: {
                    ...data.dawn,
                    history: data.dawn.history.filter(r => r.date <= dateStr)
                },
                tasks: data.tasks.filter(t => !t.completionDate || t.completionDate <= dateStr)
            };

            let prevAttrs = calculateAttributesUsing(prevData);
            let currAttrs = calculateAttributesUsing(currData);

            // 计算等级变化
            let prevArtLevel = getCurrentArtLevel(prevAttrs.artExp);
            let currArtLevel = getCurrentArtLevel(currAttrs.artExp);
            let artLevelDelta = currArtLevel.level - prevArtLevel.level;

            let prevKnowledgeLevel = getCurrentTruthLevel(prevAttrs.knowledge, 'knowledge');
            let currKnowledgeLevel = getCurrentTruthLevel(currAttrs.knowledge, 'knowledge');
            let truthKnowledgeDelta = currKnowledgeLevel.level - prevKnowledgeLevel.level;

            let prevIntelligenceLevel = getCurrentTruthLevel(prevAttrs.intelligence, 'intelligence');
            let currIntelligenceLevel = getCurrentTruthLevel(currAttrs.intelligence, 'intelligence');
            let truthIntelligenceDelta = currIntelligenceLevel.level - prevIntelligenceLevel.level;

            return {
                artLevelDelta: artLevelDelta > 0 ? `+${artLevelDelta}` : (artLevelDelta < 0 ? artLevelDelta.toString() : '0'),
                truthKnowledgeDelta: truthKnowledgeDelta > 0 ? `+${truthKnowledgeDelta}` : (truthKnowledgeDelta < 0 ? truthKnowledgeDelta.toString() : '0'),
                truthIntelligenceDelta: truthIntelligenceDelta > 0 ? `+${truthIntelligenceDelta}` : (truthIntelligenceDelta < 0 ? truthIntelligenceDelta.toString() : '0')
            };
        }

        // 获取称号增量
        function getTitleDeltas(dateStr) {
            // 计算该日期前后的称号变化
            let prevData = {
                ...data,
                dailyRecords: data.dailyRecords.filter(r => r.date < dateStr),
                dawn: {
                    ...data.dawn,
                    history: data.dawn.history.filter(r => r.date < dateStr)
                },
                tasks: data.tasks.filter(t => !t.completionDate || t.completionDate < dateStr)
            };

            let currData = {
                ...data,
                dailyRecords: data.dailyRecords.filter(r => r.date <= dateStr),
                dawn: {
                    ...data.dawn,
                    history: data.dawn.history.filter(r => r.date <= dateStr)
                },
                tasks: data.tasks.filter(t => !t.completionDate || t.completionDate <= dateStr)
            };

            // 重新计算晨曦之约连续成功天数
            let prevConsecutive = calculateConsecutiveSuccess(prevData.dawn.history);
            let currConsecutive = calculateConsecutiveSuccess(currData.dawn.history);

            let prevAttrs = calculateAttributesUsing(prevData);
            let currAttrs = calculateAttributesUsing(currData);

            // 计算称号等级变化
            let prevDawnTitle = getCurrentDawnTitle(prevConsecutive);
            let currDawnTitle = getCurrentDawnTitle(currConsecutive);
            let dawnTitleDelta = currDawnTitle.level - prevDawnTitle.level;

            let prevReadExpTitle = getCurrentReadExpTitle(prevAttrs.readExp);
            let currReadExpTitle = getCurrentReadExpTitle(currAttrs.readExp);
            let readExpTitleDelta = currReadExpTitle.level - prevReadExpTitle.level;

            let prevWillTitle = getCurrentWillTitle(prevAttrs.will);
            let currWillTitle = getCurrentWillTitle(currAttrs.will);
            let willTitleDelta = currWillTitle.level - prevWillTitle.level;

            let prevCharismaTitle = getCurrentCharismaTitle(prevAttrs.charisma);
            let currCharismaTitle = getCurrentCharismaTitle(currAttrs.charisma);
            let charismaTitleDelta = currCharismaTitle.level - prevCharismaTitle.level;

            return {
                dawnTitleDelta: dawnTitleDelta > 0 ? `+${dawnTitleDelta}` : (dawnTitleDelta < 0 ? dawnTitleDelta.toString() : '0'),
                readExpTitleDelta: readExpTitleDelta > 0 ? `+${readExpTitleDelta}` : (readExpTitleDelta < 0 ? readExpTitleDelta.toString() : '0'),
                willTitleDelta: willTitleDelta > 0 ? `+${willTitleDelta}` : (willTitleDelta < 0 ? willTitleDelta.toString() : '0'),
                charismaTitleDelta: charismaTitleDelta > 0 ? `+${charismaTitleDelta}` : (charismaTitleDelta < 0 ? charismaTitleDelta.toString() : '0')
            };
        }
        // 辅助函数：获取当前幻构师等级
        function getCurrentArtLevel(artExp) {
            for (let i = 0; i < ART_LEVELS.length; i++) {
                if (artExp <= ART_LEVELS[i].totalReq) {
                    return { level: i + 1, name: ART_LEVELS[i].level };
                }
            }
            return { level: ART_LEVELS.length, name: ART_LEVELS[ART_LEVELS.length - 1].level };
        }

        // 辅助函数：获取当前真理之路等级
        function getCurrentTruthLevel(value, type) {
            let levels = type === 'knowledge' ? TRUTH_KNOWLEDGE_LEVELS : TRUTH_INTELLIGENCE_LEVELS;
            for (let i = 0; i < levels.length; i++) {
                if (value <= levels[i].totalReq) {
                    return { level: i + 1, name: levels[i].level };
                }
            }
            return { level: levels.length, name: levels[levels.length - 1].level };
        }

        // 辅助函数：获取当前晨曦之约称号
        function getCurrentDawnTitle(consecutiveDays) {
            for (let i = TITLES_DAWN.length - 1; i >= 0; i--) {
                if (consecutiveDays >= TITLES_DAWN[i].reqDays) {
                    return { level: i + 1, name: TITLES_DAWN[i].name };
                }
            }
            return { level: 0, name: '无称号' };
        }

        // 辅助函数：获取当前阅识称号
        function getCurrentReadExpTitle(readExp) {
            for (let i = TITLES_READ_EXP.length - 1; i >= 0; i--) {
                if (readExp >= TITLES_READ_EXP[i].req) {
                    return { level: i + 1, name: TITLES_READ_EXP[i].name };
                }
            }
            return { level: 0, name: '无称号' };
        }

        // 辅助函数：获取当前意志称号
        function getCurrentWillTitle(will) {
            for (let i = TITLES_WILL.length - 1; i >= 0; i--) {
                if (will >= TITLES_WILL[i].req) {
                    return { level: i + 1, name: TITLES_WILL[i].name };
                }
            }
            return { level: 0, name: '无称号' };
        }

        // 辅助函数：获取当前魅力称号
        function getCurrentCharismaTitle(charisma) {
            for (let i = TITLES_CHARISMA.length - 1; i >= 0; i--) {
                if (charisma >= TITLES_CHARISMA[i].req) {
                    return { level: i + 1, name: TITLES_CHARISMA[i].name };
                }
            }
            return { level: 0, name: '无称号' };
        }

        // 辅助函数：计算连续成功天数
        function calculateConsecutiveSuccess(dawnHistory) {
            if (dawnHistory.length === 0) return 0;

            let sortedHistory = dawnHistory.slice().sort((a, b) => b.date.localeCompare(a.date));
            let consecutive = 0;

            for (let record of sortedHistory) {
                if (record.success) {
                    consecutive++;
                } else {
                    break;
                }
            }

            return consecutive;
        }

        // 辅助函数：使用指定数据计算属性
        function calculateAttributesUsing(dataToUse) {
            let attrs = { ...dataToUse.initial };

            // 累加每日记录的增量
            dataToUse.dailyRecords.forEach(rec => {
                attrs.intelligence += rec.deltas.intDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.readExp += rec.deltas.readExpDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.charisma += rec.deltas.charismaDelta;
                attrs.artExp += rec.deltas.artExpDelta;
            });

            // 累加晨曦之约的增量
            dataToUse.dawn.history.forEach(rec => {
                attrs.will += rec.willDelta;
                attrs.stamina += rec.staminaDelta;
            });

            // 累加任务奖励和惩罚
            dataToUse.tasks.forEach(task => {
                if (task.status === 'completed') {
                    task.rewards.forEach(rw => {
                        if (attrs.hasOwnProperty(rw.type)) {
                            attrs[rw.type] += rw.value;
                        }
                    });
                } else if (task.status === 'failed') {
                    task.penalties.forEach(pn => {
                        if (attrs.hasOwnProperty(pn.type)) {
                            attrs[pn.type] -= pn.value;
                        }
                    });
                }
            });

            return attrs;
        }

        // 获取当前等级信息
        function getCurrentLevelInfo(currentValue, levels) {
            for (let i = 0; i < levels.length; i++) {
                let level = levels[i];
                if (currentValue <= level.totalReq) {
                    // 找到当前阶段
                    let stageName = '未达成';
                    if (level.stages) {
                        level.stages.forEach(stage => {
                            if (currentValue >= stage.min && currentValue <= stage.max) {
                                stageName = stage.name;
                            }
                        });
                    }
                    return {
                        level: i + 1,
                        name: level.level,
                        fullName: `${level.level}（${stageName}）`,
                        totalReq: level.totalReq,
                        stages: level.stages || []
                    };
                }
            }
            // 超过最高等级
            let lastLevel = levels[levels.length - 1];
            return {
                level: levels.length,
                name: lastLevel.level,
                fullName: `${lastLevel.level}（高级）`,
                totalReq: lastLevel.totalReq,
                stages: lastLevel.stages || []
            };
        }

        // 计算进度信息
        function calculateProgressInfo(currentValue, levelInfo) {
            if (levelInfo.stages.length === 0) {
                return { percentage: 100, nextTarget: levelInfo.totalReq };
            }

            // 找到当前阶段
            for (let stage of levelInfo.stages) {
                if (currentValue >= stage.min && currentValue <= stage.max) {
                    let progress = currentValue - stage.min;
                    let total = stage.max - stage.min + 1;
                    let percentage = Math.min((progress / total) * 100, 100);
                    return { percentage, nextTarget: stage.max };
                }
            }

            // 如果没有找到阶段，返回默认值
            return { percentage: 0, nextTarget: levelInfo.totalReq };
        }

        // 获取当前称号信息
        function getCurrentTitleInfo(currentValue, titles) {
            let currentTitle = null;
            let nextTitle = null;

            for (let i = titles.length - 1; i >= 0; i--) {
                let reqValue = titles[i].reqDays || titles[i].req;
                if (currentValue >= reqValue) {
                    currentTitle = titles[i];
                    break;
                }
            }

            // 找到下一个称号
            for (let title of titles) {
                let reqValue = title.reqDays || title.req;
                if (currentValue < reqValue) {
                    nextTitle = title;
                    break;
                }
            }

            return { current: currentTitle, next: nextTitle };
        }

        // 计算称号进度信息
        function calculateTitleProgressInfo(currentValue, titleInfo) {
            if (!titleInfo.next) {
                // 已经是最高称号
                return { percentage: 100, nextTarget: currentValue };
            }

            let nextReq = titleInfo.next.reqDays || titleInfo.next.req;
            let prevReq = 0;

            if (titleInfo.current) {
                prevReq = titleInfo.current.reqDays || titleInfo.current.req;
            }

            let progress = currentValue - prevReq;
            let total = nextReq - prevReq;
            let percentage = Math.min((progress / total) * 100, 100);

            return { percentage, nextTarget: nextReq };
        }
        // 获取指定日期的属性增量
        function getDateDeltas(dateStr) {
            // 计算该日期的属性增量
            let dayRecord = data.dailyRecords.find(r => r.date === dateStr);
            let dawnRecord = data.dawn.history.find(r => r.date === dateStr);

            let deltas = {
                intDelta: 0,
                knowledgeDelta: 0,
                readExpDelta: 0,
                staminaDelta: 0,
                willDelta: 0,
                charismaDelta: 0,
                artExpDelta: 0
            };

            // 从每日记录获取增量
            if (dayRecord) {
                deltas.intDelta += dayRecord.deltas.intDelta;
                deltas.knowledgeDelta += dayRecord.deltas.knowledgeDelta;
                deltas.readExpDelta += dayRecord.deltas.readExpDelta;
                deltas.staminaDelta += dayRecord.deltas.staminaDelta;
                deltas.willDelta += dayRecord.deltas.willDelta;
                deltas.charismaDelta += dayRecord.deltas.charismaDelta;
                deltas.artExpDelta += dayRecord.deltas.artExpDelta;
            }

            // 从晨曦之约记录获取增量
            if (dawnRecord) {
                deltas.willDelta += dawnRecord.willDelta;
                deltas.staminaDelta += dawnRecord.staminaDelta;
            }

            // 从任务奖励/惩罚获取增量
            data.tasks.forEach(task => {
                if (task.completionDate === dateStr) {
                    if (task.status === 'completed') {
                        task.rewards.forEach(rw => {
                            switch(rw.type) {
                                case 'intelligence': deltas.intDelta += rw.value; break;
                                case 'knowledge': deltas.knowledgeDelta += rw.value; break;
                                case 'readExp': deltas.readExpDelta += rw.value; break;
                                case 'stamina': deltas.staminaDelta += rw.value; break;
                                case 'will': deltas.willDelta += rw.value; break;
                                case 'charisma': deltas.charismaDelta += rw.value; break;
                                case 'artExp': deltas.artExpDelta += rw.value; break;
                            }
                        });
                    } else if (task.status === 'failed') {
                        task.penalties.forEach(pn => {
                            switch(pn.type) {
                                case 'intelligence': deltas.intDelta -= pn.value; break;
                                case 'knowledge': deltas.knowledgeDelta -= pn.value; break;
                                case 'readExp': deltas.readExpDelta -= pn.value; break;
                                case 'stamina': deltas.staminaDelta -= pn.value; break;
                                case 'will': deltas.willDelta -= pn.value; break;
                                case 'charisma': deltas.charismaDelta -= pn.value; break;
                                case 'artExp': deltas.artExpDelta -= pn.value; break;
                            }
                        });
                    }
                }
            });

            return deltas;
        }



        // 渲染详细历史记录
        function renderDetailedHistory() {
            let container = document.getElementById('detailedHistoryContent');
            let daysSelect = document.getElementById('historyDaysSelect');
            let selectedDays = daysSelect.value;

            container.innerHTML = '';

            let dates = [];
            if (selectedDays === 'all') {
                // 获取所有有数据的日期
                let allDates = new Set();
                data.dailyRecords.forEach(r => allDates.add(r.date));
                data.dawn.history.forEach(r => allDates.add(r.date));
                data.summaries.forEach(r => allDates.add(r.date));
                data.tasks.forEach(t => { if (t.completionDate) allDates.add(t.completionDate); });
                dates = Array.from(allDates).sort((a, b) => b.localeCompare(a));
            } else {
                dates = getRecentDates(parseInt(selectedDays));
                dates.reverse(); // 最新的在前
            }

            if (dates.length === 0) {
                container.innerHTML = '<div class="no-data-message"><div class="icon">📅</div>暂无历史记录</div>';
                return;
            }

            dates.forEach(dateStr => {
                let dayCard = document.createElement('div');
                dayCard.className = 'history-day-card';

                let deltas = getDateDeltas(dateStr);
                let careerDeltas = getCareerDeltas(dateStr);
                let titleDeltas = getTitleDeltas(dateStr);
                let summary = data.summaries.find(s => s.date === dateStr);

                dayCard.innerHTML = `
                    <div class="history-day-header">
                        <div class="history-date">${dateStr}</div>
                        ${summary && summary.content.trim() ? '<div class="history-summary-badge">有总结</div>' : ''}
                    </div>
                    <div class="history-content">
                        <div class="history-section">
                            <h4>属性增量</h4>
                            <p>智力: ${deltas.intDelta.toFixed(2)} | 知识: ${deltas.knowledgeDelta.toFixed(2)} | 阅识: ${deltas.readExpDelta.toFixed(2)}</p>
                            <p>体力: ${deltas.staminaDelta.toFixed(2)} | 意志: ${deltas.willDelta.toFixed(2)} | 魅力: ${deltas.charismaDelta.toFixed(2)}</p>
                            <p>幻构师经验: ${deltas.artExpDelta.toFixed(2)}</p>
                        </div>
                        <div class="history-section">
                            <h4>等级/称号变化</h4>
                            <p>幻构师等级: ${careerDeltas.artLevelDelta} | 真理之路(知识): ${careerDeltas.truthKnowledgeDelta} | 真理之路(智力): ${careerDeltas.truthIntelligenceDelta}</p>
                            <p>晨曦称号: ${titleDeltas.dawnTitleDelta} | 阅识称号: ${titleDeltas.readExpTitleDelta} | 意志称号: ${titleDeltas.willTitleDelta} | 魅力称号: ${titleDeltas.charismaTitleDelta}</p>
                        </div>
                    </div>
                    ${summary && summary.content.trim() ? `<div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 4px; font-size: 0.9em;"><strong>总结:</strong> ${summary.content}</div>` : ''}
                `;

                container.appendChild(dayCard);
            });
        }
        /***********************************
         * “幻构师计划” 渲染 *
         ***********************************/
        function renderArtPlan() {
            let tbody = document.getElementById('artPlanTable').querySelector('tbody');
            tbody.innerHTML = '';
            let attrs = calculateAttributes();
            let currentExp = attrs.artExp;
            ART_LEVELS.forEach(lvlInfo => {
                let row = document.createElement('tr');
                let stageName = '未达成';
                if (currentExp > lvlInfo.totalReq) {
                    stageName = '已超出';
                } else {
                    lvlInfo.stages.forEach(st => {
                        if (currentExp >= st.min && currentExp <= st.max) stageName = st.name;
                    });
                }
                let pct = Math.min((currentExp / lvlInfo.totalReq) * 100, 100).toFixed(2);
                row.innerHTML = `
                    <td>${lvlInfo.level}</td>
                    <td>${lvlInfo.totalReq}</td>
                    <td>${stageName}</td>
                    <td>${Math.min(currentExp, lvlInfo.totalReq).toFixed(2)}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        /*************************************
         * “真理之路计划” 渲染 *
         *************************************/
        function renderTruthPlan() {
            let tbKn = document.getElementById('truthKnowledgeTable').querySelector('tbody');
            tbKn.innerHTML = '';
            let attrs = calculateAttributes();
            let currKn = attrs.knowledge;
            TRUTH_KNOWLEDGE_LEVELS.forEach(lvlInfo => {
                let row = document.createElement('tr');
                let stageName = '未达成';
                if (currKn > lvlInfo.totalReq) {
                    stageName = '已超出';
                } else {
                    lvlInfo.stages.forEach(st => {
                        if (currKn >= st.min && currKn <= st.max) stageName = st.name;
                    });
                }
                let pct = Math.min((currKn / lvlInfo.totalReq) * 100, 100).toFixed(2);
                row.innerHTML = `
                    <td>${lvlInfo.level}</td>
                    <td>${lvlInfo.totalReq}</td>
                    <td>${stageName}</td>
                    <td>${Math.min(currKn, lvlInfo.totalReq).toFixed(2)}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%
                    </td>
                `;
                tbKn.appendChild(row);
            });
            let tbIt = document.getElementById('truthIntelligenceTable').querySelector('tbody');
            tbIt.innerHTML = '';
            let currIt = attrs.intelligence;
            TRUTH_INTELLIGENCE_LEVELS.forEach(lvlInfo => {
                let row = document.createElement('tr');
                let stageName = '未达成';
                if (currIt > lvlInfo.totalReq) {
                    stageName = '已超出';
                } else {
                    lvlInfo.stages.forEach(st => {
                        if (currIt >= st.min && currIt <= st.max) stageName = st.name;
                    });
                }
                let pct = Math.min((currIt / lvlInfo.totalReq) * 100, 100).toFixed(2);
                row.innerHTML = `
                    <td>${lvlInfo.level}</td>
                    <td>${lvlInfo.totalReq}</td>
                    <td>${stageName}</td>
                    <td>${Math.min(currIt, lvlInfo.totalReq).toFixed(2)}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%
                    </td>
                `;
                tbIt.appendChild(row);
            });
        }

        /******************************
         * “称号系统” 渲染 *
         ******************************/
        function renderTitles() {
            let tbDawn = document.getElementById('titlesDawnTable').querySelector('tbody');
            tbDawn.innerHTML = '';
            let cs = data.dawn.consecutiveSuccess;
            TITLES_DAWN.forEach(t => {
                let attained = cs >= t.reqDays;
                let pct = attained ? 100 : ((cs / t.reqDays) * 100).toFixed(2);
                let row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.reqDays}</td>
                    <td>${cs}</td>
                    <td>${formatReward(t.reward)}</td>
                    <td>
                        ${attained ? '<span style="color:green;font-weight:bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%`}
                    </td>
                `;
                tbDawn.appendChild(row);
            });
            let attrs = calculateAttributes();
            let tbRe = document.getElementById('titlesReadExpTable').querySelector('tbody');
            tbRe.innerHTML = '';
            let re = attrs.readExp;
            TITLES_READ_EXP.forEach(t => {
                let attained = re >= t.req;
                let pct = attained ? 100 : ((re / t.req) * 100).toFixed(2);
                let row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.req}</td>
                    <td>${re.toFixed(2)}</td>
                    <td>${formatReward(t.reward)}</td>
                    <td>
                        ${attained ? '<span style="color:green;font-weight:bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%`}
                    </td>
                `;
                tbRe.appendChild(row);
            });
            let tbWi = document.getElementById('titlesWillTable').querySelector('tbody');
            tbWi.innerHTML = '';
            let wi = attrs.will;
            TITLES_WILL.forEach(t => {
                let attained = wi >= t.req;
                let pct = attained ? 100 : ((wi / t.req) * 100).toFixed(2);
                let row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.req}</td>
                    <td>${wi.toFixed(2)}</td>
                    <td>${formatReward(t.reward)}</td>
                    <td>
                        ${attained ? '<span style="color:green;font-weight:bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%`}
                    </td>
                `;
                tbWi.appendChild(row);
            });
            let tbCh = document.getElementById('titlesCharismaTable').querySelector('tbody');
            tbCh.innerHTML = '';
            let cha = attrs.charisma;
            TITLES_CHARISMA.forEach(t => {
                let attained = cha >= t.req;
                let pct = attained ? 100 : ((cha / t.req) * 100).toFixed(2);
                let row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.req}</td>
                    <td>${cha.toFixed(2)}</td>
                    <td>${formatReward(t.reward)}</td>
                    <td>
                        ${attained ? '<span style="color:green;font-weight:bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%`}
                    </td>
                `;
                tbCh.appendChild(row);
            });
        }
        function formatReward(r) {
            if (r.type === 'allAttributesEfficiency') {
                return `全体属性效率 +${(r.value * 100).toFixed(1)}%`;
            }
            if (r.type === 'intelligenceEfficiency') {
                return `智力效率 +${(r.value * 100).toFixed(1)}%`;
            }
            if (r.type === 'knowledgeEfficiency') {
                return `知识效率 +${(r.value * 100).toFixed(1)}%`;
            }
            if (r.type === 'charismaEfficiency') {
                return `魅力效率 +${(r.value * 100).toFixed(1)}%`;
            }
            return '';
        }

        /*******************************
         * “任务系统” 功能实现（未完成部分） *
         *******************************/
        let nextTaskId = 1;

        function renderTasks() {
            let ptb = document.getElementById('pendingTasksBody');
            let ctb = document.getElementById('completedTasksBody');
            ptb.innerHTML = '';
            ctb.innerHTML = '';

            // 获取筛选条件
            let typeFilter = document.getElementById('taskTypeFilter').value;
            let cycleFilter = document.getElementById('taskCycleFilter').value;
            let showDescription = document.getElementById('showDescriptionToggle').checked;

            // 控制描述列的显示/隐藏
            let descriptionColumns = document.querySelectorAll('.description-column');
            descriptionColumns.forEach(col => {
                col.style.display = showDescription ? '' : 'none';
            });

            // 筛选任务
            let filteredTasks = data.tasks.filter(task => {
                let typeMatch = !typeFilter || task.type === typeFilter;
                let cycleMatch = !cycleFilter || task.cycle === cycleFilter;
                return typeMatch && cycleMatch;
            });

            // 分离未完成和已完成/失败任务
            let pendingTasksFiltered = filteredTasks.filter(t => t.status === 'pending');
            let completedTasksFiltered = filteredTasks.filter(t => t.status !== 'pending');

            // 获取排序方式并排序
            let pendingSortOrder = document.getElementById('pendingTasksSortSelect').value;
            let completedSortOrder = document.getElementById('completedTasksSortSelect').value;

            // 排序未完成任务
            if (pendingSortOrder === 'created_desc') {
                pendingTasksFiltered.sort((a, b) => b.createdTimestamp.localeCompare(a.createdTimestamp));
            } else if (pendingSortOrder === 'created_asc') {
                pendingTasksFiltered.sort((a, b) => a.createdTimestamp.localeCompare(b.createdTimestamp));
            } else if (pendingSortOrder === 'deadline_asc') {
                pendingTasksFiltered.sort((a, b) => {
                    if (!a.deadline && !b.deadline) return 0;
                    if (!a.deadline) return 1;
                    if (!b.deadline) return -1;
                    return a.deadline.localeCompare(b.deadline);
                });
            } else if (pendingSortOrder === 'deadline_desc') {
                pendingTasksFiltered.sort((a, b) => {
                    if (!a.deadline && !b.deadline) return 0;
                    if (!a.deadline) return -1;
                    if (!b.deadline) return 1;
                    return b.deadline.localeCompare(a.deadline);
                });
            }

            // 排序已完成/失败任务
            if (completedSortOrder === 'completion_desc') {
                completedTasksFiltered.sort((a, b) => b.completionDate.localeCompare(a.completionDate));
            } else if (completedSortOrder === 'completion_asc') {
                completedTasksFiltered.sort((a, b) => a.completionDate.localeCompare(b.completionDate));
            } else if (completedSortOrder === 'created_desc') {
                completedTasksFiltered.sort((a, b) => b.createdTimestamp.localeCompare(a.createdTimestamp));
            } else if (completedSortOrder === 'created_asc') {
                completedTasksFiltered.sort((a, b) => a.createdTimestamp.localeCompare(b.createdTimestamp));
            }

            // 重新合并任务列表用于统计
            filteredTasks = [...pendingTasksFiltered, ...completedTasksFiltered];

            // 统计任务数据（基于筛选后的任务）
            let totalTasks = filteredTasks.length;
            let pendingTasks = filteredTasks.filter(t => t.status === 'pending').length;
            let completedTasks = filteredTasks.filter(t => t.status === 'completed').length;
            let failedTasks = filteredTasks.filter(t => t.status === 'failed').length;

            // 计算比率
            let completionRate = totalTasks > 0 ? ((completedTasks / totalTasks) * 100).toFixed(1) : 0;
            let failureRate = totalTasks > 0 ? ((failedTasks / totalTasks) * 100).toFixed(1) : 0;
            let successRate = (completedTasks + failedTasks) > 0 ? ((completedTasks / (completedTasks + failedTasks)) * 100).toFixed(1) : 0;

            // 更新统计显示
            document.getElementById('taskStatsTotal').textContent = totalTasks;
            document.getElementById('taskStatsPending').textContent = pendingTasks;
            document.getElementById('taskStatsCompleted').textContent = completedTasks;
            document.getElementById('taskStatsFailed').textContent = failedTasks;
            document.getElementById('taskStatsCompletionRate').textContent = completionRate + '%';
            document.getElementById('taskStatsFailureRate').textContent = failureRate + '%';
            document.getElementById('taskStatsSuccessRate').textContent = successRate + '%';

            // 更新每个任务进度（创建时间之后的记录）
            data.tasks.forEach(task => {
                let curr = 0;
                data.dailyRecords.forEach(r => {
                    if (r.timestamp >= task.createdTimestamp) {
                        switch (task.targetType) {
                            case 'study': curr += r.studyMin; break;
                            case 'specialRead': curr += r.specialReadPages; break;
                            case 'art': curr += r.artMin; break;
                            case 'exercise': curr += r.exerciseMin; break;
                            case 'bookRead': curr += r.bookReadMin; break;
                            case 'videoWatch': curr += r.videoWatchMin; break;
                            default: break;
                        }
                    }
                });
                task.progressValue = curr;
            });

            // 获取类型显示名称的函数
            function getTypeDisplayName(type) {
                switch(type) {
                    case 'art': return '幻构师计划';
                    case 'truth': return '真理之路计划';
                    case 'dawn': return '晨曦之约计划';
                    case 'other': return '其他';
                    default: return type;
                }
            }

            // 获取周期显示名称的函数
            function getCycleDisplayName(cycle) {
                switch(cycle) {
                    case 'short': return '短期';
                    case 'long': return '长期';
                    default: return cycle;
                }
            }

            // 渲染未完成任务
            pendingTasksFiltered.forEach(task => {
                let rewardPenaltyStr = '';
                task.rewards.forEach(rw => rewardPenaltyStr += `奖励[${rw.type}:${rw.value}] `);
                task.penalties.forEach(pn => rewardPenaltyStr += `惩罚[${pn.type}:${pn.value}] `);

                let rd = '';
                if (task.deadline) {
                    let rdDays = daysBetween(new Date(), parseDate(task.deadline)) - 1;
                    rd = rdDays >= 0 ? `${rdDays}` : '已过期';
                } else rd = '—';
                let pct = Math.min((task.progressValue / task.targetValue) * 100, 100).toFixed(2);
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${task.createdTimestamp}</td>
                    <td>${task.name}</td>
                    <td class="description-column" style="display: ${showDescription ? '' : 'none'};">${task.description || '—'}</td>
                    <td>${getTypeDisplayName(task.type)}</td>
                    <td>${getCycleDisplayName(task.cycle)}</td>
                    <td>${task.progressValue}/${task.targetValue}</td>
                    <td>${rd}</td>
                    <td>${rewardPenaltyStr}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width:${pct}%;"></div></div>
                        ${pct}%
                    </td>
                    <td>
                        <button class="editTaskBtn" data-id="${task.id}">编辑</button>
                        <button class="complete-btn" data-id="${task.id}">完成</button>
                        <button class="fail-btn" data-id="${task.id}">失败</button>
                        <button class="delete-btn" data-id="${task.id}">删除</button>
                    </td>
                `;
                ptb.appendChild(tr);
            });

            // 渲染已完成/失败任务
            completedTasksFiltered.forEach(task => {
                let rewardPenaltyStr = '';
                task.rewards.forEach(rw => rewardPenaltyStr += `奖励[${rw.type}:${rw.value}] `);
                task.penalties.forEach(pn => rewardPenaltyStr += `惩罚[${pn.type}:${pn.value}] `);

                let tdStatus = task.status === 'completed' ? '完成' : '失败';
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${task.createdTimestamp}</td>
                    <td>${task.name}</td>
                    <td class="description-column" style="display: ${showDescription ? '' : 'none'};">${task.description || '—'}</td>
                    <td>${getTypeDisplayName(task.type)}</td>
                    <td>${getCycleDisplayName(task.cycle)}</td>
                    <td>${task.targetValue}/${task.targetValue}</td>
                    <td>${task.completionDate}</td>
                    <td>${rewardPenaltyStr}</td>
                    <td>${tdStatus}</td>
                    <td>
                        <button class="editTaskBtn" data-id="${task.id}">编辑</button>
                        <button class="delete-btn" data-id="${task.id}">删除</button>
                    </td>
                `;
                ctb.appendChild(tr);
            });

            // 移除旧的事件监听器，使用事件委托来避免重复绑定问题
            // 事件委托将在页面加载时统一设置
        }

        function addPenaltyOrReward(containerId, type) {
            let container = document.getElementById(containerId);
            let div = document.createElement('div');
            div.classList.add('reward-penalty-item');
            div.innerHTML = `
                <div class="form-group">
                    <select class="${type}TypeSelect" required>
                        <option value="">类型</option>
                        <option value="intelligence">智力</option>
                        <option value="knowledge">知识</option>
                        <option value="readExp">阅识</option>
                        <option value="stamina">体力</option>
                        <option value="will">意志</option>
                        <option value="charisma">魅力</option>
                        <option value="artExp">幻构师经验</option>
                    </select>
                </div>
                <div class="form-group">
                    <input type="number" class="${type}ValueInput" min="0" placeholder="值" required>
                </div>
                <button type="button" class="remove-btn">删除</button>
            `;
            container.appendChild(div);
            div.querySelector('.remove-btn').addEventListener('click', () => {
                container.removeChild(div);
            });
        }
        document.getElementById('addPenaltyBtn').addEventListener('click', () => addPenaltyOrReward('penaltyContainer', 'penalty'));
        document.getElementById('addRewardBtn').addEventListener('click', () => addPenaltyOrReward('rewardContainer', 'reward'));

        // 编辑任务模态框中的添加奖励/惩罚按钮
        document.getElementById('addEditPenaltyBtn').addEventListener('click', () => addPenaltyOrReward('editPenaltyContainer', 'penalty'));
        document.getElementById('addEditRewardBtn').addEventListener('click', () => addPenaltyOrReward('editRewardContainer', 'reward'));

        function createTask() {
            let name = document.getElementById('taskName').value.trim();
            let description = document.getElementById('taskDescription').value.trim();
            let type = document.getElementById('taskType').value;
            let cycle = document.getElementById('taskCycle').value;
            let targetType = document.getElementById('taskTargetType').value;
            let targetValue = parseInt(document.getElementById('taskTargetValue').value);
            let deadline = document.getElementById('taskDeadline').value;
            if (!name || !type || !cycle || !targetType || isNaN(targetValue) || targetValue < 1) {
                alert('请完整填写必填项，目标数值 ≥ 1');
                return;
            }

            // 保存操作前状态
            saveOperationState('create_task', `创建任务 (${name})`);
            let penalties = [], rewards = [];
            document.querySelectorAll('#penaltyContainer .reward-penalty-item').forEach(div => {
                let sel = div.querySelector('.penaltyTypeSelect');
                let val = parseFloat(div.querySelector('.penaltyValueInput').value);
                if (sel.value && !isNaN(val) && val >= 0) penalties.push({ type: sel.value, value: val });
            });
            document.querySelectorAll('#rewardContainer .reward-penalty-item').forEach(div => {
                let sel = div.querySelector('.rewardTypeSelect');
                let val = parseFloat(div.querySelector('.rewardValueInput').value);
                if (sel.value && !isNaN(val) && val >= 0) rewards.push({ type: sel.value, value: val });
            });
            let newTask = {
                id: nextTaskId++,
                name, description, type, cycle, targetType, targetValue, deadline,
                penalties, rewards,
                createdTimestamp: formatTimestamp(),
                status: 'pending',
                progressValue: 0,
                completionDate: ''
            };
            data.tasks.push(newTask);

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            // 重新渲染所有界面
            renderAll();

            // 清理表单
            document.getElementById('taskForm').reset();
            document.querySelectorAll('#penaltyContainer .reward-penalty-item').forEach((div,i) => { if (i>0) div.remove(); });
            document.querySelectorAll('#rewardContainer .reward-penalty-item').forEach((div,i) => { if (i>0) div.remove(); });
        }
        document.getElementById('createTaskBtn').addEventListener('click', createTask);

        function completeTask(id) {
            let task = data.tasks.find(t => t.id === id);
            if (!task) return;
            if (task.progressValue < task.targetValue) {
                alert('任务尚未达成，无法完成');
                return;
            }

            // 保存操作前状态
            saveOperationState('complete_task', `完成任务 (${task.name})`);

            task.status = 'completed';
            task.completionDate = formatDate(new Date());

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            // 重新渲染所有界面
            renderAll();

            // 显示完成提示
            alert(`任务"${task.name}"已完成！奖励已应用。`);
        }

        function failTask(id) {
            let task = data.tasks.find(t => t.id === id);
            if (!task) return;
            if (!confirm(`确定将任务"${task.name}"标记为失败？这将应用任务惩罚。`)) return;

            // 保存操作前状态
            saveOperationState('fail_task', `任务失败 (${task.name})`);

            task.status = 'failed';
            task.completionDate = formatDate(new Date());

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            // 重新渲染所有界面
            renderAll();

            // 显示失败提示
            alert(`任务"${task.name}"已标记为失败！惩罚已应用。`);
        }

        function deleteTask(id) {
            let task = data.tasks.find(t => t.id === id);
            if (!task) return;
            if (!confirm('确定删除此任务？')) return;

            // 保存操作前状态
            saveOperationState('delete_task', `删除任务 (${task.name})`);

            data.tasks = data.tasks.filter(t => t.id !== id);

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            // 重新渲染所有界面
            renderAll();
        }

        /*****************************
         * “规则说明” 弹出框逻辑 *
         *****************************/
        document.getElementById('statusInfoBtn').addEventListener('click', () => {
            document.getElementById('statusInfoModal').style.display = 'flex';
        });
        document.getElementById('statusInfoClose').addEventListener('click', () => {
            document.getElementById('statusInfoModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('statusInfoModal')) {
                document.getElementById('statusInfoModal').style.display = 'none';
            }
        });

        /****************************
         * 编辑功能：打开/关闭模态框 *
         ****************************/

        // —— 编辑“每日记录”
        function openEditDailyModal(timestamp) {
            let rec = data.dailyRecords.find(r => r.timestamp === timestamp);
            if (!rec) return;
            document.getElementById('editDailyTimestamp').value = rec.timestamp;
            document.getElementById('editDailyDate').value = rec.date;
            document.getElementById('editArtMinutes').value = rec.artMin;
            document.getElementById('editStudyMinutes').value = rec.studyMin;
            document.getElementById('editSpecialReadPages').value = rec.specialReadPages;
            document.getElementById('editExerciseMinutes').value = rec.exerciseMin;
            document.getElementById('editBookReadMinutes').value = rec.bookReadMin;
            document.getElementById('editVideoWatchMinutes').value = rec.videoWatchMin;
            document.getElementById('editChkWillArt').checked = rec.chkWillArt;
            document.getElementById('editChkWillStudy').checked = rec.chkWillStudy;
            document.getElementById('editChkWillExercise').checked = rec.chkWillExercise;
            document.getElementById('editChkWillBook').checked = rec.chkWillBook;
            document.getElementById('editChkWillVideo').checked = rec.chkWillVideo;
            document.getElementById('editDailyModal').style.display = 'flex';
        }
        document.getElementById('editDailyClose').addEventListener('click', () => {
            document.getElementById('editDailyModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('editDailyModal')) {
                document.getElementById('editDailyModal').style.display = 'none';
            }
        });
        document.getElementById('saveEditDailyBtn').addEventListener('click', () => {
            let ts = document.getElementById('editDailyTimestamp').value;
            let rec = data.dailyRecords.find(r => r.timestamp === ts);
            if (!rec) return;

            // 保存操作前状态
            saveOperationState('edit_daily_record', `编辑每日记录 (${rec.date})`);
            rec.date = document.getElementById('editDailyDate').value;
            rec.artMin = parseInt(document.getElementById('editArtMinutes').value) || 0;
            rec.studyMin = parseInt(document.getElementById('editStudyMinutes').value) || 0;
            rec.specialReadPages = parseInt(document.getElementById('editSpecialReadPages').value) || 0;
            rec.exerciseMin = parseInt(document.getElementById('editExerciseMinutes').value) || 0;
            rec.bookReadMin = parseInt(document.getElementById('editBookReadMinutes').value) || 0;
            rec.videoWatchMin = parseInt(document.getElementById('editVideoWatchMinutes').value) || 0;
            rec.chkWillArt = document.getElementById('editChkWillArt').checked;
            rec.chkWillStudy = document.getElementById('editChkWillStudy').checked;
            rec.chkWillExercise = document.getElementById('editChkWillExercise').checked;
            rec.chkWillBook = document.getElementById('editChkWillBook').checked;
            rec.chkWillVideo = document.getElementById('editChkWillVideo').checked;

            recomputeAllDailyRecords();

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            renderAll();
            document.getElementById('editDailyModal').style.display = 'none';
        });

        // —— 编辑“晨曦之约记录”
        function openEditDawnModal(date) {
            let rec = data.dawn.history.find(r => r.date === date);
            if (!rec) return;
            document.getElementById('editDawnDate').value = rec.date;
            document.getElementById('editDawnNewDate').value = rec.date;
            document.getElementById('editChkSleptOnTime').checked = rec.success && rec.success;
            document.getElementById('editChkWokeOnTime').checked = rec.success && rec.success;
            document.getElementById('editChkSpecialCase').checked = !rec.success;
            document.getElementById('editChkEarlySleep').checked = rec.earlySleep;
            document.getElementById('editChkEarlyRise').checked = rec.earlyRise;
            document.getElementById('editDawnModal').style.display = 'flex';
        }
        document.getElementById('editDawnClose').addEventListener('click', () => {
            document.getElementById('editDawnModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('editDawnModal')) {
                document.getElementById('editDawnModal').style.display = 'none';
            }
        });
        document.getElementById('saveEditDawnBtn').addEventListener('click', () => {
            let oldDate = document.getElementById('editDawnDate').value;
            let rec = data.dawn.history.find(r => r.date === oldDate);
            if (!rec) return;

            // 保存操作前状态
            saveOperationState('edit_dawn_record', `编辑晨曦之约记录 (${oldDate})`);
            let newDate = document.getElementById('editDawnNewDate').value;
            let sleptOnTime = document.getElementById('editChkSleptOnTime').checked;
            let wokeOnTime = document.getElementById('editChkWokeOnTime').checked;
            let specialCase = document.getElementById('editChkSpecialCase').checked;
            let earlySleep = document.getElementById('editChkEarlySleep').checked;
            let earlyRise = document.getElementById('editChkEarlyRise').checked;
            let success = specialCase ? true : (sleptOnTime && wokeOnTime);

            rec.date = newDate;
            rec.success = success;
            rec.earlySleep = earlySleep;
            rec.earlyRise = earlyRise;

            recomputeDawnConsecutive();

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            renderAll();
            document.getElementById('editDawnModal').style.display = 'none';
        });

        // —— 编辑“每日总结”
        function openEditSummaryModal(date) {
            let rec = data.summaries.find(r => r.date === date);
            if (!rec) return;
            document.getElementById('editSummaryOldDate').value = rec.date;
            document.getElementById('editSummaryDate').value = rec.date;
            document.getElementById('editSummaryContent').value = rec.content;
            document.getElementById('editSummaryModal').style.display = 'flex';
        }
        document.getElementById('editSummaryClose').addEventListener('click', () => {
            document.getElementById('editSummaryModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('editSummaryModal')) {
                document.getElementById('editSummaryModal').style.display = 'none';
            }
        });
        document.getElementById('saveEditSummaryBtn').addEventListener('click', () => {
            let oldDate = document.getElementById('editSummaryOldDate').value;
            let rec = data.summaries.find(r => r.date === oldDate);
            if (!rec) return;

            // 保存操作前状态
            saveOperationState('edit_summary', `编辑阶段性总结 (${oldDate})`);
            let newDate = document.getElementById('editSummaryDate').value;
            let newContent = document.getElementById('editSummaryContent').value;
            if (newDate !== oldDate && data.summaries.some(r => r.date === newDate)) {
                alert('新日期已存在总结，无法修改为该日期');
                return;
            }
            rec.date = newDate;
            rec.content = newContent;

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            renderAll();
            document.getElementById('editSummaryModal').style.display = 'none';
        });
        // —— 编辑“已完成/失败任务”
        function openEditTaskModal(id) {
            let task = data.tasks.find(t => t.id === id);
            if (!task) return;
            document.getElementById('editTaskId').value = task.id;

            // 设置创建时间（转换为datetime-local格式）
            let createdDate = new Date(task.createdTimestamp);
            let datetimeLocal = createdDate.getFullYear() + '-' +
                String(createdDate.getMonth() + 1).padStart(2, '0') + '-' +
                String(createdDate.getDate()).padStart(2, '0') + 'T' +
                String(createdDate.getHours()).padStart(2, '0') + ':' +
                String(createdDate.getMinutes()).padStart(2, '0');
            document.getElementById('editTaskCreatedTimestamp').value = datetimeLocal;

            document.getElementById('editTaskName').value = task.name;
            document.getElementById('editTaskDescription').value = task.description;
            document.getElementById('editTaskType').value = task.type;
            document.getElementById('editTaskCycle').value = task.cycle;
            document.getElementById('editTaskTargetType').value = task.targetType;
            document.getElementById('editTaskTargetValue').value = task.targetValue;
            document.getElementById('editTaskDeadline').value = task.deadline || '';
            // 清空并重建惩罚/奖励列表
            let penContainer = document.getElementById('editPenaltyContainer');
            let rewContainer = document.getElementById('editRewardContainer');
            penContainer.querySelectorAll('.reward-penalty-item').forEach((div, idx) => { if (idx > 0) div.remove(); });
            rewContainer.querySelectorAll('.reward-penalty-item').forEach((div, idx) => { if (idx > 0) div.remove(); });
            // 添加已有惩罚条目
            task.penalties.forEach(pn => {
                let div = document.createElement('div');
                div.classList.add('reward-penalty-item');
                div.innerHTML = `
                    <div class="form-group">
                        <select class="penaltyTypeSelect" required>
                            <option value="">类型</option>
                            <option value="intelligence"${pn.type==='intelligence'?' selected':''}>智力</option>
                            <option value="knowledge"${pn.type==='knowledge'?' selected':''}>知识</option>
                            <option value="readExp"${pn.type==='readExp'?' selected':''}>阅识</option>
                            <option value="stamina"${pn.type==='stamina'?' selected':''}>体力</option>
                            <option value="will"${pn.type==='will'?' selected':''}>意志</option>
                            <option value="charisma"${pn.type==='charisma'?' selected':''}>魅力</option>
                            <option value="artExp"${pn.type==='artExp'?' selected':''}>幻构师经验</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="number" class="penaltyValueInput" min="0" value="${pn.value}" required>
                    </div>
                    <button type="button" class="remove-btn">删除</button>
                `;
                penContainer.appendChild(div);
                div.querySelector('.remove-btn').addEventListener('click', () => penContainer.removeChild(div));
            });
            // 添加已有奖励条目
            task.rewards.forEach(rw => {
                let div = document.createElement('div');
                div.classList.add('reward-penalty-item');
                div.innerHTML = `
                    <div class="form-group">
                        <select class="rewardTypeSelect" required>
                            <option value="">类型</option>
                            <option value="intelligence"${rw.type==='intelligence'?' selected':''}>智力</option>
                            <option value="knowledge"${rw.type==='knowledge'?' selected':''}>知识</option>
                            <option value="readExp"${rw.type==='readExp'?' selected':''}>阅识</option>
                            <option value="stamina"${rw.type==='stamina'?' selected':''}>体力</option>
                            <option value="will"${rw.type==='will'?' selected':''}>意志</option>
                            <option value="charisma"${rw.type==='charisma'?' selected':''}>魅力</option>
                            <option value="artExp"${rw.type==='artExp'?' selected':''}>幻构师经验</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="number" class="rewardValueInput" min="0" value="${rw.value}" required>
                    </div>
                    <button type="button" class="remove-btn">删除</button>
                `;
                rewContainer.appendChild(div);
                div.querySelector('.remove-btn').addEventListener('click', () => rewContainer.removeChild(div));
            });
            document.getElementById('editTaskStatus').value = task.status;
            document.getElementById('editTaskCompletionDate').value = task.completionDate || '';

            // 根据任务状态控制完成日期字段的显示
            const completionDateGroup = document.querySelector('#editTaskModal .date-group');
            if (task.status === 'pending') {
                completionDateGroup.style.display = 'none';
            } else {
                completionDateGroup.style.display = 'block';
            }

            document.getElementById('editTaskModal').style.display = 'flex';
        }
        document.getElementById('editTaskClose').addEventListener('click', () => {
            document.getElementById('editTaskModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('editTaskModal')) {
                document.getElementById('editTaskModal').style.display = 'none';
            }
        });

        // 编辑任务状态变化时控制完成日期字段显示
        document.getElementById('editTaskStatus').addEventListener('change', function() {
            const completionDateGroup = document.querySelector('#editTaskModal .date-group');
            if (this.value === 'pending') {
                completionDateGroup.style.display = 'none';
                document.getElementById('editTaskCompletionDate').value = '';
            } else {
                completionDateGroup.style.display = 'block';
                // 如果没有完成日期，自动设置为今天
                if (!document.getElementById('editTaskCompletionDate').value) {
                    document.getElementById('editTaskCompletionDate').value = formatDate(new Date());
                }
            }
        });
        document.getElementById('saveEditTaskBtn').addEventListener('click', () => {
            let id = parseInt(document.getElementById('editTaskId').value);
            let task = data.tasks.find(t => t.id === id);
            if (!task) return;

            // 保存操作前状态
            saveOperationState('edit_task', `编辑任务 (${task.name})`);

            // 获取创建时间并转换为时间戳格式
            let createdTimestampInput = document.getElementById('editTaskCreatedTimestamp').value;
            let createdDate = new Date(createdTimestampInput);
            let createdTimestamp = createdDate.getFullYear() + '-' +
                String(createdDate.getMonth() + 1).padStart(2, '0') + '-' +
                String(createdDate.getDate()).padStart(2, '0') + ' ' +
                String(createdDate.getHours()).padStart(2, '0') + ':' +
                String(createdDate.getMinutes()).padStart(2, '0') + ':' +
                String(createdDate.getSeconds()).padStart(2, '0');

            let name = document.getElementById('editTaskName').value.trim();
            let description = document.getElementById('editTaskDescription').value.trim();
            let type = document.getElementById('editTaskType').value;
            let cycle = document.getElementById('editTaskCycle').value;
            let targetType = document.getElementById('editTaskTargetType').value;
            let targetValue = parseInt(document.getElementById('editTaskTargetValue').value);
            let deadline = document.getElementById('editTaskDeadline').value;
            let status = document.getElementById('editTaskStatus').value;
            let completionDate = document.getElementById('editTaskCompletionDate').value;

            if (!name || !type || !cycle || !targetType || isNaN(targetValue) || targetValue < 1) {
                alert('请完整填写必填项，目标数值 ≥ 1');
                return;
            }
            // 重新读取惩罚/奖励
            let penalties = [], rewards = [];
            document.querySelectorAll('#editPenaltyContainer .reward-penalty-item').forEach(div => {
                let sel = div.querySelector('.penaltyTypeSelect');
                let val = parseFloat(div.querySelector('.penaltyValueInput').value);
                if (sel.value && !isNaN(val) && val >= 0) penalties.push({ type: sel.value, value: val });
            });
            document.querySelectorAll('#editRewardContainer .reward-penalty-item').forEach(div => {
                let sel = div.querySelector('.rewardTypeSelect');
                let val = parseFloat(div.querySelector('.rewardValueInput').value);
                if (sel.value && !isNaN(val) && val >= 0) rewards.push({ type: sel.value, value: val });
            });

            // 更新字段
            task.createdTimestamp = createdTimestamp;
            task.name = name;
            task.description = description;
            task.type = type;
            task.cycle = cycle;
            task.targetType = targetType;
            task.targetValue = targetValue;
            task.deadline = deadline;
            task.status = status;
            task.completionDate = status === 'pending' ? '' : completionDate;
            task.penalties = penalties;
            task.rewards = rewards;

            // 保存数据到本地存储
            localStorage.setItem('lifeGameData', JSON.stringify(data));

            // 重新渲染所有界面
            renderAll();
            document.getElementById('editTaskModal').style.display = 'none';
        });

        /**************************************
         * renderAll：刷新所有界面元素 *
         **************************************/
        // 注意：此函数已在前面定义，包含图表更新逻辑

        /**************************************
         * 主题切换功能 *
         **************************************/

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('gameTheme') || 'dark';
            applyTheme(savedTheme);
            updateThemeSelector(savedTheme);
        }

        // 应用主题
        function applyTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.removeAttribute('data-theme');
            } else {
                document.documentElement.setAttribute('data-theme', theme);
            }
            localStorage.setItem('gameTheme', theme);
        }

        // 更新主题选择器状态
        function updateThemeSelector(activeTheme) {
            const options = document.querySelectorAll('.theme-option');
            options.forEach(option => {
                option.classList.remove('active');
                if (option.dataset.theme === activeTheme) {
                    option.classList.add('active');
                }
            });
        }

        // 主题切换器事件
        document.getElementById('themeToggle').addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = document.getElementById('themeDropdown');
            dropdown.classList.toggle('show');
        });

        // 主题选项点击事件
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function() {
                const theme = this.dataset.theme;
                applyTheme(theme);
                updateThemeSelector(theme);
                document.getElementById('themeDropdown').classList.remove('show');
            });
        });

        // 点击其他地方关闭主题选择器
        document.addEventListener('click', function() {
            document.getElementById('themeDropdown').classList.remove('show');
        });

        // 初始化主题
        initTheme();

        // 初始化日期输入
        function initDateInputs() {
            const today = formatDate(new Date());
            document.getElementById('inputDailyDate').value = today;
            document.getElementById('inputSummaryDate').value = today;
        }

        // 初始化任务系统的筛选和显示功能
        function initTaskSystemFeatures() {
            // 筛选功能事件监听器
            document.getElementById('taskTypeFilter').addEventListener('change', renderTasks);
            document.getElementById('taskCycleFilter').addEventListener('change', renderTasks);
            document.getElementById('showDescriptionToggle').addEventListener('change', renderTasks);

            // 排序功能事件监听器
            document.getElementById('pendingTasksSortSelect').addEventListener('change', renderTasks);
            document.getElementById('completedTasksSortSelect').addEventListener('change', renderTasks);

            // 历史记录排序功能事件监听器
            document.getElementById('dailyHistorySortSelect').addEventListener('change', renderDailyHistory);
            document.getElementById('dawnHistorySortSelect').addEventListener('change', renderDawnHistory);
            document.getElementById('summaryHistorySortSelect').addEventListener('change', renderSummaryHistory);

            // 清除筛选按钮
            document.getElementById('clearFiltersBtn').addEventListener('click', function() {
                document.getElementById('taskTypeFilter').value = '';
                document.getElementById('taskCycleFilter').value = '';
                document.getElementById('showDescriptionToggle').checked = true;
                renderTasks();
            });

            // 撤回功能按钮
            document.getElementById('undoBtn').addEventListener('click', performUndo);

            // 初始化撤回按钮状态
            updateUndoButton();
        }

        // 页面加载完成后初始化
        function initializeApp() {
            // 加载数据
            loadData();

            // 初始化日期输入
            initDateInputs();

            // 初始化任务系统功能
            initTaskSystemFeatures();

            // 渲染所有界面
            renderAll();
        }

        // 启动应用
        initializeApp();

    </script>
</body>
</html>
