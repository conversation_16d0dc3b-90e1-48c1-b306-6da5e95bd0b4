# 人生游戏计划系统需求明确文档

## 1. 系统总体概述

### 1.1 项目定位
设计一个个人成长游戏化系统，通过RPG游戏元素激励用户培养自律习惯、提升各项能力。

### 1.2 核心功能概述
- 基础属性系统：管理与提升六大属性
- 逆水行舟机制：习惯养成与惩罚系统
- 三大计划体系：幻构师(绘画)、真理之路(学习与阅读)、晨曦之约(作息)
- 职业等级提升：技能成长路径
- 称号奖励系统：成就激励机制
- 数据记录与分析：成长轨迹可视化

## 2. 属性系统详细规则

### 2.1 基础属性

| 属性名称 | 初始值 | 增长方式 | 增长规则 |
|---------|-------|---------|---------|
| 智力(Intelligence) | 0 | 学习 | 每学习1小时增加1点 |
| 知识(Knowledge) | 0 | 阅读 | 每阅读10页增加1点；每阅读1页增加0.1点 |
| 体力(Stamina) | 0 | 运动训练 | 每训练1小时增加1点；连续3/7/30天运动效率分别增加10%/20%/30% |
| 意志(Willpower) | 0 | 持续执行 | 连续执行任何行动3/7/30天分别增加1/2/3点（不同行动可叠加） |
| 魅力(Charisma) | 0 | 综合提升 | 其他四种基础属性每增加1点，增加0.1魅力 |
| 幻构师经验 | 1180 | 绘画 | 每绘画1小时增加10点经验 |

### 2.2 属性衰减规则
- **意志衰减**：懈怠1天减少1点，连续懈怠≥3天每天减少2点，连续懈怠≥7天每天减少3点
- 其他属性不会自然衰减，但受逆水行舟机制影响可能减少

## 3. 逆水行舟详细规则

### 3.1 幻构师计划逆水行舟
- **判定标准**：绘画时长>0为执行，=0为懈怠
- **废退法则**：
  - 单日懈怠：经验-50/天
  - 连续懈怠≥3天：经验-80/天
  - 连续懈怠≥7天：经验-100/天
- **行舟法则**：
  - 连续执行≥3天：经验增长效率+10%
  - 连续执行≥7天：经验增长效率+25%
  - 连续执行≥30天：经验增长效率+40%

### 3.2 真理之路计划逆水行舟
- **判定标准**：学习时长>0或阅读页数>0为执行，两者都=0为懈怠
- **废退法则**：
  - 单日懈怠：知识-0.1/天，智力-0.5/天
  - 连续懈怠≥3天：知识-0.3/天，智力-1.5/天
  - 连续懈怠≥7天：知识-1/天，智力-5/天
- **行舟法则**：
  - 连续执行≥3天：知识和智力属性获取效率+10%
  - 连续执行≥7天：知识和智力属性获取效率+25%
  - 连续执行≥30天：知识和智力属性获取效率+40%

### 3.3 晨曦之约计划逆水行舟
- **判定标准**：24:00前入睡且8:30前起床为执行成功，否则为懈怠
- **废退法则**：
  - 未按时睡觉：意志-1/次
  - 未按时起床：体力-0.5/次
- **行舟法则**：
  - 连续执行≥3天：每天意志+0.5，体力+0.2
  - 连续执行≥7天：每天意志+1，体力+0.5
  - 连续执行≥30天：每天意志+2，体力+1
  - 23:30前睡觉且8:00前起床：额外意志+0.5/天

## 4. 职业等级系统详细规则

### 4.1 幻构师计划等级体系
详细等级与经验需求表：
1. **Lv.3 描形学徒**（总需求1500）
   - 初级（1-450）
   - 中级（451-1050）
   - 高级（1051-1500）
2. **Lv.4 构素学者**（总需求3000）
   - 初级（1501-2400）
   - 中级（2401-3600）
   - 高级（3601-4500）
3. **Lv.5 灵绘使徒**（总需求5000）
   - 初级（4501-6000）
   - 中级（6001-8000）
   - 高级（8001-9500）
4. **Lv.6 影纹术士**（总需求8000）
   - 初级（9501-11900）
   - 中级（11901-15100）
   - 高级（15101-17500）
5. **Lv.7 心象织者**（总需求12000）
   - 初级（17501-21100）
   - 中级（21101-25900）
   - 高级（25901-29500）
6. **Lv.8 空境画匠**（总需求18000）
   - 初级（29501-34900）
   - 中级（34901-42100）
   - 高级（42101-47500）
7. **Lv.9 律令绘爵**（总需求26000）
   - 初级（47501-55300）
   - 中级（55301-65700）
   - 高级（65701-73500）
8. **Lv.10 幻构师**（总需求36000）
   - 初级（73501-84300）
   - 中级（84301-98700）
   - 高级（98701-109500）

### 4.2 真理之路计划等级体系
分为知识侧和智力侧两条职业线：

**知识侧**
1. **LV.1 灰袍学徒**（总需求150）
   - 初级（1-30）
   - 中级（31-75）
   - 高级（76-150）
2. **LV.2 白袍向导**（总需求500）
   - 初级（151-250）
   - 中级（251-400）
   - 高级（401-650）
3. **LV.3 墨衣学者**（总需求1500）
   - 初级（651-950）
   - 中级（951-1400）
   - 高级（1401-2150）
4. **LV.4 青衿贤者**（总需求4000）
   - 初级（2151-2950）
   - 中级（2951-4150）
   - 高级（4151-6150）
5. **LV.5 玄冕宗师**（总需求10000）
   - 初级（6151-8150）
   - 中级（8151-11150）
   - 高级（11151-16150）

**智力侧**
1. **LV.1 褐衣明理**（总需求150）
   - 初级（1-30）
   - 中级（31-75）
   - 高级（76-150）
2. **LV.2 缁衣慎思**（总需求500）
   - 初级（151-250）
   - 中级（251-400）
   - 高级（401-650）
3. **LV.3 朱衣审辩**（总需求1500）
   - 初级（651-950）
   - 中级（951-1400）
   - 高级（1401-2150）
4. **LV.4 紫绶格物**（总需求4000）
   - 初级（2151-2950）
   - 中级（2951-4150）
   - 高级（4151-6150）
5. **LV.5 金章弘道**（总需求10000）
   - 初级（6151-8150）
   - 中级（8151-11150）
   - 高级（11151-16150）

## 5. 称号系统详细规则

### 5.1 晨曦之约称号体系
| 等级 | 称号 | 要求 | 奖励 | 失约惩罚标准 |
|------|------|------|------|------------|
| Lv.1 | 星辉学徒 | 连续坚持 7 天 | 智力提升效率 +5% | 失败1天失去称号 |
| Lv.2 | 晨风哨卫 | 连续坚持 30 天 | 知识+智力提升效率 +5% | 累计失败3天失去称号 |
| Lv.3 | 夜穹守誓 | 连续坚持 60 天 | 全体属性提升效率 +5% | 累计失败6天失去称号 |
| Lv.4 | 破晓骑士 | 连续坚持 90 天 | 全体属性提升效率 +10% | 累计失败9天失去称号 |
| Lv.5 | 黎明星使 | 连续坚持 120 天 | 全体属性提升效率 +15% | 累计失败12天失去称号 |
| Lv.6 | 永夜圣者 | 连续坚持 180 天 | 全体属性提升效率 +20% | 累计失败18天失去称号 |
| Lv.7 | 晨曦领主 | 连续坚持 365 天 | 全体属性提升效率 +25% | 累计失败36天失去称号 |
| Lv.8 | 时序主宰 | 连续坚持 730 天 | 全体属性提升效率 +30% | 累计失败73天失去称号 |

**特别规则**：失去称号后重新计算连续坚持天数。

### 5.2 意志称号体系
| 等级 | 称号 | 所需意志值 | 奖励 |
|------|------|-----------|------|
| LV.1 | 晨曦微志 | 50 | 魅力增长效率 +5% |
| LV.2 | 坚石守心 | 200 | 魅力增长效率 +10% |
| LV.3 | 荆棘先锋 | 500 | 魅力增长效率 +15% |
| LV.4 | 钢铁铸意 | 800 | 魅力增长效率 +20% |
| LV.5 | 风暴不屈 | 1200 | 魅力增长效率 +25% |
| LV.6 | 星辰恒志 | 2000 | 魅力增长效率 +30% |
| LV.7 | 炽魂永燃 | 3000 | 魅力增长效率 +40% |
| LV.8 | 无朽之心 | 5000 | 魅力增长效率 +50% |

### 5.3 魅力称号体系
| 等级 | 称号 | 所需魅力值 | 奖励 |
|------|------|-----------|------|
| LV.1 | 萤火微光 | 10 | 全体属性提升效率 +5% |
| LV.2 | 晨露流辉 | 50 | 全体属性提升效率 +10% |
| LV.3 | 星芒初绽 | 100 | 全体属性提升效率 +15% |
| LV.4 | 银月颂光 | 200 | 全体属性提升效率 +20% |
| LV.5 | 日冕凝华 | 300 | 全体属性提升效率 +25% |
| LV.6 | 虹彩冠冕 | 500 | 全体属性提升效率 +30% |
| LV.7 | 天穹律光 | 800 | 全体属性提升效率 +40% |
| LV.8 | 万象圣辉 | 1200 | 全体属性提升效率 +50% |

**综合规则**：所有称号提供的效率加成可以叠加计算。

## 6. 界面功能详细需求

### 6.1 全局界面元素
- **顶部信息区**
  - 标题："人生游戏计划"
  - 当前日期和时间（北京时间）
  - 计划启动天数（2025年3月1日为第1天）
  - 保存按钮：保存所有数据状态
  - 重置按钮：将所有数据恢复至初始状态
  - 主题切换功能：提供10种预设配色方案

### 6.2 状态展示区
- **问号图标**：点击显示系统规则说明
- **属性面板**：
  - 展示六项基础属性当前值
  - 各属性实时更新
- **职业等级面板**：
  - 显示幻构师等级（含小阶段）
  - 显示真理之路-知识侧等级（含小阶段）
  - 显示真理之路-智力侧等级（含小阶段）
- **称号面板**：
  - 显示晨曦之约当前称号和提供的加成
  - 显示意志当前称号和提供的加成
  - 显示魅力当前称号和提供的加成
- **逆水行舟状态面板**：
  - 三个计划的当前逆水行舟状态（废退/行舟/正常）
  - 对应的加成/减益效果显示

### 6.3 每日记录功能模块
- **表单输入字段**：
  - 绘画时长（小时）
  - 学习时长（小时）
  - 阅读页数
- **自动计算字段**：
  - 幻构师经验增量
  - 智力增量
  - 知识增量
  - 体力增量（暂不启用）
  - 意志增量
  - 魅力增量
  - 逆水行舟状态备注
- **表单功能**：
  - 默认显示当天记录
  - 历史记录按钮：点击显示历史数据
  - 分页显示：每页10条记录
  - 编辑与删除功能

### 6.4 晨曦之约计划模块
- **表单输入字段**：
  - 入睡日期
  - 起床日期
  - 睡觉时间
  - 起床时间
  - 特殊情况标记（勾选框）
- **自动计算字段**：
  - 打卡状态（成功/失败）
- **表单功能**：
  - 默认显示当天记录
  - 历史记录按钮：点击显示历史数据
  - 分页显示：每页10条记录
  - 编辑与删除功能

### 6.5 每日总结模块
- **表单输入字段**：
  - 日期（自动填充）
  - 总结内容（文本区域）
- **表单功能**：
  - 默认显示当天记录
  - 历史记录按钮：点击显示历史数据
  - 分页显示：每页10条记录
  - 编辑与删除功能

### 6.6 每日总记录模块
- **自动生成字段**：
  - 日期
  - 幻构师进度（当前值与变化量）
  - 知识侧进度（当前值与变化量）
  - 智力侧进度（当前值与变化量）
  - 晨曦打卡状态
  - 每日总结摘要
- **表单功能**：
  - 默认显示当天记录
  - 历史记录按钮：点击显示历史数据
  - 分页显示：每页10条记录
  - 编辑与删除功能

### 6.7 幻构师计划进度模块
- 所有等级与阶段的表格化展示
- 每个等级/阶段显示：
  - 等级名称
  - 总需求经验
  - 阶段划分
  - 当前经验
  - 百分比进度条

### 6.8 真理之路计划进度模块
- **知识侧与智力侧分别展示**
- 所有等级与阶段的表格化展示
- 每个等级/阶段显示：
  - 等级名称
  - 总需求属性值
  - 阶段划分
  - 当前属性值
  - 百分比进度条

### 6.9 称号系统展示模块
- **三种称号分别展示**
- 所有等级称号的表格化展示
- 每个称号显示：
  - 等级
  - 称号名称
  - 获取条件
  - 当前进度
  - 奖励效果
  - 百分比进度条
  - 已获得称号用特殊UI标识

### 6.10 数据分析与可视化模块
- **基础属性分析区**
  - 六种基础属性趋势展示
  - 三个时间维度切换：
    - 每月属性提升趋势（按日显示）
    - 每年属性提升趋势（按日/月显示，可切换）
    - 过往属性提升趋势（按月/年显示，可切换）
  - 图表类型切换：条状图/折线图
  
- **幻构师经验分析区**
  - 幻构师经验趋势单独展示
  - 三个时间维度切换同上
  - 图表类型切换：条状图/折线图

## 7. 数据处理与计算逻辑

### 7.1 属性计算流程
1. 获取输入数据（学习时长/阅读页数/绘画时长）
2. 判定各计划当日执行状态
3. 计算各计划逆水行舟状态及效果
4. 计算称号加成效果
5. 最终计算各项属性变化量
6. 更新属性总值

### 7.2 逆水行舟状态判定
- **三种状态**：
  1. 懈怠：当日相关活动为0，触发废退法则
  2. 正常执行：当日有活动但连续天数未达标，无加成无减益
  3. 高效行舟：当日有活动且连续天数达标，触发行舟法则

### 7.3 称号获取逻辑
- **晨曦之约称号**：
  - 基于连续打卡天数自动判定
  - 失约失去称号并重新计数
- **意志称号**：
  - 基于当前累计意志值自动判定
  - 意志值达标后获得相应称号
- **魅力称号**：
  - 基于当前累计魅力值自动判定
  - 魅力值达标后获得相应称号
- 前一等级称号获得后才能开启下一等级称号进度

### 7.4 关键算法需求
- **连续执行天数计算**：准确追踪各计划的连续执行天数
- **属性效率加成叠加**：正确计算多个来源的效率加成
- **技能等级判定**：根据属性值自动判定当前等级与阶段
- **数据统计与可视化**：不同时间维度的数据聚合与展示

## 8. 数据存储需求

### 8.1 存储内容
- 用户基础属性值
- 每日活动记录
- 计划执行状态记录
- 称号获取进度
- 技能等级进度
- 逆水行舟状态
- 每日总结内容

### 8.2 存储方式
- 本地存储，支持数据保存与恢复
- 数据格式应方便导出与导入

### 8.3 数据安全
- 提供数据备份功能
- 防止意外数据丢失的措施

## 9. 扩展性考虑

### 9.1 未来可能添加的功能
- 体力训练记录与计算（当前暂不启用）
- 更多计划类型的支持
- 更复杂的属性关联机制
- 社交分享功能

### 9.2 预留扩展接口
- 设计应考虑未来功能扩展的可能性
- 数据模型应具有足够的灵活性

## 10. 非功能性需求

### 10.1 性能要求
- 界面响应迅速，操作流畅
- 数据计算准确无误
- 支持大量历史数据的存储与查询

### 10.2 兼容性要求
- 支持主流浏览器
- 响应式设计，适应不同屏幕尺寸

### 10.3 用户体验要求
- 界面美观，主题可定制
- 操作简单直观
- 反馈及时清晰
- 游戏化元素丰富，提升使用动力