<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>修复测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #testResults {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>人生游戏计划 - 修复测试页面</h1>
    
    <div class="test-section">
        <h2>1. Chart.js 加载测试</h2>
        <button onclick="testChartJS()">测试 Chart.js 加载</button>
        <div id="chartTest"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 数据导入导出测试</h2>
        <button onclick="testExport()">测试数据导出</button>
        <button onclick="testImport()">测试数据导入</button>
        <input type="file" id="importFile" accept=".json" style="display: none;">
        <div id="importExportTest"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 图表初始化测试</h2>
        <button onclick="testChartInit()">测试图表初始化</button>
        <div style="width: 400px; height: 200px; margin: 20px 0;">
            <canvas id="testChart"></canvas>
        </div>
        <div id="chartInitTest"></div>
    </div>
    
    <div id="testResults">
        <h2>测试结果</h2>
    </div>

    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <script>
        let testResults = [];
        
        function addResult(test, status, message) {
            const result = { test, status, message, timestamp: new Date() };
            testResults.push(result);
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const container = document.getElementById('testResults');
            let html = '<h2>测试结果</h2>';
            
            testResults.forEach(result => {
                const className = result.status === 'success' ? 'success' : 
                                result.status === 'error' ? 'error' : 'warning';
                html += `<div class="test-result ${className}">
                    <strong>${result.test}:</strong> ${result.message}
                    <small style="float: right;">${result.timestamp.toLocaleTimeString()}</small>
                </div>`;
            });
            
            container.innerHTML = html;
        }
        
        function testChartJS() {
            const container = document.getElementById('chartTest');
            
            if (typeof Chart === 'undefined') {
                container.innerHTML = '<div class="test-result error">Chart.js 未加载</div>';
                addResult('Chart.js加载', 'error', 'Chart.js库未成功加载');
                
                // 尝试加载备用CDN
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js';
                script.onload = function() {
                    container.innerHTML = '<div class="test-result success">Chart.js 备用CDN加载成功</div>';
                    addResult('Chart.js备用CDN', 'success', '备用CDN加载成功');
                };
                script.onerror = function() {
                    container.innerHTML = '<div class="test-result error">Chart.js 备用CDN也加载失败</div>';
                    addResult('Chart.js备用CDN', 'error', '备用CDN加载失败');
                };
                document.head.appendChild(script);
            } else {
                container.innerHTML = '<div class="test-result success">Chart.js 已成功加载</div>';
                addResult('Chart.js加载', 'success', `Chart.js版本: ${Chart.version || '未知'}`);
            }
        }
        
        function testExport() {
            const container = document.getElementById('importExportTest');
            
            try {
                // 模拟数据
                const testData = {
                    startDate: '2024-01-01',
                    dailyRecords: [
                        {
                            date: '2024-01-01',
                            deltas: { intDelta: 1, knowledgeDelta: 2, artExpDelta: 3 }
                        }
                    ],
                    dawn: { history: [] },
                    summaries: [],
                    tasks: [],
                    exportDate: new Date().toISOString(),
                    version: '1.0'
                };
                
                const jsonString = JSON.stringify(testData, null, 2);
                const blob = new Blob([jsonString], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'test_export.json';
                a.click();
                URL.revokeObjectURL(url);
                
                container.innerHTML = '<div class="test-result success">数据导出测试成功</div>';
                addResult('数据导出', 'success', '测试数据已成功导出');
            } catch (error) {
                container.innerHTML = `<div class="test-result error">数据导出失败: ${error.message}</div>`;
                addResult('数据导出', 'error', error.message);
            }
        }
        
        function testImport() {
            const fileInput = document.getElementById('importFile');
            fileInput.click();
            
            fileInput.onchange = function(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                const container = document.getElementById('importExportTest');
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);
                        
                        // 验证数据格式
                        if (!data || typeof data !== 'object') {
                            throw new Error('无效的数据格式');
                        }
                        
                        container.innerHTML = '<div class="test-result success">数据导入测试成功</div>';
                        addResult('数据导入', 'success', '测试文件导入成功，数据格式正确');
                    } catch (error) {
                        container.innerHTML = `<div class="test-result error">数据导入失败: ${error.message}</div>`;
                        addResult('数据导入', 'error', error.message);
                    }
                };
                
                reader.onerror = function() {
                    container.innerHTML = '<div class="test-result error">文件读取失败</div>';
                    addResult('数据导入', 'error', '文件读取失败');
                };
                
                reader.readAsText(file);
            };
        }
        
        function testChartInit() {
            const container = document.getElementById('chartInitTest');
            
            if (typeof Chart === 'undefined') {
                container.innerHTML = '<div class="test-result error">Chart.js未加载，无法测试图表初始化</div>';
                addResult('图表初始化', 'error', 'Chart.js未加载');
                return;
            }
            
            try {
                const canvas = document.getElementById('testChart');
                const ctx = canvas.getContext('2d');
                
                const chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月'],
                        datasets: [{
                            label: '测试数据',
                            data: [10, 20, 15, 25, 30],
                            borderColor: '#007bff',
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                
                container.innerHTML = '<div class="test-result success">图表初始化成功</div>';
                addResult('图表初始化', 'success', '测试图表已成功创建和渲染');
                
                // 清理测试图表
                setTimeout(() => {
                    chart.destroy();
                }, 3000);
                
            } catch (error) {
                container.innerHTML = `<div class="test-result error">图表初始化失败: ${error.message}</div>`;
                addResult('图表初始化', 'error', error.message);
            }
        }
        
        // 页面加载完成后自动测试Chart.js
        window.addEventListener('load', function() {
            setTimeout(() => {
                testChartJS();
            }, 1000);
        });
    </script>
</body>
</html>
