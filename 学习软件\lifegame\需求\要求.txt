/*
Project: 人生游戏计划 (Life Game Plan)
Single-Page React Application using Tailwind CSS, Recharts, and LocalStorage for persistence.
Structure:
- public/index.html
- src/
  - index.js
  - App.jsx
  - components/
    - Header.jsx
    - AttributeDisplay.jsx
    - PlanStatus.jsx
    - DailyRecordForm.jsx
    - DailyRecordsTable.jsx
    - MorningCheckinForm.jsx
    - MorningCheckinTable.jsx
    - DailySummaryForm.jsx
    - DailySummaryTable.jsx
    - OverallRecordsTable.jsx
    - PlanProgress.jsx
    - TitlesDisplay.jsx
    - ChartsOverview.jsx
  - utils/
    - constants.js
    - calculations.js
    - storage.js
  - assets/
    - icons.svg
  - tailwind.config.js
  - index.css
*/

// package.json dependencies (for reference):
// {
//   "dependencies": {
//     "react": "^18.2.0",
//     "react-dom": "^18.2.0",
//     "react-scripts": "5.0.1",
//     "recharts": "^2.5.0",
//     "date-fns": "^2.29.3"
//   },
//   "devDependencies": {
//     "tailwindcss": "^3.2.0",
//     "autoprefixer": "^10.4.7",
//     "postcss": "^8.4.14"
//   }
// }

// tailwind.config.js
// module.exports = {
//   content: ["./src/**/*.{js,jsx,ts,tsx}", "./public/index.html"],
//   theme: { extend: {} },
//   plugins: []
// };

// src/index.css
// @tailwind base;
// @tailwind components;
// @tailwind utilities;

// src/index.js
import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);

// src/utils/constants.js
export const INITIAL_ATTRIBUTES = {
  intelligence: 0,
  knowledge: 0,
  stamina: 0,
  willpower: 0,
  charisma: 0,
  artificerXP: 1430,
};

export const PLAN_THRESHOLDS = {
  // thresholds for reverse rowing rules, stage definitions, etc.
  artificer: {
    decay: [{ days: 1, value: -50 }, { days: 3, value: -80 }, { days: 7, value: -100 }],
    bonus: [{ days: 3, rate: 0.1 }, { days: 7, rate: 0.25 }, { days: 30, rate: 0.4 }],
    stages: [
      { level: 'Lv.1 描形学徒', exp: 1500, sub: [450, 1050, 1500] },
      { level: 'Lv.2 构素学者', exp: 3000, sub: [2400, 3600, 4500] },
      { level: 'Lv.3 灵绘使徒', exp: 5000, sub: [6000, 8000, 9500] },
      { level: 'Lv.4 影纹术士', exp: 8000, sub: [11900, 15100, 17500] },
      { level: 'Lv.5 心象织者', exp: 12000, sub: [21100, 25900, 29500] },
      { level: 'Lv.6 空境画匠', exp: 18000, sub: [34900, 42100, 47500] },
      { level: 'Lv.7 律令绘爵', exp: 26000, sub: [55300, 65700, 73500] },
      { level: 'Lv.8 幻构师', exp: 36000, sub: [84300, 98700, 109500] },
    ],
  },
  truth: {
    decay: [
      { days: 1, knowledge: -0.1, intelligence: -0.5 },
      { days: 3, knowledge: -0.3, intelligence: -1.5 },
      { days: 7, knowledge: -1.0, intelligence: -5.0 },
    ],
    bonus: [{ days: 3, rate: 0.1 }, { days: 7, rate: 0.25 }, { days: 30, rate: 0.4 }],
    knowledgeStages: [
      { level: 'LV.1 灰袍学徒', need: 150, sub: [30, 75, 150] },
      { level: 'LV.2 白袍向导', need: 500, sub: [250, 400, 650] },
      { level: 'LV.3 墨衣学者', need: 1500, sub: [950, 1400, 2150] },
      { level: 'LV.4 青衿贤者', need: 4000, sub: [2950, 4150, 6150] },
      { level: 'LV.5 玄冕宗师', need: 10000, sub: [8150, 11150, 16150] },
    ],
    intelStages: [
      { level: 'LV.1 褐衣明理', need: 150, sub: [30, 75, 150] },
      { level: 'LV.2 缁衣慎思', need: 500, sub: [250, 400, 650] },
      { level: 'LV.3 朱衣审辩', need: 1500, sub: [950, 1400, 2150] },
      { level: 'LV.4 紫绶格物', need: 4000, sub: [2950, 4150, 6150] },
      { level: 'LV.5 金章弘道', need: 10000, sub: [8150, 11150, 16150] },
    ],
  },
  morning: {
    thresholds: [{ days: 3, will: 0.5, stamina: 0.2 }, { days: 7, will: 1, stamina: 0.5 }, { days: 30, will: 2, stamina: 1 }],
    titles: [
      { level: 'Lv.1', title: '星辉学徒', days: 7, bonus: { intelRate: 0.05 } },
      { level: 'Lv.2', title: '晨风哨卫', days: 30, bonus: { intelRate: 0.05, knowledgeRate: 0.05 } },
      { level: 'Lv.3', title: '夜穹守誓', days: 60, bonus: { allRate: 0.05 } },
      { level: 'Lv.4', title: '破晓骑士', days: 90, bonus: { allRate: 0.10 } },
      { level: 'Lv.5', title: '黎明星使', days: 120, bonus: { allRate: 0.15 } },
      { level: 'Lv.6', title: '永夜圣者', days: 180, bonus: { allRate: 0.20 } },
      { level: 'Lv.7', title: '晨曦领主', days: 365, bonus: { allRate: 0.25 } },
      { level: 'Lv.8', title: '时序主宰', days: 730, bonus: { allRate: 0.30 } },
    ],
    lose: [1, 3, 6, 9, 12, 18, 36, 73],
  },
  willTitles: [
    { level: 'LV.1', title: '晨曦微志', need: 50, bonus: 0.05 },
    { level: 'LV.2', title: '坚石守心', need: 200, bonus: 0.10 },
    { level: 'LV.3', title: '荆棘先锋', need: 500, bonus: 0.15 },
    { level: 'LV.4', title: '钢铁铸意', need: 800, bonus: 0.20 },
    { level: 'LV.5', title: '风暴不屈', need: 1200, bonus: 0.25 },
    { level: 'LV.6', title: '星辰恒志', need: 2000, bonus: 0.30 },
    { level: 'LV.7', title: '炽魂永燃', need: 3000, bonus: 0.40 },
    { level: 'LV.8', title: '无朽之心', need: 5000, bonus: 0.50 },
  ],
  charmTitles: [
    { level: 'LV.1', title: '萤火微光', need: 10, bonus: 0.05 },
    { level: 'LV.2', title: '晨露流辉', need: 50, bonus: 0.10 },
    { level: 'LV.3', title: '星芒初绽', need: 100, bonus: 0.15 },
    { level: 'LV.4', title: '银月颂光', need: 200, bonus: 0.20 },
    { level: 'LV.5', title: '日冕凝华', need: 300, bonus: 0.25 },
    { level: 'LV.6', title: '虹彩冠冕', need: 500, bonus: 0.30 },
    { level: 'LV.7', title: '天穹律光', need: 800, bonus: 0.40 },
    { level: 'LV.8', title: '万象圣辉', need: 1200, bonus: 0.50 },
  ],
};

// src/utils/storage.js
export const loadData = (key, defaultValue) => {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : defaultValue;
  } catch (e) {
    console.error('Failed to load from storage', e);
    return defaultValue;
  }
};

export const saveData = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (e) {
    console.error('Failed to save to storage', e);
  }
};

// src/utils/calculations.js
import { PLAN_THRESHOLDS } from './constants';

export const calculateDecayOrBonus = (history, planKey, today, type) => {
  // Given an array of date-value logs, determine consecutive days of success or failure
  // and return decay or bonus multiplier/value for today
  // type: 'artificer', 'truth', 'morning'
  // Implement logic based on thresholds
  return { multiplier: 1, delta: 0, status: 'normal' }; // placeholder
};

// src/components/Header.jsx
import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';

const Header = ({ startDate }) => {
  const [now, setNow] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setNow(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const diffDays = Math.floor((now - new Date(startDate)) / (1000 * 60 * 60 * 24));

  return (
    <div className="bg-blue-600 text-white p-4 flex justify-between items-center">
      <h1 className="text-2xl font-bold">人生游戏计划</h1>
      <div className="text-right">
        <div>{format(now, 'yyyy-MM-dd HH:mm:ss')}</div>
        <div>计划启动天数：{diffDays >= 0 ? diffDays : 0} 天</div>
      </div>
    </div>
  );
};

export default Header;

// src/components/AttributeDisplay.jsx
import React from 'react';

const AttributeDisplay = ({ attributes }) => {
  return (
    <div className="grid grid-cols-3 gap-4 p-4 bg-white rounded shadow">
      {Object.entries(attributes).map(([key, value]) => (
        <div key={key} className="flex flex-col items-center">
          <span className="capitalize text-gray-700">{key}</span>
          <span className="text-xl font-semibold text-blue-600">{value.toFixed(1)}</span>
        </div>
      ))}
    </div>
  );
};

export default AttributeDisplay;

// src/components/PlanProgress.jsx
import React from 'react';

const PlanProgress = ({ current, total, subStages }) => {
  let stage = '初级';
  let percent = (current / total) * 100;
  if (subStages[0] && current > subStages[0]) stage = '中级';
  if (subStages[1] && current > subStages[1]) stage = '高级';

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex justify-between">
        <span className="font-medium">小阶段：{stage}</span>
        <span className="font-medium">{percent.toFixed(1)}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded h-4 mt-1">
        <div className="bg-green-500 h-4 rounded" style={{ width: `${percent}%` }} />
      </div>
    </div>
  );
};

export default PlanProgress;

// src/components/PlanStatus.jsx
import React from 'react';
import PlanProgress from './PlanProgress';
import { PLAN_THRESHOLDS } from '../utils/constants';

const PlanStatus = ({ attributes, artificerXP, truthKnowledge, truthIntel }) => {
  // Determine current level and subStages for artificer
  const artificerStages = PLAN_THRESHOLDS.artificer.stages;
  let artLevel = artificerStages[0];
  for (let stage of artificerStages) {
    if (artificerXP <= stage.exp) { artLevel = stage; break; }
  }
  const artIndex = artificerStages.indexOf(artLevel);
  const artTotal = artLevel.exp;
  const artSubs = artLevel.sub;

  // Knowledge and Intel similar logic
  const knowStages = PLAN_THRESHOLDS.truth.knowledgeStages;
  let knowLevel = knowStages[0];
  for (let stage of knowStages) {
    const need = stage.need;
    if (truthKnowledge <= need) { knowLevel = stage; break; }
  }
  const knowTotal = knowLevel.need;
  const knowSubs = knowLevel.sub;

  const intelStages = PLAN_THRESHOLDS.truth.intelStages;
  let intelLevel = intelStages[0];
  for (let stage of intelStages) {
    const need = stage.need;
    if (truthIntel <= need) { intelLevel = stage; break; }
  }
  const intelTotal = intelLevel.need;
  const intelSubs = intelLevel.sub;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
      <div className="bg-white rounded shadow p-4">
        <h2 className="font-semibold mb-2">幻构师计划</h2>
        <div>当前等级：{artLevel.level}</div>
        <div>总需求经验：{artTotal}</div>
        <div>当前经验：{artificerXP}</div>
        <PlanProgress current={artificerXP} total={artTotal} subStages={artSubs} />
      </div>
      <div className="bg-white rounded shadow p-4">
        <h2 className="font-semibold mb-2">真理之路 (知识侧)</h2>
        <div>当前等级：{knowLevel.level}</div>
        <div>总需求知识：{knowTotal}</div>
        <div>当前知识：{truthKnowledge}</div>
        <PlanProgress current={truthKnowledge} total={knowTotal} subStages={knowSubs} />
      </div>
      <div className="bg-white rounded shadow p-4">
        <h2 className="font-semibold mb-2">真理之路 (智力侧)</n        </h2>
        <div>当前等级：{intelLevel.level}</div>
        <div>总需求智力：{intelTotal}</div>
        <div>当前智力：{truthIntel}</div>
        <PlanProgress current={truthIntel} total={intelTotal} subStages={intelSubs} />
      </div>
    </div>
  );
};

export default PlanStatus;

// src/components/TitlesDisplay.jsx
import React from 'react';
import { PLAN_THRESHOLDS } from '../utils/constants';

const TitlesDisplay = ({ willpower, charisma, morningStreak }) => {
  const willTitles = PLAN_THRESHOLDS.willTitles;
  let currentWillTitle = { title: '无', progress: 0, obtained: false };
  for (let i = 0; i < willTitles.length; i++) {
    if (willpower >= willTitles[i].need) {
      currentWillTitle = { title: willTitles[i].title, progress: 100, obtained: true };
    } else {
      const prevNeed = i > 0 ? willTitles[i - 1].need : 0;
      const range = willTitles[i].need - prevNeed;
      const prog = ((willpower - prevNeed) / range) * 100;
      currentWillTitle = { title: willTitles[i].title, progress: Math.max(0, prog), obtained: false };
      break;
    }
  }

  const charmTitles = PLAN_THRESHOLDS.charmTitles;
  let currentCharmTitle = { title: '无', progress: 0, obtained: false };
  for (let i = 0; i < charmTitles.length; i++) {
    if (charisma >= charmTitles[i].need) {
      currentCharmTitle = { title: charmTitles[i].title, progress: 100, obtained: true };
    } else {
      const prevNeed = i > 0 ? charmTitles[i - 1].need : 0;
      const range = charmTitles[i].need - prevNeed;
      const prog = ((charisma - prevNeed) / range) * 100;
      currentCharmTitle = { title: charmTitles[i].title, progress: Math.max(0, prog), obtained: false };
      break;
    }
  }

  const morningTitles = PLAN_THRESHOLDS.morning.titles;
  let currentMorningTitle = { title: '无', progress: 0, obtained: false };
  for (let i = 0; i < morningTitles.length; i++) {
    if (morningStreak >= morningTitles[i].days) {
      currentMorningTitle = { title: morningTitles[i].title, progress: 100, obtained: true };
    } else {
      const prevDays = i > 0 ? morningTitles[i - 1].days : 0;
      const range = morningTitles[i].days - prevDays;
      const prog = ((morningStreak - prevDays) / range) * 100;
      currentMorningTitle = { title: morningTitles[i].title, progress: Math.max(0, prog), obtained: false };
      break;
    }
  }

  const renderTitle = (data) => (
    <div className="bg-white rounded shadow p-4">
      <div className="flex justify-between items-center mb-2">
        <span className="font-semibold">{data.title}</span>
        {data.obtained ? <span className="text-green-500">已获得</span> : <span>{data.progress.toFixed(1)}%</span>}
      </div>
      <div className="w-full bg-gray-200 h-3 rounded">
        <div
          className={`${data.obtained ? 'bg-yellow-500' : 'bg-blue-500'} h-3 rounded`}
          style={{ width: `${data.progress}%` }}
        />
      </div>
    </div>
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
      <div>
        <h3 className="font-semibold mb-1">意志称号</h3>
        {renderTitle(currentWillTitle)}
      </div>
      <div>
        <h3 className="font-semibold mb-1">魅力称号</h3>
        {renderTitle(currentCharmTitle)}
      </div>
      <div>
        <h3 className="font-semibold mb-1">晨曦之约称号</h3>
        {renderTitle(currentMorningTitle)}
      </div>
    </div>
  );
};

export default TitlesDisplay;

// src/components/DailyRecordForm.jsx
import React, { useState } from 'react';
import { format } from 'date-fns';

const DailyRecordForm = ({ onSubmit }) => {
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [drawingMinutes, setDrawingMinutes] = useState(0);
  const [studyMinutes, setStudyMinutes] = useState(0);
  const [readPages, setReadPages] = useState(0);
  const [exerciseMinutes, setExerciseMinutes] = useState(0);
  const [negateInverse, setNegateInverse] = useState(false);
  const [negateWill, setNegateWill] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ date, drawingMinutes, studyMinutes, readPages, exerciseMinutes, negateInverse, negateWill });
    // Reset form or keep for editing
  };

  return (
    <form className="bg-white p-4 rounded shadow mb-4" onSubmit={handleSubmit}>
      <h3 className="font-semibold mb-2">每日记录</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-gray-700">日期</label>
          <input
            type="date"
            className="w-full border rounded p-2"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>
        <div>
          <label className="block text-gray-700">绘画时长 (分钟)</label>
          <input
            type="number"
            className="w-full border rounded p-2"
            value={drawingMinutes}
            onChange={(e) => setDrawingMinutes(parseInt(e.target.value, 10))}
          />
        </div>
        <div>
          <label className="block text-gray-700">学习时长 (分钟)</label>
          <input
            type="number"
            className="w-full border rounded p-2"
            value={studyMinutes}
            onChange={(e) => setStudyMinutes(parseInt(e.target.value, 10))}
          />
        </div>
        <div>
          <label className="block text-gray-700">阅读页数</label>
          <input
            type="number"
            className="w-full border rounded p-2"
            value={readPages}
            onChange={(e) => setReadPages(parseInt(e.target.value, 10))}
          />
        </div>
        <div>
          <label className="block text-gray-700">运动时长 (分钟)</label>
          <input
            type="number"
            className="w-full border rounded p-2"
            value={exerciseMinutes}
            onChange={(e) => setExerciseMinutes(parseInt(e.target.value, 10))}
          />
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="negateInverse"
            checked={negateInverse}
            onChange={() => setNegateInverse(!negateInverse)}
            className="mr-2"
          />
          <label htmlFor="negateInverse" className="text-gray-700">禁用逆水行舟</label>
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="negateWill"
            checked={negateWill}
            onChange={() => setNegateWill(!negateWill)}
            className="mr-2"
          />
          <label htmlFor="negateWill" className="text-gray-700">禁用意志增减</label>
        </div>
      </div>
      <div className="mt-4">
        <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded mr-2">提交</button>
        <button type="button" className="bg-gray-300 text-gray-700 px-4 py-2 rounded">重置</button>
      </div>
    </form>
  );
};

export default DailyRecordForm;

// src/components/DailyRecordsTable.jsx
import React from 'react';

const DailyRecordsTable = ({ records, onEdit, onDelete }) => {
  return (
    <div className="bg-white rounded shadow p-4">
      <h3 className="font-semibold mb-2">每日记录列表</h3>
      <table className="w-full table-auto">
        <thead>
          <tr>
            <th className="border px-2 py-1">日期</th>
            <th className="border px-2 py-1">绘画时长</th>
            <th className="border px-2 py-1">学习时长</th>
            <th className="border px-2 py-1">阅读页数</th>
            <th className="border px-2 py-1">运动时长</th>
            <th className="border px-2 py-1">幻构师经验增量</th>
            <th className="border px-2 py-1">智力增量</th>
            <th className="border px-2 py-1">知识增量</th>
            <th className="border px-2 py-1">体力增量</th>
            <th className="border px-2 py-1">意志增量</n        </tr>
        </thead>
        <tbody>
          {records.map((r) => (
            <tr key={r.date}>
              <td className="border px-2 py-1">{r.date}</td>
              <td className="border px-2 py-1">{r.drawingMinutes}</td>
              <td className="border px-2 py-1">{r.studyMinutes}</td>
              <td className="border px-2 py-1">{r.readPages}</td>
              <td className="border px-2 py-1">{r.exerciseMinutes}</td>
              <td className="border px-2 py-1">{r.artificerDelta}</td>
              <td className="border px-2 py-1">{r.intelDelta}</td>
              <td className="border px-2 py-1">{r.knowledgeDelta}</td>
              <td className="border px-2 py-1">{r.staminaDelta}</td>
              <td className="border px-2 py-1">{r.willDelta}</td>
              <td className="border px-2 py-1">{r.charismaDelta}</td>
              <td className="border px-2 py-1">{r.statusNote}</td>
              <td className="border px-2 py-1">
                <button onClick={() => onEdit(r.date)} className="text-blue-500 mr-2">编辑</button>
                <button onClick={() => onDelete(r.date)} className="text-red-500">删除</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DailyRecordsTable;

// src/components/MorningCheckinForm.jsx
import React, { useState } from 'react';
import { format } from 'date-fns';

const MorningCheckinForm = ({ onSubmit }) => {
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [sleptEarly, setSleptEarly] = useState(false);
  const [wokeEarly, setWokeEarly] = useState(false);
  const [specialCase, setSpecialCase] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ date, sleptEarly, wokeEarly, specialCase });
  };

  return (
    <form className="bg-white p-4 rounded shadow mb-4" onSubmit={handleSubmit}>
      <h3 className="font-semibold mb-2">晨曦之约打卡</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="sleptEarly"
            checked={sleptEarly}
            onChange={() => setSleptEarly(!sleptEarly)}
            className="mr-2"
          />
          <label htmlFor="sleptEarly" className="text-gray-700">前一天及时入睡</label>
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="wokeEarly"
            checked={wokeEarly}
            onChange={() => setWokeEarly(!wokeEarly)}
            className="mr-2"
          />
          <label htmlFor="wokeEarly" className="text-gray-700">当天及时起床</label>
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="specialCase"
            checked={specialCase}
            onChange={() => setSpecialCase(!specialCase)}
            className="mr-2"
          />
          <label htmlFor="specialCase" className="text-gray-700">特殊情况</label>
        </div>
      </div>
      <div className="mt-4">
        <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded mr-2">提交</button>
        <button type="button" className="bg-gray-300 text-gray-700 px-4 py-2 rounded">重置</button>
      </div>
    </form>
  );
};

export default MorningCheckinForm;

// src/components/MorningCheckinTable.jsx
import React from 'react';

const MorningCheckinTable = ({ records, onEdit, onDelete }) => {
  return (
    <div className="bg-white rounded shadow p-4 mt-4">
      <h3 className="font-semibold mb-2">晨曦之约历史记录</h3>
      <table className="w-full table-auto">
        <thead>
          <tr>
            <th className="border px-2 py-1">日期</th>
            <th className="border px-2 py-1">打卡结果</th>
            <th className="border px-2 py-1">额外奖励</th>
            <th className="border px-2 py-1">操作</th>
          </tr>
        </thead>
        <tbody>
          {records.map((r) => (
            <tr key={r.date}>
              <td className="border px-2 py-1">{r.date}</td>
              <td className="border px-2 py-1">{r.success ? '成功' : '失败'}</td>
              <td className="border px-2 py-1">{r.bonusNote}</td>
              <td className="border px-2 py-1">
                <button onClick={() => onEdit(r.date)} className="text-blue-500 mr-2">编辑</button>
                <button onClick={() => onDelete(r.date)} className="text-red-500">删除</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MorningCheckinTable;

// src/components/DailySummaryForm.jsx
import React, { useState } from 'react';
import { format } from 'date-fns';

const DailySummaryForm = ({ onSubmit }) => {
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [content, setContent] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ date, content });
  };

  return (
    <form className="bg-white p-4 rounded shadow mb-4" onSubmit={handleSubmit}>
      <h3 className="font-semibold mb-2">每日个人总结</h3>
      <div>
        <label className="block text-gray-700">日期</label>
        <input
          type="date"
          className="w-full border rounded p-2 mb-2"
          value={date}
          onChange={(e) => setDate(e.target.value)}
        />
      </div>
      <div>
        <label className="block text-gray-700">总结内容</label>
        <textarea
          className="w-full border rounded p-2"
          rows={4}
          value={content}
          onChange={(e) => setContent(e.target.value)}
        />
      </div>
      <div className="mt-4">
        <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded mr-2">提交</button>
        <button type="button" className="bg-gray-300 text-gray-700 px-4 py-2 rounded">重置</button>
      </div>
    </form>
  );
};

export default DailySummaryForm;

// src/components/DailySummaryTable.jsx
import React from 'react';

const DailySummaryTable = ({ summaries, onEdit, onDelete }) => {
  return (
    <div className="bg-white rounded shadow p-4 mt-4">
      <h3 className="font-semibold mb-2">每日总结历史记录</h3>
      <table className="w-full table-auto">
        <thead>
          <tr>
            <th className="border px-2 py-1">日期</th>
            <th className="border px-2 py-1">总结内容</th>
            <th className="border px-2 py-1">操作</th>
          </tr>
        </thead>
        <tbody>
          {summaries.map((s) => (
            <tr key={s.date}>
              <td className="border px-2 py-1">{s.date}</td>
              <td className="border px-2 py-1">{s.content}</td>
              <td className="border px-2 py-1">
                <button onClick={() => onEdit(s.date)} className="text-blue-500 mr-2">编辑</button>
                <button onClick={() => onDelete(s.date)} className="text-red-500">删除</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DailySummaryTable;

// src/components/OverallRecordsTable.jsx
import React from 'react';

const OverallRecordsTable = ({ overallRecords, onEdit, onDelete }) => {
  return (
    <div className="bg-white rounded shadow p-4 mt-4">
      <h3 className="font-semibold mb-2">每日总记录</h3>
      <table className="w-full table-auto text-sm">
        <thead>
          <tr>
            <th className="border px-2 py-1">日期</th>
            <th className="border px-2 py-1">幻构师进度</th>
            <th className="border px-2 py-1">知识侧进度</th>
            <th className="border px-2 py-1">智力侧进度</th>
            <th className="border px-2 py-1">晨曦打卡</th>
            <th className="border px-2 py-1">总结</th>
            <th className="border px-2 py-1">任务状态概览</th>
            <th className="border px-2 py-1">操作</th>
          </tr>
        </thead>
        <tbody>
          {overallRecords.map((r) => (
            <tr key={r.date}>
              <td className="border px-2 py-1">{r.date}</td>
              <td className="border px-2 py-1">{r.artProgress}</td>
              <td className="border px-2 py-1">{r.knowProgress}</td>
              <td className="border px-2 py-1">{r.intelProgress}</td>
              <td className="border px-2 py-1">{r.morningResult}</td>
              <td className="border px-2 py-1">{r.summaryPreview}</td>
              <td className="border px-2 py-1">{r.overviewStatus}</td>
              <td className="border px-2 py-1">
                <button onClick={() => onEdit(r.date)} className="text-blue-500 mr-2">编辑</button>
                <button onClick={() => onDelete(r.date)} className="text-red-500">删除</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default OverallRecordsTable;

// src/components/ChartsOverview.jsx
import React, { useState } from 'react';
import { LineChart, BarChart, Line, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Legend } from 'recharts';

const ChartsOverview = ({ data }) => {
  const [view, setView] = useState('monthly'); // 'monthly', 'yearly', 'overall'
  const [type, setType] = useState('attribute'); // 'attribute', 'xp'

  // Filter data based on view
  const filteredData = data; // implement filter logic

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
      <div className="bg-white rounded shadow p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-semibold">{type === 'attribute' ? '基础属性提升趋势' : '幻构师经验趋势'}</h3>
          <div>
            <select value={view} onChange={(e) => setView(e.target.value)} className="border rounded p-1 mr-2">
              <option value="monthly">每月</option>
              <option value="yearly">每年</option>
              <option value="overall">过往</option>
            </select>
            <select value={type} onChange={(e) => setType(e.target.value)} className="border rounded p-1">
              <option value="attribute">基础属性</option>
              <option value="xp">幻构师经验</option>
            </select>
          </div>
        </div>
        <ResponsiveContainer width="100%" height={300}>
          {type === 'attribute' ? (
            <LineChart data={filteredData}>
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="intelligence" stroke="#8884d8" />
              <Line type="monotone" dataKey="knowledge" stroke="#82ca9d" />
              <Line type="monotone" dataKey="stamina" stroke="#ffc658" />
              <Line type="monotone" dataKey="willpower" stroke="#ff7300" />
              <Line type="monotone" dataKey="charisma" stroke="#387908" />
            </LineChart>
          ) : (
            <BarChart data={filteredData}>
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="artificerXP" />
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
      <div className="bg-white rounded shadow p-4">
        {/* Optional comparison chart or additional view */}
      </div>
    </div>
  );
};

export default ChartsOverview;

// src/App.jsx
import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import AttributeDisplay from './components/AttributeDisplay';
import PlanStatus from './components/PlanStatus';
import TitlesDisplay from './components/TitlesDisplay';
import DailyRecordForm from './components/DailyRecordForm';
import DailyRecordsTable from './components/DailyRecordsTable';
import MorningCheckinForm from './components/MorningCheckinForm';
import MorningCheckinTable from './components/MorningCheckinTable';
import DailySummaryForm from './components/DailySummaryForm';
import DailySummaryTable from './components/DailySummaryTable';
import OverallRecordsTable from './components/OverallRecordsTable';
import ChartsOverview from './components/ChartsOverview';
import { INITIAL_ATTRIBUTES } from './utils/constants';
import { loadData, saveData } from './utils/storage';

const App = () => {
  const [startDate, setStartDate] = useState(loadData('startDate', new Date().toISOString().split('T')[0]));
  const [attributes, setAttributes] = useState(loadData('attributes', INITIAL_ATTRIBUTES));
  const [artificerXP, setArtificerXP] = useState(loadData('artificerXP', INITIAL_ATTRIBUTES.artificerXP));
  const [truthKnowledge, setTruthKnowledge] = useState(loadData('truthKnowledge', 0));
  const [truthIntel, setTruthIntel] = useState(loadData('truthIntel', 0));
  const [willpower, setWillpower] = useState(loadData('willpower', 0));
  const [charisma, setCharisma] = useState(loadData('charisma', 0));
  // Records
  const [dailyRecords, setDailyRecords] = useState(loadData('dailyRecords', []));
  const [checkinRecords, setCheckinRecords] = useState(loadData('checkinRecords', []));
  const [summaries, setSummaries] = useState(loadData('summaries', []));
  const [overallRecords, setOverallRecords] = useState(loadData('overallRecords', []));

  useEffect(() => { saveData('startDate', startDate); }, [startDate]);
  useEffect(() => { saveData('attributes', attributes); }, [attributes]);
  useEffect(() => { saveData('artificerXP', artificerXP); }, [artificerXP]);
  useEffect(() => { saveData('truthKnowledge', truthKnowledge); }, [truthKnowledge]);
  useEffect(() => { saveData('truthIntel', truthIntel); }, [truthIntel]);
  useEffect(() => { saveData('willpower', willpower); }, [willpower]);
  useEffect(() => { saveData('charisma', charisma); }, [charisma]);
  useEffect(() => { saveData('dailyRecords', dailyRecords); }, [dailyRecords]);
  useEffect(() => { saveData('checkinRecords', checkinRecords); }, [checkinRecords]);
  useEffect(() => { saveData('summaries', summaries); }, [summaries]);
  useEffect(() => { saveData('overallRecords', overallRecords); }, [overallRecords]);

  const handleDailyRecordSubmit = (record) => {
    // Calculate deltas based on rules
    // Placeholder: straight conversions
    const artDelta = record.drawingMinutes > 0 ? (record.drawingMinutes / 60) * 10 : 0;
    const intelDelta = record.studyMinutes > 0 ? record.studyMinutes / 60 : 0;
    const knowledgeDelta = record.readPages > 0 ? record.readPages / 10 : 0;
    const staminaDelta = record.exerciseMinutes > 0 ? record.exerciseMinutes / 60 : 0;
    const willDelta = 0; // Implement reverse rowing and continuous logic
    const charismaDelta = (intelDelta + knowledgeDelta + staminaDelta + willDelta) * 0.1;
    const statusNote = '';
    const newRec = { ...record, artificerDelta: artDelta, intelDelta, knowledgeDelta, staminaDelta, willDelta, charismaDelta, statusNote };
    setDailyRecords([...dailyRecords.filter((r) => r.date !== record.date), newRec]);
    // Update attributes
    setArtificerXP((prev) => prev + artDelta);
    setAttributes((prev) => ({
      ...prev,
      intelligence: prev.intelligence + intelDelta,
      knowledge: prev.knowledge + knowledgeDelta,
      stamina: prev.stamina + staminaDelta,
      willpower: prev.willpower + willDelta,
      charisma: prev.charisma + charismaDelta,
    }));
    setTruthKnowledge((prev) => prev + knowledgeDelta);
    setTruthIntel((prev) => prev + intelDelta);
    setWillpower((prev) => prev + willDelta);
    setCharisma((prev) => prev + charismaDelta);
    // Add to overallRecords
    const overall = {
      date: record.date,
      artProgress: `${artDelta.toFixed(1)}`,
      knowProgress: `${knowledgeDelta.toFixed(1)}`,
      intelProgress: `${intelDelta.toFixed(1)}`,
      morningResult: '',
      summaryPreview: '',
      overviewStatus: '',
    };
    setOverallRecords([...overallRecords.filter((r) => r.date !== record.date), overall]);
  };

  const handleCheckinSubmit = (record) => {
    // Determine success and bonus
    const success = record.specialCase || (record.sleptEarly && record.wokeEarly);
    const bonusNote = success ? `${record.sleptEarly ? '体力+0.5 ' : ''}${record.wokeEarly ? '意志****' : ''}` : '';
    const newRec = { ...record, success, bonusNote };
    setCheckinRecords([...checkinRecords.filter((r) => r.date !== record.date), newRec]);
    // Update streak and attributes
    // Placeholder: +0 for now
  };

  const handleSummarySubmit = (summary) => {
    setSummaries([...summaries.filter((s) => s.date !== summary.date), summary]);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header startDate={startDate} />
      <div className="container mx-auto p-4">
        <AttributeDisplay attributes={attributes} />
        <PlanStatus attributes={attributes} artificerXP={artificerXP} truthKnowledge={truthKnowledge} truthIntel={truthIntel} />
        <TitlesDisplay willpower={willpower} charisma={charisma} morningStreak={checkinRecords.filter((r) => r.success).length} />
        <div className="flex justify-end mb-4">
          <button onClick={() => window.location.reload()} className="bg-red-500 text-white px-4 py-2 rounded mr-2">重置所有数据</button>
          <button onClick={() => alert('数据已保存')} className="bg-green-500 text-white px-4 py-2 rounded">保存数据</button>
        </div>
        <DailyRecordForm onSubmit={handleDailyRecordSubmit} />
        <DailyRecordsTable records={dailyRecords} onEdit={(date) => {}} onDelete={(date) => setDailyRecords(dailyRecords.filter((r) => r.date !== date))} />
        <MorningCheckinForm onSubmit={handleCheckinSubmit} />
        <MorningCheckinTable records={checkinRecords} onEdit={(date) => {}} onDelete={(date) => setCheckinRecords(checkinRecords.filter((r) => r.date !== date))} />
        <DailySummaryForm onSubmit={handleSummarySubmit} />
        <DailySummaryTable summaries={summaries} onEdit={(date) => {}} onDelete={(date) => setSummaries(summaries.filter((s) => s.date !== date))} />
        <OverallRecordsTable overallRecords={overallRecords} onEdit={(date) => {}} onDelete={(date) => setOverallRecords(overallRecords.filter((r) => r.date !== date))} />
        <ChartsOverview data={[...dailyRecords.map((r) => ({ date: r.date, ...attributes, artificerXP }))]} />
      </div>
    </div>
  );
};

export default App;
