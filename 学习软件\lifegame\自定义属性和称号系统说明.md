# LifeGame 自定义属性和称号系统 v2.1 使用说明

## 🎯 系统概述

全新的自定义属性和称号系统让您可以根据个人需求创建专属的属性体系和成就系统，完全集成到现有的LifeGame生态中。

## ✨ 主要特性

### 🎯 自定义属性系统
- **灵活创建**：支持创建任意数量的自定义属性
- **多种计算方式**：手动输入、公式计算、依赖属性
- **完全集成**：与每日记录、任务系统、奖励惩罚系统无缝集成
- **个性化设置**：自定义图标、颜色、单位、精度等
- **智能排序**：支持拖拽调整显示顺序

### 🏆 自定义称号系统
- **多样条件**：支持单属性阈值、多属性组合、特殊成就等
- **丰富奖励**：提供属性效率加成、特殊效果等奖励
- **实时判定**：自动检测称号获得条件并显示进度
- **进度追踪**：清晰显示距离获得称号的进度
- **完美融合**：与现有称号系统协同工作

### 🔄 版本兼容性
- **数据安全**：完全兼容现有数据，无缝升级
- **向后兼容**：支持从任何历史版本导入数据
- **智能迁移**：自动处理数据结构变化

## 🚀 使用指南

### 访问自定义管理界面
1. 点击页面右上角的 **"⚙️ 自定义"** 按钮
2. 在弹出的管理界面中选择相应的选项卡

### 🎯 自定义属性管理

#### 创建自定义属性
1. 在"自定义属性"选项卡中点击 **"➕ 添加属性"**
2. 填写属性信息：
   - **属性名称**：如"专注力"、"创造力"等
   - **属性描述**：描述属性的含义和用途
   - **图标**：选择合适的emoji图标
   - **颜色**：设置属性的主题色
   - **初始值**：属性的起始数值
   - **单位**：如"点"、"%"、"小时"等
   - **小数位数**：显示精度设置
   - **计算方式**：
     - **手动输入**：在每日记录中手动输入增量
     - **公式计算**：基于其他属性自动计算
     - **依赖属性**：依赖其他属性的变化

#### 公式计算示例
```
intelligence * 0.5 + knowledge * 0.3
stamina + will * 0.8
(intelligence + knowledge) / 2
```

#### 编辑和管理属性
- **编辑**：点击属性卡片的"编辑"按钮
- **删除**：点击"删除"按钮（会提示确认）
- **排序**：拖拽属性卡片调整显示顺序

### 🏆 自定义称号管理

#### 创建自定义称号
1. 在"自定义称号"选项卡中点击 **"➕ 添加称号"**
2. 填写称号基本信息：
   - **称号名称**：如"学习达人"、"意志坚定者"
   - **称号描述**：描述获得称号的意义
   - **图标**：选择合适的emoji图标
   - **颜色**：设置称号的主题色

#### 设置获得条件
支持多种条件类型：

**属性阈值条件**
- 选择目标属性（基础属性或自定义属性）
- 设置比较符（≥、>、≤、<、=）
- 设置阈值数值

**多条件组合**
- 可添加多个条件
- 所有条件都满足时才能获得称号

#### 设置称号奖励
**效率加成奖励**
- 选择目标属性（可选择"全属性"）
- 设置加成比例（如0.05表示5%加成）
- 添加奖励描述

#### 称号进度显示
- 系统会自动计算并显示获得进度
- 实时更新，让您了解距离目标还有多远

### 📊 在每日记录中使用自定义属性

#### 手动输入属性
1. 在"每日记录"选项卡中，如果有手动输入类型的自定义属性
2. 会自动显示"🎯 自定义属性"区域
3. 点击"展开"按钮，输入当日的属性增量值
4. 提交记录时会自动计算并累加到总属性中

#### 公式计算属性
- 公式计算类型的属性会根据其他属性的增量自动计算
- 无需手动输入，系统会自动处理

### 🎮 在任务系统中使用自定义属性

#### 任务奖励和惩罚
- 在创建或编辑任务时，奖励和惩罚的属性选择中会包含自定义属性
- 可以设置完成任务获得自定义属性奖励
- 也可以设置失败时扣除自定义属性

#### 每日任务
- 每日任务的奖励系统同样支持自定义属性
- 可以设置每日完成特定任务获得自定义属性奖励

## 🎨 界面特色

### 一致的设计风格
- 采用与主界面相同的玻璃拟态效果
- 使用统一的主题色彩系统
- 保持现有界面的视觉连贯性

### 直观的交互体验
- 支持拖拽排序
- 快速编辑功能
- 清晰的状态反馈
- 友好的错误提示

### 响应式布局
- 适配不同屏幕尺寸
- 优化的移动端体验
- 流畅的动画效果

## 🔧 技术特性

### 数据持久化
- 所有自定义数据自动保存到localStorage
- 支持导入导出功能
- 完整的备份恢复机制

### 性能优化
- 高效的属性计算算法
- 智能的DOM更新策略
- 最小化的重复计算

### 安全性保障
- 公式计算的安全验证
- 数据结构完整性检查
- 错误处理和恢复机制

## 📋 使用示例

### 示例1：创建"专注力"属性
```
属性名称：专注力
描述：衡量专注学习和工作的能力
图标：🎯
颜色：#3b82f6
初始值：0
单位：点
计算方式：手动输入
```

### 示例2：创建"学习效率"属性
```
属性名称：学习效率
描述：基于智力和意志的综合学习能力
图标：📈
颜色：#10b981
计算方式：公式计算
公式：intelligence * 0.6 + will * 0.4
```

### 示例3：创建"专注大师"称号
```
称号名称：专注大师
描述：专注力达到一定水平的证明
图标：🧘
颜色：#8b5cf6
获得条件：专注力 ≥ 100
奖励：智力获取效率+10%
```

## ⚠️ 注意事项

### 属性创建建议
1. **合理命名**：使用简洁明确的属性名称
2. **适当精度**：根据属性特性选择合适的小数位数
3. **谨慎公式**：确保公式计算的合理性和安全性

### 称号设计建议
1. **渐进式目标**：设置合理的获得条件，避免过于困难或简单
2. **有意义的奖励**：提供有实际价值的效率加成
3. **清晰的描述**：让用户明确知道称号的价值和获得方式

### 数据安全
1. **定期备份**：建议定期导出数据进行备份
2. **谨慎删除**：删除自定义属性或称号前请确认
3. **测试功能**：使用测试按钮验证系统功能正常

## 🆕 版本更新

### v2.1.0 新增功能
- ✅ 完整的自定义属性系统
- ✅ 强大的自定义称号系统
- ✅ 与现有系统的完美集成
- ✅ 增强的版本兼容性
- ✅ 全面的测试验证机制

---

**版本**: v2.1.0  
**更新日期**: 2025-07-02  
**兼容性**: 完全向后兼容，支持所有历史版本数据
