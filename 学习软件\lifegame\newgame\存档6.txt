<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>人生游戏计划</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Chart.js CDN -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100 text-gray-800">

  <!-- 容器 -->
  <div class="container mx-auto p-6">

    <!-- 顶部 Header：当前时间 & 计划启动天数 & Import/Export/Reset -->
    <div class="bg-white shadow p-4 mb-6 rounded-lg">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold">人生游戏计划</h1>
          <p id="currentTime" class="text-gray-600 mt-1">当前时间：--</p>
          <p id="daysSince" class="text-gray-600">计划启动天数：--</p>
        </div>
        <div class="space-x-4">
          <label class="mr-2 text-gray-700">计划开始日期：</label>
          <input id="startDateInput" type="date" class="border rounded px-2 py-1" />
        </div>
      </div>
      <div class="flex justify-end items-center mt-4 space-x-2">
        <button id="exportBtn" class="bg-green-500 text-white px-4 py-2 rounded">导出数据</button>
        <label for="importFile" class="bg-blue-500 text-white px-4 py-2 rounded cursor-pointer">导入数据</label>
        <input type="file" id="importFile" accept=".json" class="hidden" />
        <button id="resetBtn" class="bg-red-500 text-white px-4 py-2 rounded">重置数据</button>
      </div>
    </div>

    <!-- 状态概览 -->
    <div class="bg-white shadow p-4 mb-6 rounded-lg">
      <h2 class="text-xl font-semibold mb-3">当前状态概览 <button id="overviewInfoBtn" class="text-gray-400 hover:text-gray-600 ml-2">❔</button></h2>
      <div id="overviewInfo" class="hidden mb-4 p-3 border-l-4 border-blue-500 bg-blue-50 rounded">
        <p class="text-sm"><strong>基础属性提升方法：</strong></p>
        <ul class="text-sm list-disc list-inside space-y-1">
          <li>智力：每学习1小时 +1 智力。</li>
          <li>知识：每阅读1页 +0.1 知识。</li>
          <li>体力：每运动1小时 +1 体力。</li>
          <li>幻构师经验：每绘画1小时 +10 经验。</li>
          <li>意志：单项行动连续3天 +1/天，连续7天 +2/天，连续30天 +3/天；失败连续1天 -1/天，连续3天 -2/天，连续7天 -3/天。</li>
          <li>魅力：基础属性之和每增加1 +0.1 魅力。</li>
        </ul>
        <p class="text-sm mt-2"><strong>称号获取规则：</strong>见称号系统板块。</p>
      </div>
      <div class="grid grid-cols-2 gap-6">
        <!-- 属性板块 -->
        <div class="bg-gray-50 p-4 shadow rounded">
          <h3 class="font-semibold mb-2">属性</h3>
          <ul class="space-y-1 text-sm">
            <li>智力：<span id="attrIntelligence">0.0</span></li>
            <li>知识：<span id="attrKnowledge">0.0</span></li>
            <li>体力：<span id="attrStamina">0.0</span></li>
            <li>意志：<span id="attrWillpower">0.0</span></li>
            <li>魅力：<span id="attrCharisma">0.0</span></li>
            <li>幻构师经验：<span id="attrArtisanExp">1460.0</span></li>
            <li>阅识：<span id="attrReadingExp">0.0</span></li>
          </ul>
        </div>
        <!-- 职业阶段板块 -->
        <div class="bg-gray-50 p-4 shadow rounded">
          <h3 class="font-semibold mb-2">职业水平</h3>
          <div class="space-y-4 text-sm">
            <!-- 幻构师 -->
            <div>
              <p class="font-medium">幻构师计划</p>
              <p id="artisanProgressText">--</p>
              <div class="w-full bg-gray-200 h-2 rounded mt-1">
                <div id="artisanProgressBar" class="bg-blue-500 h-2 rounded" style="width: 0%;"></div>
              </div>
            </div>
            <!-- 真理之路 知识侧 -->
            <div>
              <p class="font-medium">真理之路（知识侧）</p>
              <p id="truthKnowProgressText">--</p>
              <div class="w-full bg-gray-200 h-2 rounded mt-1">
                <div id="truthKnowProgressBar" class="bg-green-500 h-2 rounded" style="width: 0%;"></div>
              </div>
            </div>
            <!-- 真理之路 智力侧 -->
            <div>
              <p class="font-medium">真理之路（智力侧）</p>
              <p id="truthIntelProgressText">--</p>
              <div class="w-full bg-gray-200 h-2 rounded mt-1">
                <div id="truthIntelProgressBar" class="bg-purple-500 h-2 rounded" style="width: 0%;"></div>
              </div>
            </div>
          </div>
        </div>
        <!-- 称号板块 -->
        <div class="bg-gray-50 p-4 shadow rounded">
          <h3 class="font-semibold mb-2">称号</h3>
          <ul class="space-y-1 text-sm">
            <li>晨曦之约称号：<span id="titleMorning">无</span> <span id="titleMorningPerc"></span></li>
            <li>意志称号：<span id="titleWill">无</span> <span id="titleWillPerc"></span></li>
            <li>魅力称号：<span id="titleCharisma">无</span> <span id="titleCharismaPerc"></span></li>
            <li>阅识称号：<span id="titleReading">无</span> <span id="titleReadingPerc"></span></li>
          </ul>
        </div>
        <!-- 逆水行舟板块 -->
        <div class="bg-gray-50 p-4 shadow rounded">
          <h3 class="font-semibold mb-2">逆水行舟状态</h3>
          <p class="text-sm">若近期无活动记录，属性将逐渐向初始水平回落。</p>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="bg-white shadow rounded mb-6">
      <div class="flex border-b">
        <button class="tab-btn flex-1 py-2 text-center hover:bg-gray-100" data-tab="dailyRecord">每日记录</button>
        <button class="tab-btn flex-1 py-2 text-center hover:bg-gray-100" data-tab="morningRoutine">晨曦之约</button>
        <button class="tab-btn flex-1 py-2 text-center hover:bg-gray-100" data-tab="dailySummary">每日总结</button>
        <!-- 添加任务系统标签 -->
        <button class="tab-btn flex-1 py-2 text-center hover:bg-gray-100" data-tab="taskSystem">任务系统</button>
      </div>

      <!-- 每日记录 Tab -->
      <div id="dailyRecord" class="tab-content p-4">
        <h3 class="text-lg font-semibold mb-3">每日记录</h3>
        <!-- 输入表单 -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm">学习时长（分钟）</label>
            <input id="inputStudy" type="number" min="0" value="0"
                   class="border rounded w-full px-2 py-1" />
            <label class="inline-flex items-center mt-1 text-sm">
              <input id="chkStudyWill" type="checkbox" checked class="mr-1" />
              计入意志
            </label>
          </div>
          <div>
            <label class="block text-sm">阅读页数</label>
            <input id="inputReading" type="number" min="0" value="0"
                   class="border rounded w-full px-2 py-1" />
            <label class="inline-flex items-center mt-1 text-sm">
              <input id="chkReadingWill" type="checkbox" checked class="mr-1" />
              计入意志
            </label>
          </div>
          <div>
            <label class="block text-sm">绘画时长（分钟）</label>
            <input id="inputDrawing" type="number" min="0" value="0"
                   class="border rounded w-full px-2 py-1" />
            <label class="inline-flex items-center mt-1 text-sm">
              <input id="chkDrawingWill" type="checkbox" checked class="mr-1" />
              计入意志
            </label>
          </div>
          <div>
            <label class="block text-sm">运动时长（分钟）</label>
            <input id="inputExercise" type="number" min="0" value="0"
                   class="border rounded w-full px-2 py-1" />
            <label class="inline-flex items-center mt-1 text-sm">
              <input id="chkExerciseWill" type="checkbox" checked class="mr-1" />
              计入意志
            </label>
          </div>
          <div>
            <label class="block text-sm">其他阅读时长（分钟）</label>
            <input id="inputOtherReading" type="number" min="0" value="0"
                   class="border rounded w-full px-2 py-1" />
            <label class="inline-flex items-center mt-1 text-sm">
              <input id="chkOtherReadingWill" type="checkbox" checked class="mr-1" />
              计入意志
            </label>
          </div>
          <div>
            <label class="block text-sm">观看影视时长（分钟）</label>
            <input id="inputWatching" type="number" min="0" value="0"
                   class="border rounded w-full px-2 py-1" />
            <label class="inline-flex items-center mt-1 text-sm">
              <input id="chkWatchingWill" type="checkbox" checked class="mr-1" />
              计入意志
            </label>
          </div>
        </div>
        <button id="saveDailyRecord" class="bg-blue-500 text-white px-4 py-2 rounded">保存</button>

        <!-- 历史记录 -->
        <div class="mt-6">
          <div class="flex justify-between items-center mb-2">
            <h4 class="font-semibold text-sm">历史记录</h4>
            <button id="toggleDailyHistory" class="text-sm text-blue-500">展开</button>
          </div>
          <div id="dailyHistory" class="hidden">
            <table class="w-full text-left border-collapse text-sm">
              <thead>
                <tr>
                  <th class="border px-2 py-1">日期</th>
                  <th class="border px-2 py-1">学习</th>
                  <th class="border px-2 py-1">阅读</th>
                  <th class="border px-2 py-1">绘画</th>
                  <th class="border px-2 py-1">运动</th>
                  <th class="border px-2 py-1">智力±</th>
                  <th class="border px-2 py-1">知识±</th>
                  <th class="border px-2 py-1">体力±</th>
                  <th class="border px-2 py-1">意志±</th>
                  <th class="border px-2 py-1">魅力±</th>
                  <th class="border px-2 py-1">经验±</th>
                  <th class="border px-2 py-1">操作</th>
                </tr>
              </thead>
              <tbody id="dailyHistoryBody"></tbody>
            </table>
            <div id="dailyHistoryPager" class="mt-2 flex justify-center space-x-2"></div>
          </div>
        </div>
      </div>

      <!-- 晨曦之约 Tab -->
      <div id="morningRoutine" class="tab-content hidden p-4">
        <h3 class="text-lg font-semibold mb-3">晨曦之约计划</h3>
        <div class="space-y-2 mb-4">
          <label class="inline-flex items-center text-sm">
            <input id="chkSleptOnTime" type="checkbox" class="mr-1" />
            前一天及时入睡
          </label>
          <label class="inline-flex items-center text-sm">
            <input id="chkWokeOnTime" type="checkbox" class="mr-1" />
            今日及时起床
          </label>
          <label class="inline-flex items-center text-sm">
            <input id="chkSpecial" type="checkbox" class="mr-1" />
            特殊情况（算作成功）
          </label>
        </div>
        <button id="saveMorningRoutine" class="bg-blue-500 text-white px-4 py-2 rounded">保存</button>

        <!-- 历史记录 -->
        <div class="mt-6">
          <div class="flex justify-between items-center mb-2">
            <h4 class="font-semibold text-sm">历史记录</h4>
            <button id="toggleMorningHistory" class="text-sm text-blue-500">展开</button>
          </div>
          <div id="morningHistory" class="hidden">
            <table class="w-full text-left border-collapse text-sm">
              <thead>
                <tr>
                  <th class="border px-2 py-1">日期</th>
                  <th class="border px-2 py-1">入睡</th>
                  <th class="border px-2 py-1">起床</th>
                  <th class="border px-2 py-1">特殊</th>
                  <th class="border px-2 py-1">意志±</th>
                  <th class="border px-2 py-1">体力±</th>
                  <th class="border px-2 py-1">操作</th>
                </tr>
              </thead>
              <tbody id="morningHistoryBody"></tbody>
            </table>
            <div id="morningHistoryPager" class="mt-2 flex justify-center space-x-2"></div>
          </div>
        </div>
      </div>

      <!-- 每日总结 Tab -->
      <div id="dailySummary" class="tab-content hidden p-4">
        <h3 class="text-lg font-semibold mb-3">每日总结</h3>
        <textarea id="summaryText" placeholder="输入今日总结..."
                  class="w-full border rounded px-2 py-1 mb-2 h-24"></textarea>
        <button id="saveSummary" class="bg-blue-500 text-white px-4 py-2 rounded mb-4">保存</button>
        <div class="mt-4">
          <div class="flex justify-between items-center mb-2">
            <h4 class="font-semibold text-sm">历史记录</h4>
            <button id="toggleSummaryHistory" class="text-sm text-blue-500">展开</button>
          </div>
          <div id="summaryHistory" class="hidden">
            <ul id="summaryHistoryBody" class="space-y-2 text-sm"></ul>
            <div id="summaryHistoryPager" class="mt-2 flex justify-center space-x-2"></div>
          </div>
        </div>
      </div>

      <!-- 任务系统 Tab -->
      <div id="taskSystem" class="tab-content hidden p-4">
        <h3 class="text-lg font-semibold mb-3">任务系统</h3>
        
        <!-- 任务创建表单 -->
        <div class="bg-gray-50 p-4 rounded mb-4">
          <h4 class="font-medium mb-3">创建新任务</h4>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm mb-1">任务名称 *</label>
              <input type="text" id="taskName" class="w-full border rounded px-2 py-1" required>
            </div>
            <div>
              <label class="block text-sm mb-1">任务描述</label>
              <input type="text" id="taskDesc" class="w-full border rounded px-2 py-1">
            </div>
            <div>
              <label class="block text-sm mb-1">任务类型 *</label>
              <select id="taskType" class="w-full border rounded px-2 py-1" required>
                <option value="">请选择</option>
                <option value="artisan">幻构师计划</option>
                <option value="truth">真理之路计划</option>
                <option value="morning">晨曦之约计划</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div>
              <label class="block text-sm mb-1">任务周期 *</label>
              <select id="taskPeriod" class="w-full border rounded px-2 py-1" required>
                <option value="">请选择</option>
                <option value="short">短期</option>
                <option value="long">长期</option>
              </select>
            </div>
            <div>
              <label class="block text-sm mb-1">目标类型 *</label>
              <select id="taskTargetType" class="w-full border rounded px-2 py-1" required>
                <option value="">请选择</option>
                <option value="study">学习时长(分钟)</option>
                <option value="reading">阅读专著页数</option>
                <option value="drawing">绘画时长(分钟)</option>
                <option value="exercise">运动时长(分钟)</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div>
              <label class="block text-sm mb-1">目标数值 *</label>
              <input type="number" id="taskTargetValue" min="1" class="w-full border rounded px-2 py-1" required>
            </div>
            <div>
              <label class="block text-sm mb-1">截止日期</label>
              <input type="date" id="taskDeadline" class="w-full border rounded px-2 py-1">
            </div>
          </div>
          
          <!-- 奖励设置 -->
          <div class="mt-4">
            <h5 class="font-medium mb-2">奖励设置</h5>
            <div id="rewardsList" class="space-y-2"></div>
            <button id="addReward" class="text-sm text-blue-500 mt-2">+ 添加奖励</button>
          </div>
          
          <!-- 惩罚设置 -->
          <div class="mt-4">
            <h5 class="font-medium mb-2">惩罚设置</h5>
            <div id="penaltiesList" class="space-y-2"></div>
            <button id="addPenalty" class="text-sm text-blue-500 mt-2">+ 添加惩罚</button>
          </div>
          
          <button id="createTask" class="bg-blue-500 text-white px-4 py-2 rounded mt-4">创建任务</button>
        </div>

        <!-- 未完成任务列表 -->
        <div class="mb-6">
          <h4 class="font-medium mb-3">未完成任务</h4>
          <div class="overflow-x-auto">
            <table class="w-full text-sm">
              <thead>
                <tr class="bg-gray-50">
                  <th class="px-4 py-2 text-left">任务名称</th>
                  <th class="px-4 py-2 text-left">类型</th>
                  <th class="px-4 py-2 text-left">进度</th>
                  <th class="px-4 py-2 text-left">剩余天数</th>
                  <th class="px-4 py-2 text-left">奖惩</th>
                  <th class="px-4 py-2 text-left">操作</th>
                </tr>
              </thead>
              <tbody id="pendingTasksList"></tbody>
            </table>
          </div>
        </div>

        <!-- 已完成任务列表 -->
        <div>
          <h4 class="font-medium mb-3">已完成任务</h4>
          <div class="overflow-x-auto">
            <table class="w-full text-sm">
              <thead>
                <tr class="bg-gray-50">
                  <th class="px-4 py-2 text-left">任务名称</th>
                  <th class="px-4 py-2 text-left">类型</th>
                  <th class="px-4 py-2 text-left">完成日期</th>
                  <th class="px-4 py-2 text-left">获得奖励</th>
                </tr>
              </thead>
              <tbody id="completedTasksList"></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 每日总记录 汇总 -->
    <div class="bg-white shadow p-4 mb-6 rounded-lg">
      <h3 class="text-lg font-semibold mb-3">每日总记录</h3>
      <table class="w-full text-left border-collapse text-sm">
        <thead>
          <tr>
            <th class="border px-2 py-1">日期</th>
            <th class="border px-2 py-1">智力±</th>
            <th class="border px-2 py-1">知识±</th>
            <th class="border px-2 py-1">体力±</th>
            <th class="border px-2 py-1">意志±</th>
            <th class="border px-2 py-1">魅力±</th>
            <th class="border px-2 py-1">幻构师经验±</th>
            <th class="border px-2 py-1">晨曦打卡</th>
            <th class="border px-2 py-1">每日总结</th>
          </tr>
        </thead>
        <tbody id="dailyAggregateBody">
          <tr>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
            <td class="border px-2 py-1">--</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 职业阶段 -->
    <div class="bg-white shadow p-4 mb-6 rounded-lg">
      <h3 class="text-lg font-semibold mb-3">职业阶段</h3>
      <div class="space-y-4 text-sm">
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">幻构师计划</p>
          <p id="cpArtisanText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="cpArtisanBar" class="bg-blue-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">真理之路（知识侧）</p>
          <p id="cpTruthKnowText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="cpTruthKnowBar" class="bg-green-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">真理之路（智力侧）</p>
          <p id="cpTruthIntelText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="cpTruthIntelBar" class="bg-purple-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 称号系统 -->
    <div class="bg-white shadow p-4 mb-6 rounded-lg">
      <h3 class="text-lg font-semibold mb-3">称号系统</h3>
      <div class="space-y-4 text-sm">
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">晨曦之约称号</p>
          <p id="tsMorningText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="tsMorningBar" class="bg-blue-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">意志称号</p>
          <p id="tsWillText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="tsWillBar" class="bg-purple-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">魅力称号</p>
          <p id="tsCharismaText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="tsCharismaBar" class="bg-yellow-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
        <div class="bg-gray-50 p-3 rounded shadow">
          <p class="font-medium">阅识称号</p>
          <p id="tsReadingText">--</p>
          <div class="w-full bg-gray-200 h-2 rounded mt-1">
            <div id="tsReadingBar" class="bg-pink-500 h-2 rounded" style="width: 0%;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阶段性总结：图表 -->
    <div class="bg-white shadow p-4 mb-6 rounded-lg">
      <h3 class="text-lg font-semibold mb-3">阶段性总结</h3>
      <div class="flex space-x-4 mb-4">
        <button id="chartTypeBar" class="bg-blue-500 text-white px-3 py-1 rounded">条状图</button>
        <button id="chartTypeLine" class="bg-green-500 text-white px-3 py-1 rounded">折线图</button>
      </div>
      <canvas id="trendChart"></canvas>
    </div>

  </div>

  <script>
    /************* 辅助函数与常量 *************/

    // localStorage 读取与写入
    function loadData(key, defaultValue) {
      const raw = localStorage.getItem(key);
      if (!raw) return defaultValue;
      try {
        return JSON.parse(raw);
      } catch {
        return defaultValue;
      }
    }
    function saveData(key, value) {
      localStorage.setItem(key, JSON.stringify(value));
    }

    // 计算天数差：从 startDate (YYYY-MM-DD) 到今天
    function computeDaysSince(startDate) {
      if (!startDate) return 0;
      const start = new Date(startDate);
      const today = new Date();
      const diff = today - start;
      return Math.floor(diff / (1000 * 60 * 60 * 24));
    }

    // 获取今天日期字符串 YYYY-MM-DD
    function getToday() {
      return new Date().toISOString().slice(0, 10);
    }

    // 基础属性默认值
    const DEFAULT_ATTRS = {
      intelligence: 0,
      knowledge: 0,
      stamina: 0,
      willpower: 0,
      charisma: 0,
      artisanExp: 1460,
      readingExp: 0  // 新增阅识属性
    };

    // 职业阶段配置
    const ARTISAN_PLAN = [
      { level: 1, total: 1500, name: '描形学徒', ranges: [[1,450], [451,1050], [1051,1500]] },
      { level: 2, total: 3000, name: '构素学者', ranges: [[1501,2400], [2401,3600], [3601,4500]] },
      { level: 3, total: 5000, name: '灵绘使徒', ranges: [[4501,6000], [6001,8000], [8001,9500]] },
      { level: 4, total: 8000, name: '影纹术士', ranges: [[9501,11900], [11901,15100], [15101,17500]] },
      { level: 5, total: 12000, name: '心象织者', ranges: [[17501,21100], [21101,25900], [25901,29500]] },
      { level: 6, total: 18000, name: '空境画匠', ranges: [[29501,34900], [34901,42100], [42101,47500]] },
      { level: 7, total: 26000, name: '律令绘爵', ranges: [[47501,55300], [55301,65700], [65701,73500]] },
      { level: 8, total: 36000, name: '幻构师', ranges: [[73501,84300], [84301,98700], [98701,109500]] }
    ];

    const TRUTH_KNOW_PLAN = [
      { level: 1, total: 150, name: '灰袍学徒', ranges: [[1,30], [31,75], [76,150]] },
      { level: 2, total: 500, name: '白袍向导', ranges: [[151,250], [251,400], [401,650]] },
      { level: 3, total: 1500, name: '墨衣学者', ranges: [[651,950], [951,1400], [1401,2150]] },
      { level: 4, total: 4000, name: '青衿贤者', ranges: [[2151,2950], [2951,4150], [4151,6150]] },
      { level: 5, total: 10000, name: '玄冕宗师', ranges: [[6151,8150], [8151,11150], [11151,16150]] }
    ];

    const TRUTH_INTEL_PLAN = [
      { level: 1, total: 150, name: '褐衣明理', ranges: [[1,30], [31,75], [76,150]] },
      { level: 2, total: 500, name: '缁衣慎思', ranges: [[151,250], [251,400], [401,650]] },
      { level: 3, total: 1500, name: '朱衣审辩', ranges: [[651,950], [951,1400], [1401,2150]] },
      { level: 4, total: 4000, name: '紫绶格物', ranges: [[2151,2950], [2951,4150], [4151,6150]] },
      { level: 5, total: 10000, name: '金章弘道', ranges: [[6151,8150], [8151,11150], [11151,16150]] }
    ];

    // 称号配置
    const MORNING_TITLES = [
      { level: 1, name: '星辉学徒', need: 7, reward: { intelEff: 0.05 } },
      { level: 2, name: '晨风哨卫', need: 30, reward: { intelEff: 0.05, knowledgeEff: 0.05 } },
      { level: 3, name: '夜穹守誓', need: 60, reward: { allEff: 0.05 } },
      { level: 4, name: '破晓骑士', need: 90, reward: { allEff: 0.10 } },
      { level: 5, name: '黎明星使', need: 120, reward: { allEff: 0.15 } },
      { level: 6, name: '永夜圣者', need: 180, reward: { allEff: 0.20 } },
      { level: 7, name: '晨曦领主', need: 365, reward: { allEff: 0.25 } },
      { level: 8, name: '时序主宰', need: 730, reward: { allEff: 0.30 } }
    ];
    const WILL_TITLES = [
      { level: 1, name: '晨曦微志', need: 50, reward: { charismaEff: 0.05 } },
      { level: 2, name: '坚石守心', need: 200, reward: { charismaEff: 0.10 } },
      { level: 3, name: '荆棘先锋', need: 500, reward: { charismaEff: 0.15 } },
      { level: 4, name: '钢铁铸意', need: 800, reward: { charismaEff: 0.20 } },
      { level: 5, name: '风暴不屈', need: 1200, reward: { charismaEff: 0.25 } },
      { level: 6, name: '星辰恒志', need: 2000, reward: { charismaEff: 0.30 } },
      { level: 7, name: '炽魂永燃', need: 3000, reward: { charismaEff: 0.40 } },
      { level: 8, name: '无朽之心', need: 5000, reward: { charismaEff: 0.50 } }
    ];
    const CHARISMA_TITLES = [
      { level: 1, name: '萤火微光', need: 10, reward: { allEff: 0.05 } },
      { level: 2, name: '晨露流辉', need: 50, reward: { allEff: 0.10 } },
      { level: 3, name: '星芒初绽', need: 100, reward: { allEff: 0.15 } },
      { level: 4, name: '银月颂光', need: 200, reward: { allEff: 0.20 } },
      { level: 5, name: '日冕凝华', need: 300, reward: { allEff: 0.25 } },
      { level: 6, name: '虹彩冠冕', need: 500, reward: { allEff: 0.30 } },
      { level: 7, name: '天穹律光', need: 800, reward: { allEff: 0.40 } },
      { level: 8, name: '万象圣辉', need: 1200, reward: { allEff: 0.50 } }
    ];
    const READING_TITLES = [
      { level: 1, name: '历尘星火', need: 100, reward: { intelEff: 0.05 } },
      { level: 2, name: '历溪观澜', need: 300, reward: { intelKnowEff: 0.05 } },
      { level: 3, name: '历卷拓荒', need: 600, reward: { allEff: 0.05 } },
      { level: 4, name: '历镜寻真', need: 1200, reward: { allEff: 0.10 } },
      { level: 5, name: '历川归海', need: 2000, reward: { allEff: 0.15 } },
      { level: 6, name: '历世洞明', need: 3000, reward: { allEff: 0.20 } },
      { level: 7, name: '历界织识', need: 5000, reward: { allEff: 0.25 } },
      { level: 8, name: '历象归藏', need: 8000, reward: { allEff: 0.30 } }
    ];

    // 计算职业阶段进度
    function computePlanProgress(currentValue, planArray) {
      const stages = ['初级', '中级', '高级'];
      
      // 遍历每个等级
      for (const item of planArray) {
        // 检查当前值是否在这个等级的范围内
        for (let i = 0; i < item.ranges.length; i++) {
          const [min, max] = item.ranges[i];
          if (currentValue >= min && currentValue <= max) {
            // 计算在当前阶段内的百分比
            const range = max - min + 1;
            const progress = currentValue - min + 1;
            const percent = Math.floor((progress / range) * 100);
            return {
              name: `${item.name} ${stages[i]}`,
              percent: Math.min(percent, 100)
            };
          }
        }
      }

      // 如果超过最高等级的最大值
      const lastItem = planArray[planArray.length - 1];
      if (currentValue > lastItem.ranges[2][1]) {
        return {
          name: `${lastItem.name} 高级`,
          percent: 100
        };
      }

      // 默认返回
      return { name: '无', percent: 0 };
    }

    // 计算称号
    function computeTitle(currentValue, titleArray) {
      let title = { name: '无', percent: 0, achieved: false };
      for (let i = titleArray.length - 1; i >= 0; i--) {
        const t = titleArray[i];
        if (currentValue >= t.need) {
          return { name: t.name, percent: 100, achieved: true };
        }
        if (i === 0 && currentValue < t.need) {
          return { name: '无', percent: (currentValue / t.need) * 100, achieved: false };
        }
        const prevNeed = i > 0 ? titleArray[i - 1].need : 0;
        if (currentValue < t.need && currentValue >= prevNeed) {
          const span = t.need - prevNeed;
          const offset = currentValue - prevNeed;
          return { name: t.name, percent: (offset / span) * 100, achieved: false };
        }
      }
      return title;
    }

    // 计算意志/魅力称号
    function computeWillTitle(val) {
      return computeTitle(val, WILL_TITLES);
    }
    function computeCharismaTitle(val) {
      return computeTitle(val, CHARISMA_TITLES);
    }
    function computeMorningTitle(days) {
      return computeTitle(days, MORNING_TITLES);
    }
    function computeReadingTitle(val) {
      return computeTitle(val, READING_TITLES);
    }

    // 计算当日每日记录的属性增量
    function computeDailyDeltas(rec, prevContinues) {
      const delta = { 
        intelligence: 0, 
        knowledge: 0, 
        stamina: 0, 
        willpower: 0, 
        charisma: 0, 
        artisanExp: 0,
        readingExp: 0  // 新增阅识增量
      };
      // 智力
      delta.intelligence += rec.studyMinutes / 60;
      // 知识
      delta.knowledge += rec.readingPages * 0.1;
      // 体力
      delta.stamina += rec.exerciseMinutes / 60;
      // 幻构师经验
      delta.artisanExp += (rec.drawingMinutes / 60) * 10;

      // 意志
      const WILL_INC = { 3: 1, 7: 2, 30: 3 };
      const WILL_DEC = { 1: -1, 3: -2, 7: -3 };
      ['study', 'reading', 'drawing', 'exercise'].forEach((act) => {
        if (!rec[act + 'CountWill']) return;
        const amount = act === 'reading' ? rec.readingPages : rec[act + 'Minutes'];
        const cont = prevContinues[act] || { successDays: 0, failDays: 0 };
        if (amount > 0) {
          cont.successDays += 1;
          cont.failDays = 0;
          [30, 7, 3].forEach((th) => {
            if (cont.successDays >= th) delta.willpower += WILL_INC[th];
          });
        } else {
          cont.failDays += 1;
          cont.successDays = 0;
          [7, 3, 1].forEach((th) => {
            if (cont.failDays >= th) delta.willpower += WILL_DEC[th];
          });
        }
        prevContinues[act] = cont;
      });

      // 魅力
      const sumBase = delta.intelligence + delta.knowledge + delta.stamina + delta.willpower;
      delta.charisma += sumBase * 0.1;

      // 计算阅识增量
      delta.readingExp += (rec.otherReadingMinutes / 60);  // 每小时+1阅识
      delta.readingExp += (rec.watchingMinutes / 60);      // 每小时+1阅识

      return delta;
    }

    // 计算连续状态（为每日记录意志判断，这里简化：仅计算当日是否>0则视作成功，否则视为失败，不跨天计算）
    function getPrevContinuesForToday() {
      return {
        study: { successDays: 0, failDays: 0 },
        reading: { successDays: 0, failDays: 0 },
        drawing: { successDays: 0, failDays: 0 },
        exercise: { successDays: 0, failDays: 0 }
      };
    }

    // 计算晨曦连续天数
    function computeMorningConsecutive(records) {
      const sorted = [...records].sort((a, b) => new Date(a.date) - new Date(b.date));
      let consec = 0, lastDate = null;
      sorted.forEach((r) => {
        if (r.sleptOnTime && r.wokeOnTime) {
          if (!lastDate) {
            consec = 1;
          } else {
            const prev = new Date(lastDate);
            const curr = new Date(r.date);
            const diffDays = (curr - prev) / (1000 * 60 * 60 * 24);
            if (diffDays === 1) consec += 1;
            else consec = 1;
          }
          lastDate = r.date;
        }
      });
      return consec;
    }

    // 导出所有 localStorage 数据为 JSON
    function exportAllData() {
      const keys = [
        'startDate',
        'attributes',
        'dailyRecords',
        'morningRecords',
        'dailySummaries',
        'morningConsec',
        'truthKnowValue',
        'truthIntelValue'
      ];
      const data = {};
      keys.forEach((k) => { data[k] = loadData(k, null); });
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'life_game_data.json';
      a.click();
      URL.revokeObjectURL(url);
    }

    // 导入 JSON 数据并覆盖 localStorage
    function importAllData(json) {
      const keys = Object.keys(json);
      keys.forEach((k) => {
        localStorage.setItem(k, JSON.stringify(json[k]));
      });
      location.reload();
    }

    // 重置所有 localStorage 数据
    function resetAllData() {
      if (!confirm('确定要重置所有数据吗？此操作不可撤销。')) return;
      localStorage.clear();
      location.reload();
    }

    /************* 主逻辑 *************/
    document.addEventListener('DOMContentLoaded', () => {
      // 元素引用
      const currentTimeEl = document.getElementById('currentTime');
      const daysSinceEl = document.getElementById('daysSince');
      const startDateInput = document.getElementById('startDateInput');
      const exportBtn = document.getElementById('exportBtn');
      const importFile = document.getElementById('importFile');
      const resetBtn = document.getElementById('resetBtn');
      const overviewInfoBtn = document.getElementById('overviewInfoBtn');
      const overviewInfo = document.getElementById('overviewInfo');

      // 属性元素
      const attrIntEl = document.getElementById('attrIntelligence');
      const attrKnowEl = document.getElementById('attrKnowledge');
      const attrStaEl = document.getElementById('attrStamina');
      const attrWillEl = document.getElementById('attrWillpower');
      const attrChaEl = document.getElementById('attrCharisma');
      const attrArtExpEl = document.getElementById('attrArtisanExp');
      const attrReadingExpEl = document.getElementById('attrReadingExp');

      // 职业进度元素
      const artisanProgText = document.getElementById('artisanProgressText');
      const artisanProgBar = document.getElementById('artisanProgressBar');
      const truthKnowProgText = document.getElementById('truthKnowProgressText');
      const truthKnowProgBar = document.getElementById('truthKnowProgressBar');
      const truthIntelProgText = document.getElementById('truthIntelProgressText');
      const truthIntelProgBar = document.getElementById('truthIntelProgressBar');

      // 称号元素
      const titleMorningEl = document.getElementById('titleMorning');
      const titleMorningPercEl = document.getElementById('titleMorningPerc');
      const titleWillEl = document.getElementById('titleWill');
      const titleWillPercEl = document.getElementById('titleWillPerc');
      const titleChaEl = document.getElementById('titleCharisma');
      const titleChaPercEl = document.getElementById('titleCharismaPerc');
      const titleReadingEl = document.getElementById('titleReading');
      const titleReadingPercEl = document.getElementById('titleReadingPerc');

      // CareerProgress 版块
      const cpArtText = document.getElementById('cpArtisanText');
      const cpArtBar = document.getElementById('cpArtisanBar');
      const cpKnowText = document.getElementById('cpTruthKnowText');
      const cpKnowBar = document.getElementById('cpTruthKnowBar');
      const cpIntelText = document.getElementById('cpTruthIntelText');
      const cpIntelBar = document.getElementById('cpTruthIntelBar');

      // TitleSystem 版块
      const tsMorningText = document.getElementById('tsMorningText');
      const tsMorningBar = document.getElementById('tsMorningBar');
      const tsWillText = document.getElementById('tsWillText');
      const tsWillBar = document.getElementById('tsWillBar');
      const tsChaText = document.getElementById('tsCharismaText');
      const tsChaBar = document.getElementById('tsCharismaBar');
      const tsReadingText = document.getElementById('tsReadingText');
      const tsReadingBar = document.getElementById('tsReadingBar');

      // DailyRecord 元素
      const inputStudy = document.getElementById('inputStudy');
      const inputReading = document.getElementById('inputReading');
      const inputDrawing = document.getElementById('inputDrawing');
      const inputExercise = document.getElementById('inputExercise');
      const inputOtherReading = document.getElementById('inputOtherReading');
      const inputWatching = document.getElementById('inputWatching');
      const chkStudyWill = document.getElementById('chkStudyWill');
      const chkReadingWill = document.getElementById('chkReadingWill');
      const chkDrawingWill = document.getElementById('chkDrawingWill');
      const chkExerciseWill = document.getElementById('chkExerciseWill');
      const chkOtherReadingWill = document.getElementById('chkOtherReadingWill');
      const chkWatchingWill = document.getElementById('chkWatchingWill');
      const saveDailyRecordBtn = document.getElementById('saveDailyRecord');
      const toggleDailyHistoryBtn = document.getElementById('toggleDailyHistory');
      const dailyHistoryDiv = document.getElementById('dailyHistory');
      const dailyHistoryBody = document.getElementById('dailyHistoryBody');
      const dailyHistoryPager = document.getElementById('dailyHistoryPager');

      // MorningRoutine 元素
      const chkSleptOnTime = document.getElementById('chkSleptOnTime');
      const chkWokeOnTime = document.getElementById('chkWokeOnTime');
      const chkSpecial = document.getElementById('chkSpecial');
      const saveMorningBtn = document.getElementById('saveMorningRoutine');
      const toggleMorningHistoryBtn = document.getElementById('toggleMorningHistory');
      const morningHistoryDiv = document.getElementById('morningHistory');
      const morningHistoryBody = document.getElementById('morningHistoryBody');
      const morningHistoryPager = document.getElementById('morningHistoryPager');

      // DailySummary 元素
      const summaryText = document.getElementById('summaryText');
      const saveSummaryBtn = document.getElementById('saveSummary');
      const toggleSummaryHistoryBtn = document.getElementById('toggleSummaryHistory');
      const summaryHistoryDiv = document.getElementById('summaryHistory');
      const summaryHistoryBody = document.getElementById('summaryHistoryBody');
      const summaryHistoryPager = document.getElementById('summaryHistoryPager');

      // DailyAggregate 元素
      const dailyAggregateBody = document.getElementById('dailyAggregateBody');

      // Charts
      const trendCtx = document.getElementById('trendChart').getContext('2d');
      let trendChartInstance = null;
      const chartTypeBarBtn = document.getElementById('chartTypeBar');
      const chartTypeLineBtn = document.getElementById('chartTypeLine');

      // Tab 控制
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');
      tabBtns.forEach((btn, idx) => {
        btn.addEventListener('click', () => {
          tabBtns.forEach(b => b.classList.remove('bg-gray-200'));
          btn.classList.add('bg-gray-200');
          tabContents.forEach(tc => tc.classList.add('hidden'));
          const tabId = btn.getAttribute('data-tab');
          document.getElementById(tabId).classList.remove('hidden');
        });
        // 默认激活第一个
        if (idx === 0) btn.click();
      });

      /************* 初始化状态 *************/
      // startDate
      const storedStart = loadData('startDate', '');
      startDateInput.value = storedStart;
      // 属性
      const attrs = loadData('attributes', DEFAULT_ATTRS);
      // 真理之路
      let truthKnowValue = loadData('truthKnowValue', 0);
      let truthIntelValue = loadData('truthIntelValue', 0);
      // 晨曦记录与连续天数
      let morningRecords = loadData('morningRecords', []);
      let morningConsec = loadData('morningConsec', 0);
      // 每日记录
      let dailyRecords = loadData('dailyRecords', []);
      // 每日总结
      let dailySummaries = loadData('dailySummaries', []);

      // 更新当前时间显示
      function updateCurrentTime() {
        const now = new Date();
        currentTimeEl.textContent = '当前时间：' + now.toLocaleString('zh-CN', { hour12: false });
      }
      setInterval(updateCurrentTime, 1000);
      updateCurrentTime();

      // 更新计划启动天数
      function updateDaysSince() {
        const sd = startDateInput.value;
        saveData('startDate', sd);
        const days = computeDaysSince(sd);
        daysSinceEl.textContent = '计划启动天数：' + days;
      }
      startDateInput.addEventListener('change', updateDaysSince);
      updateDaysSince();

      // Toggle 概览信息
      overviewInfoBtn.addEventListener('click', () => {
        overviewInfo.classList.toggle('hidden');
      });

      // 导出
      exportBtn.addEventListener('click', exportAllData);
      // 导入
      importFile.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (evt) => {
          try {
            const json = JSON.parse(evt.target.result);
            importAllData(json);
          } catch {
            alert('导入文件格式有误');
          }
        };
        reader.readAsText(file);
      });
      // 重置
      resetBtn.addEventListener('click', resetAllData);

      /************* 更新状态概览 *************/
      function refreshStatusOverview() {
        // 属性显示
        attrIntEl.textContent = attrs.intelligence.toFixed(1);
        attrKnowEl.textContent = attrs.knowledge.toFixed(1);
        attrStaEl.textContent = attrs.stamina.toFixed(1);
        attrWillEl.textContent = attrs.willpower.toFixed(1);
        attrChaEl.textContent = attrs.charisma.toFixed(1);
        attrArtExpEl.textContent = attrs.artisanExp.toFixed(1);
        attrReadingExpEl.textContent = attrs.readingExp.toFixed(1);

        // 职业阶段
        const artisanProg = computePlanProgress(attrs.artisanExp, ARTISAN_PLAN);
        artisanProgText.textContent = `${artisanProg.name} (${artisanProg.percent}%)`;
        artisanProgBar.style.width = artisanProg.percent + '%';

        const knowProg = computePlanProgress(truthKnowValue, TRUTH_KNOW_PLAN);
        truthKnowProgText.textContent = `${knowProg.name} (${knowProg.percent}%)`;
        truthKnowProgBar.style.width = knowProg.percent + '%';

        const intelProg = computePlanProgress(truthIntelValue, TRUTH_INTEL_PLAN);
        truthIntelProgText.textContent = `${intelProg.name} (${intelProg.percent}%)`;
        truthIntelProgBar.style.width = intelProg.percent + '%';

        // 称号
        const morningTitle = computeMorningTitle(morningConsec);
        titleMorningEl.textContent = morningTitle.name;
        titleMorningPercEl.textContent = morningTitle.achieved ? '(已获得)' : `(${morningTitle.percent.toFixed(1)}%)`;

        const willTitle = computeWillTitle(attrs.willpower);
        titleWillEl.textContent = willTitle.name;
        titleWillPercEl.textContent = willTitle.achieved ? '(已获得)' : `(${willTitle.percent.toFixed(1)}%)`;

        const chaTitle = computeCharismaTitle(attrs.charisma);
        titleChaEl.textContent = chaTitle.name;
        titleChaPercEl.textContent = chaTitle.achieved ? '(已获得)' : `(${chaTitle.percent.toFixed(1)}%)`;

        const readingTitle = computeReadingTitle(attrs.readingExp);
        titleReadingEl.textContent = readingTitle.name;
        titleReadingPercEl.textContent = readingTitle.achieved ? '(已获得)' : `(${readingTitle.percent.toFixed(1)}%)`;

        // CareerProgress 区块
        cpArtText.textContent = `${artisanProg.name} (${artisanProg.percent}%)`;
        cpArtBar.style.width = artisanProg.percent + '%';

        cpKnowText.textContent = `${knowProg.name} (${knowProg.percent}%)`;
        cpKnowBar.style.width = knowProg.percent + '%';

        cpIntelText.textContent = `${intelProg.name} (${intelProg.percent}%)`;
        cpIntelBar.style.width = intelProg.percent + '%';

        // TitleSystem 区块
        tsMorningText.textContent = `${morningTitle.name} ${morningTitle.achieved ? '(已获)' : `(${morningTitle.percent.toFixed(1)}%)`}`;
        tsMorningBar.style.width = morningTitle.percent + '%';

        tsWillText.textContent = `${willTitle.name} ${willTitle.achieved ? '(已获)' : `(${willTitle.percent.toFixed(1)}%)`}`;
        tsWillBar.style.width = willTitle.percent + '%';

        tsChaText.textContent = `${chaTitle.name} ${chaTitle.achieved ? '(已获)' : `(${chaTitle.percent.toFixed(1)}%)`}`;
        tsChaBar.style.width = chaTitle.percent + '%';

        tsReadingText.textContent = `${readingTitle.name} ${readingTitle.achieved ? '(已获)' : `(${readingTitle.percent.toFixed(1)}%)`}`;
        tsReadingBar.style.width = readingTitle.percent + '%';
      }
      refreshStatusOverview();

      /************* 每日记录 功能 *************/
      let currentPageDaily = 1, pageSizeDaily = 10;
      function renderDailyHistory() {
        const showAll = !dailyHistoryDiv.classList.contains('hidden');
        const records = dailyRecords.slice().sort((a, b) => new Date(b.date) - new Date(a.date));
        let toRender = [];
        if (showAll) {
          const start = (currentPageDaily - 1) * pageSizeDaily;
          toRender = records.slice(start, start + pageSizeDaily);
        } else {
          const today = getToday();
          toRender = records.filter(r => r.date === today);
        }
        dailyHistoryBody.innerHTML = '';
        toRender.forEach(r => {
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td class="border px-2 py-1">${r.date}</td>
            <td class="border px-2 py-1">${r.studyMinutes}</td>
            <td class="border px-2 py-1">${r.readingPages}</td>
            <td class="border px-2 py-1">${r.drawingMinutes}</td>
            <td class="border px-2 py-1">${r.exerciseMinutes}</td>
            <td class="border px-2 py-1">${r.delta.intelligence.toFixed(1)}</td>
            <td class="border px-2 py-1">${r.delta.knowledge.toFixed(1)}</td>
            <td class="border px-2 py-1">${r.delta.stamina.toFixed(1)}</td>
            <td class="border px-2 py-1">${r.delta.willpower.toFixed(1)}</td>
            <td class="border px-2 py-1">${r.delta.charisma.toFixed(1)}</td>
            <td class="border px-2 py-1">${r.delta.artisanExp.toFixed(1)}</td>
            <td class="border px-2 py-1">
              <button data-id="${r.id}" class="editDaily text-green-500 mr-2 text-xs">编辑</button>
              <button data-id="${r.id}" class="delDaily text-red-500 text-xs">删除</button>
            </td>
          `;
          dailyHistoryBody.appendChild(tr);
        });
        // 分页按钮
        dailyHistoryPager.innerHTML = '';
        if (showAll) {
          const totalPages = Math.ceil(records.length / pageSizeDaily);
          for (let i = 1; i <= totalPages; i++) {
            const btn = document.createElement('button');
            btn.textContent = i;
            btn.className = `px-2 py-1 border rounded text-xs ${i === currentPageDaily ? 'bg-blue-500 text-white' : 'bg-white'}`;
            btn.addEventListener('click', () => {
              currentPageDaily = i;
              renderDailyHistory();
            });
            dailyHistoryPager.appendChild(btn);
          }
        }
        // 绑定编辑删除
        document.querySelectorAll('.editDaily').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const id = Number(e.target.getAttribute('data-id'));
            const rec = dailyRecords.find(r => r.id === id);
            if (!rec) return;
            // 加载到表单
            inputStudy.value = rec.studyMinutes;
            inputReading.value = rec.readingPages;
            inputDrawing.value = rec.drawingMinutes;
            inputExercise.value = rec.exerciseMinutes;
            inputOtherReading.value = rec.otherReadingMinutes;
            inputWatching.value = rec.watchingMinutes;
            chkStudyWill.checked = rec.studyCountWill;
            chkReadingWill.checked = rec.readingCountWill;
            chkDrawingWill.checked = rec.drawingCountWill;
            chkExerciseWill.checked = rec.exerciseCountWill;
            chkOtherReadingWill.checked = rec.otherReadingCountWill;
            chkWatchingWill.checked = rec.watchingCountWill;
            saveDailyRecordBtn.setAttribute('data-edit-id', id);
          });
        });
        document.querySelectorAll('.delDaily').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const id = Number(e.target.getAttribute('data-id'));
            if (!confirm('确认删除该记录？')) return;
            dailyRecords = dailyRecords.filter(r => r.id !== id);
            saveData('dailyRecords', dailyRecords);
            recalcAllFromDaily();
            renderDailyHistory();
            refreshStatusOverview();
            renderDailyAggregate();
            renderTrendChart(currentChartType);
            renderTasks();
          });
        });
      }
      toggleDailyHistoryBtn.addEventListener('click', () => {
        dailyHistoryDiv.classList.toggle('hidden');
        toggleDailyHistoryBtn.textContent = dailyHistoryDiv.classList.contains('hidden') ? '展开' : '收起';
        currentPageDaily = 1;
        renderDailyHistory();
      });
      renderDailyHistory();

      // 保存或更新每日记录
      saveDailyRecordBtn.addEventListener('click', () => {
        const study = Number(inputStudy.value);
        const reading = Number(inputReading.value);
        const drawing = Number(inputDrawing.value);
        const exercise = Number(inputExercise.value);
        const otherReading = Number(inputOtherReading.value);  // 新增
        const watching = Number(inputWatching.value);          // 新增
        const recId = saveDailyRecordBtn.getAttribute('data-edit-id');
        const today = getToday();
        // 构造记录对象
        const record = {
          id: recId ? Number(recId) : Date.now(),
          date: today,
          studyMinutes: study,
          readingPages: reading,
          drawingMinutes: drawing,
          exerciseMinutes: exercise,
          otherReadingMinutes: otherReading,
          watchingMinutes: watching,
          studyCountWill: chkStudyWill.checked,
          readingCountWill: chkReadingWill.checked,
          drawingCountWill: chkDrawingWill.checked,
          exerciseCountWill: chkExerciseWill.checked,
          otherReadingCountWill: chkOtherReadingWill.checked,
          watchingCountWill: chkWatchingWill.checked,
          delta: { intelligence: 0, knowledge: 0, stamina: 0, willpower: 0, charisma: 0, artisanExp: 0 }
        };
        // 计算增量
        const prevCont = getPrevContinuesForToday();
        const dlt = computeDailyDeltas(record, prevCont);
        record.delta = dlt;
        // 更新 attrs
        attrs.intelligence += dlt.intelligence;
        attrs.knowledge += dlt.knowledge;
        attrs.stamina += dlt.stamina;
        attrs.willpower += dlt.willpower;
        attrs.charisma += dlt.charisma;
        attrs.artisanExp += dlt.artisanExp;
        attrs.readingExp += dlt.readingExp;  // 更新阅识

        // 真理之路
        truthKnowValue = attrs.knowledge;
        truthIntelValue = attrs.intelligence;
        // 保存 attrs
        saveData('attributes', attrs);
        saveData('truthKnowValue', truthKnowValue);
        saveData('truthIntelValue', truthIntelValue);

        // 更新 dailyRecords
        if (recId) {
          // 编辑
          dailyRecords = dailyRecords.map(r => r.id === Number(recId) ? record : r);
          saveDailyRecordBtn.removeAttribute('data-edit-id');
        } else {
          // 新增
          dailyRecords.unshift(record);
        }
        saveData('dailyRecords', dailyRecords);

        // 清空表单
        inputStudy.value = 0;
        inputReading.value = 0;
        inputDrawing.value = 0;
        inputExercise.value = 0;
        inputOtherReading.value = 0;
        inputWatching.value = 0;
        chkStudyWill.checked = true;
        chkReadingWill.checked = true;
        chkDrawingWill.checked = true;
        chkExerciseWill.checked = true;
        chkOtherReadingWill.checked = true;
        chkWatchingWill.checked = true;

        // 刷新界面
        renderDailyHistory();
        refreshStatusOverview();
        renderDailyAggregate();
        renderTrendChart(currentChartType);
        renderTasks();
      });

      /************* 晨曦之约 功能 *************/
      let currentPageMorning = 1, pageSizeMorning = 10;
      function renderMorningHistory() {
        const showAll = !morningHistoryDiv.classList.contains('hidden');
        const recs = morningRecords.slice().sort((a, b) => new Date(b.date) - new Date(a.date));
        let toRender = [];
        if (showAll) {
          const start = (currentPageMorning - 1) * pageSizeMorning;
          toRender = recs.slice(start, start + pageSizeMorning);
        } else {
          const today = getToday();
          toRender = recs.filter(r => r.date === today);
        }
        morningHistoryBody.innerHTML = '';
        toRender.forEach(r => {
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td class="border px-2 py-1">${r.date}</td>
            <td class="border px-2 py-1">${r.sleptOnTime ? '是' : '否'}</td>
            <td class="border px-2 py-1">${r.wokeOnTime ? '是' : '否'}</td>
            <td class="border px-2 py-1">${r.special ? '是' : '否'}</td>
            <td class="border px-2 py-1">${r.delta.willpower.toFixed(1)}</td>
            <td class="border px-2 py-1">${r.delta.stamina.toFixed(1)}</td>
            <td class="border px-2 py-1">
              <button data-id="${r.id}" class="editMorning text-green-500 mr-2 text-xs">编辑</button>
              <button data-id="${r.id}" class="delMorning text-red-500 text-xs">删除</button>
            </td>
          `;
          morningHistoryBody.appendChild(tr);
        });
        // 分页按钮
        morningHistoryPager.innerHTML = '';
        if (showAll) {
          const totalPages = Math.ceil(recs.length / pageSizeMorning);
          for (let i = 1; i <= totalPages; i++) {
            const btn = document.createElement('button');
            btn.textContent = i;
            btn.className = `px-2 py-1 border rounded text-xs ${i === currentPageMorning ? 'bg-blue-500 text-white' : 'bg-white'}`;
            btn.addEventListener('click', () => {
              currentPageMorning = i;
              renderMorningHistory();
            });
            morningHistoryPager.appendChild(btn);
          }
        }
        // 绑定编辑删除
        document.querySelectorAll('.editMorning').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const id = Number(e.target.getAttribute('data-id'));
            const rec = morningRecords.find(r => r.id === id);
            if (!rec) return;
            chkSleptOnTime.checked = rec.sleptOnTime;
            chkWokeOnTime.checked = rec.wokeOnTime;
            chkSpecial.checked = rec.special;
            saveMorningBtn.setAttribute('data-edit-id', id);
          });
        });
        document.querySelectorAll('.delMorning').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const id = Number(e.target.getAttribute('data-id'));
            if (!confirm('确认删除该记录？')) return;
            morningRecords = morningRecords.filter(r => r.id !== id);
            saveData('morningRecords', morningRecords);
            morningConsec = computeMorningConsecutive(morningRecords);
            saveData('morningConsec', morningConsec);
            renderMorningHistory();
            refreshStatusOverview();
            renderDailyAggregate();
          });
        });
      }
      toggleMorningHistoryBtn.addEventListener('click', () => {
        morningHistoryDiv.classList.toggle('hidden');
        toggleMorningHistoryBtn.textContent = morningHistoryDiv.classList.contains('hidden') ? '展开' : '收起';
        currentPageMorning = 1;
        renderMorningHistory();
      });
      renderMorningHistory();

      // 保存或更新晨曦记录
      saveMorningBtn.addEventListener('click', () => {
        const slept = chkSleptOnTime.checked;
        const woke = chkWokeOnTime.checked;
        const special = chkSpecial.checked;
        const recId = saveMorningBtn.getAttribute('data-edit-id');
        const today = getToday();
        // 计算增量
        const delta = { willpower: 0, stamina: 0 };
        if (slept && woke) {
          if (special) {
            delta.willpower += 1;
            delta.stamina += 0.5;
          }
        } else {
          if (!slept) delta.willpower -= 1;
          if (!woke) delta.stamina -= 0.5;
        }
        const record = {
          id: recId ? Number(recId) : Date.now(),
          date: today,
          sleptOnTime: slept,
          wokeOnTime: woke,
          special: special,
          delta
        };
        if (recId) {
          morningRecords = morningRecords.map(r => r.id === Number(recId) ? record : r);
          saveMorningBtn.removeAttribute('data-edit-id');
        } else {
          morningRecords.push(record);
        }
        saveData('morningRecords', morningRecords);
        // 更新属性
        attrs.willpower += delta.willpower;
        attrs.stamina += delta.stamina;
        saveData('attributes', attrs);
        // 更新连续天数
        morningConsec = computeMorningConsecutive(morningRecords);
        saveData('morningConsec', morningConsec);
        // 清除表单
        chkSleptOnTime.checked = false;
        chkWokeOnTime.checked = false;
        chkSpecial.checked = false;
        // 刷新界面
        renderMorningHistory();
        refreshStatusOverview();
        renderDailyAggregate();
      });

      /************* 每日总结 功能 *************/
      let currentPageSummary = 1, pageSizeSummary = 10;
      function renderSummaryHistory() {
        const showAll = !summaryHistoryDiv.classList.contains('hidden');
        const recs = dailySummaries.slice().sort((a, b) => new Date(b.date) - new Date(a.date));

        let toRender = [];
        if (showAll) {
          const start = (currentPageSummary - 1) * pageSizeSummary;
          toRender = recs.slice(start, start + pageSizeSummary);
        } else {
          const today = getToday();
          toRender = recs.filter(r => r.date === today);
        }
        summaryHistoryBody.innerHTML = '';
        toRender.forEach(s => {
          const li = document.createElement('li');
          li.className = 'border rounded p-2';
          li.innerHTML = `
            <div class="flex justify-between items-start">
              <div>
                <p class="font-medium text-xs mb-1">${s.date}</p>
                <p class="whitespace-pre-wrap text-xs">${s.content}</p>
              </div>
              <div class="space-x-2">
                <button data-id="${s.id}" class="editSummary text-green-500 text-xs">编辑</button>
                <button data-id="${s.id}" class="delSummary text-red-500 text-xs">删除</button>
              </div>
            </div>
          `;
          summaryHistoryBody.appendChild(li);
        });
        // 分页按钮
        summaryHistoryPager.innerHTML = '';
        if (showAll) {
          const totalPages = Math.ceil(recs.length / pageSizeSummary);
          for (let i = 1; i <= totalPages; i++) {
            const btn = document.createElement('button');
            btn.textContent = i;
            btn.className = `px-2 py-1 border rounded text-xs ${i === currentPageSummary ? 'bg-blue-500 text-white' : 'bg-white'}`;
            btn.addEventListener('click', () => {
              currentPageSummary = i;
              renderSummaryHistory();
            });
            summaryHistoryPager.appendChild(btn);
          }
        }
        // 绑定编辑删除
        document.querySelectorAll('.editSummary').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const id = Number(e.target.getAttribute('data-id'));
            const rec = dailySummaries.find(r => r.id === id);
            if (!rec) return;
            summaryText.value = rec.content;
            saveSummaryBtn.setAttribute('data-edit-id', id);
          });
        });
        document.querySelectorAll('.delSummary').forEach(btn => {
          btn.addEventListener('click', (e) => {
            const id = Number(e.target.getAttribute('data-id'));
            if (!confirm('确认删除该总结？')) return;
            dailySummaries = dailySummaries.filter(r => r.id !== id);
            saveData('dailySummaries', dailySummaries);
            renderSummaryHistory();
            renderDailyAggregate();
          });
        });
      }
      toggleSummaryHistoryBtn.addEventListener('click', () => {
        summaryHistoryDiv.classList.toggle('hidden');
        toggleSummaryHistoryBtn.textContent = summaryHistoryDiv.classList.contains('hidden') ? '展开' : '收起';
        currentPageSummary = 1;
        renderSummaryHistory();
      });
      renderSummaryHistory();

      saveSummaryBtn.addEventListener('click', () => {
        const content = summaryText.value.trim();
        if (!content) {
          alert('请输入总结内容');
          return;
        }
        const recId = saveSummaryBtn.getAttribute('data-edit-id');
        const today = getToday();
        const rec = { id: recId ? Number(recId) : Date.now(), date: today, content };
        if (recId) {
          dailySummaries = dailySummaries.map(r => r.id === Number(recId) ? rec : r);
          saveSummaryBtn.removeAttribute('data-edit-id');
        } else {
          dailySummaries.unshift(rec);
        }
        saveData('dailySummaries', dailySummaries);
        // 清空
        summaryText.value = '';
        // 刷新界面
        renderSummaryHistory();
        renderDailyAggregate();
      });

      /************* 每日总记录 汇总 *************/
      function renderDailyAggregate() {
        const today = getToday();
        
        // 计算今日属性变化
        let todayDeltas = {
          intelligence: 0,
          knowledge: 0,
          stamina: 0,
          willpower: 0,
          charisma: 0,
          artisanExp: 0
        };

        // 汇总每日记录的变化
        dailyRecords
          .filter(r => r.date === today)
          .forEach(r => {
            todayDeltas.intelligence += r.delta.intelligence;
            todayDeltas.knowledge += r.delta.knowledge;
            todayDeltas.stamina += r.delta.stamina;
            todayDeltas.willpower += r.delta.willpower;
            todayDeltas.charisma += r.delta.charisma;
            todayDeltas.artisanExp += r.delta.artisanExp;
          });

        // 加入晨曦打卡的变化
        const morningRec = morningRecords.find(r => r.date === today);
        if (morningRec) {
          todayDeltas.stamina += morningRec.delta.stamina;
          todayDeltas.willpower += morningRec.delta.willpower;
        }

        const morningStatus = morningRec 
          ? ((morningRec.sleptOnTime && morningRec.wokeOnTime) ? '成功' : '失败') 
          : '未打卡';
        const summaryRec = dailySummaries.find(r => r.date === today);
        const summaryStatus = summaryRec ? '已填写' : '未填写';

        // 渲染行，使用颜色区分正负值
        dailyAggregateBody.innerHTML = `
          <tr>
            <td class="border px-2 py-1">${today}</td>
            <td class="border px-2 py-1 ${todayDeltas.intelligence > 0 ? 'text-green-600' : todayDeltas.intelligence < 0 ? 'text-red-600' : ''}">${todayDeltas.intelligence > 0 ? '+' : ''}${todayDeltas.intelligence.toFixed(1)}</td>
            <td class="border px-2 py-1 ${todayDeltas.knowledge > 0 ? 'text-green-600' : todayDeltas.knowledge < 0 ? 'text-red-600' : ''}">${todayDeltas.knowledge > 0 ? '+' : ''}${todayDeltas.knowledge.toFixed(1)}</td>
            <td class="border px-2 py-1 ${todayDeltas.stamina > 0 ? 'text-green-600' : todayDeltas.stamina < 0 ? 'text-red-600' : ''}">${todayDeltas.stamina > 0 ? '+' : ''}${todayDeltas.stamina.toFixed(1)}</td>
            <td class="border px-2 py-1 ${todayDeltas.willpower > 0 ? 'text-green-600' : todayDeltas.willpower < 0 ? 'text-red-600' : ''}">${todayDeltas.willpower > 0 ? '+' : ''}${todayDeltas.willpower.toFixed(1)}</td>
            <td class="border px-2 py-1 ${todayDeltas.charisma > 0 ? 'text-green-600' : todayDeltas.charisma < 0 ? 'text-red-600' : ''}">${todayDeltas.charisma > 0 ? '+' : ''}${todayDeltas.charisma.toFixed(1)}</td>
            <td class="border px-2 py-1 ${todayDeltas.artisanExp > 0 ? 'text-green-600' : todayDeltas.artisanExp < 0 ? 'text-red-600' : ''}">${todayDeltas.artisanExp > 0 ? '+' : ''}${todayDeltas.artisanExp.toFixed(1)}</td>
            <td class="border px-2 py-1">${morningStatus}</td>
            <td class="border px-2 py-1">${summaryStatus}</td>
          </tr>
        `;
      }
      renderDailyAggregate();

      /************* 趋势图表 *************/
      function generateTrendData() {
        // 从每日记录累积各属性，按日期排序
        if (!dailyRecords.length) return { labels: [], dataMap: {} };
        const sorted = dailyRecords.slice().sort((a, b) => new Date(a.date) - new Date(b.date));
        let cum = { intelligence: 0, knowledge: 0, stamina: 0, willpower: 0, charisma: 0, artisanExp: 0 };
        const labels = [], dataMap = {
          intelligence: [], knowledge: [], stamina: [], willpower: [], charisma: [], artisanExp: []
        };
        sorted.forEach(r => {
          cum.intelligence += r.delta.intelligence;
          cum.knowledge += r.delta.knowledge;
          cum.stamina += r.delta.stamina;
          cum.willpower += r.delta.willpower;
          cum.charisma += r.delta.charisma;
          cum.artisanExp += r.delta.artisanExp;
          labels.push(r.date);
          Object.keys(dataMap).forEach(key => dataMap[key].push(cum[key]));
        });
        return { labels, dataMap };
      }

      let currentChartType = 'bar';
      function renderTrendChart(type) {
        const { labels, dataMap } = generateTrendData();
        if (trendChartInstance) trendChartInstance.destroy();
        trendChartInstance = new Chart(trendCtx, {
          type: type,
          data: {
            labels,
            datasets: [
              { label: '智力', data: dataMap.intelligence, borderColor: '#3b82f6', backgroundColor: '#3b82f6', tension: 0.3 },
              { label: '知识', data: dataMap.knowledge, borderColor: '#10b981', backgroundColor: '#10b981', tension: 0.3 },
              { label: '体力', data: dataMap.stamina, borderColor: '#f97316', backgroundColor: '#f97316', tension: 0.3 },
              { label: '意志', data: dataMap.willpower, borderColor: '#8b5cf6', backgroundColor: '#8b5cf6', tension: 0.3 },
              { label: '魅力', data: dataMap.charisma, borderColor: '#eab308', backgroundColor: '#eab308', tension: 0.3 },
              { label: '幻构师经验', data: dataMap.artisanExp, borderColor: '#6b7280', backgroundColor: '#6b7280', tension: 0.3 }
            ]
          },
          options: {
            responsive: true,
            scales: {
              x: { title: { display: true, text: '日期' } },
              y: { title: { display: true, text: '数值' } }
            }
          }
        });
      }
      renderTrendChart(currentChartType);

      chartTypeBarBtn.addEventListener('click', () => {
        currentChartType = 'bar';
        renderTrendChart('bar');
      });
      chartTypeLineBtn.addEventListener('click', () => {
        currentChartType = 'line';
        renderTrendChart('line');
      });

      /************* 辅助：从每日记录重算所有累积状态 *************/
      function recalcAllFromDaily() {
        // 重置 attrs
        Object.assign(attrs, { ...DEFAULT_ATTRS });
        attrs.artisanExp = DEFAULT_ATTRS.artisanExp;
        // 清空真理之路
        truthKnowValue = 0; truthIntelValue = 0;
        // 清空晨曦不变
        // 从每日记录依次应用 delta
        dailyRecords.slice().sort((a, b) => new Date(a.date) - new Date(b.date)).forEach(r => {
          attrs.intelligence += r.delta.intelligence;
          attrs.knowledge += r.delta.knowledge;
          attrs.stamina += r.delta.stamina;
          attrs.willpower += r.delta.willpower;
          attrs.charisma += r.delta.charisma;
          attrs.artisanExp += r.delta.artisanExp;
          attrs.readingExp += r.delta.readingExp;  // 汇总阅识
        });
        // 真理之路
        truthKnowValue = attrs.knowledge;
        truthIntelValue = attrs.intelligence;
        saveData('attributes', attrs);
        saveData('truthKnowValue', truthKnowValue);
        saveData('truthIntelValue', truthIntelValue);
      }

      /************* 首次加载后的更新 *************/
      recalcAllFromDaily();
      morningConsec = computeMorningConsecutive(morningRecords);
      saveData('morningConsec', morningConsec);
      refreshStatusOverview();
      renderDailyAggregate();
      renderTrendChart(currentChartType);

      // 任务系统相关常量
      const TASK_TYPES = {
        artisan: '幻构师计划',
        truth: '真理之路计划',
        morning: '晨曦之约计划',
        other: '其他'
      };

      const ATTR_TYPES = {
        intelligence: '智力',
        knowledge: '知识',
        stamina: '体力',
        willpower: '意志',
        charisma: '魅力',
        artisanExp: '幻构师经验'
      };

      const TARGET_TYPES = {
        study: '学习时长',
        reading: '阅读专著页数',
        drawing: '绘画时长',
        exercise: '运动时长',
        other: '其他'
      };

      // 渲染奖励/惩罚条目
      function renderRewardPenaltyItem(type) {
        const div = document.createElement('div');
        div.className = 'flex items-center space-x-2';
        div.innerHTML = `
          <select class="border rounded px-2 py-1">
            ${Object.entries(ATTR_TYPES).map(([key, label]) => 
              `<option value="${key}">${label}</option>`
            ).join('')}
          </select>
          <input type="number" min="0" value="0" class="border rounded px-2 py-1 w-20">
          <button class="text-red-500 text-sm delete${type}">删除</button>
        `;
        return div;
      }

      // 计算任务进度
      function computeTaskProgress(task, dailyRecords) {
        let current = 0;
        // 只统计任务创建后的记录
        const taskRecords = dailyRecords.filter(r => 
          new Date(r.date) >= new Date(task.createdAt)
        );
        
        taskRecords.forEach(record => {
          switch(task.targetType) {
            case 'study':
              current += record.studyMinutes;
              break;
            case 'reading':
              current += record.readingPages;
              break;
            case 'drawing':
              current += record.drawingMinutes;
              break;
            case 'exercise':
              current += record.exerciseMinutes;
              break;
          }
        });
        return {
          current,
          percent: Math.min(100, Math.floor((current / task.targetValue) * 100))
        };
      }

      // 计算剩余天数
      function computeRemainingDays(deadline) {
        if (!deadline) return null;
        const diff = new Date(deadline) - new Date();
        return Math.ceil(diff / (1000 * 60 * 60 * 24));
      }

      // 应用任务奖励
      function applyTaskReward(task) {
        task.rewards.forEach(r => {
          attrs[r.type] += r.value;
        });
        saveData('attributes', attrs);
        refreshStatusOverview();
      }

      // 应用任务惩罚
      function applyTaskPenalty(task) {
        task.penalties.forEach(p => {
          attrs[p.type] = Math.max(0, attrs[p.type] - p.value);
        });
        saveData('attributes', attrs);
        refreshStatusOverview();
      }

      // 任务系统元素
      const taskName = document.getElementById('taskName');
      const taskDesc = document.getElementById('taskDesc');
      const taskType = document.getElementById('taskType');
      const taskPeriod = document.getElementById('taskPeriod');
      const taskTargetType = document.getElementById('taskTargetType');
      const taskTargetValue = document.getElementById('taskTargetValue');
      const taskDeadline = document.getElementById('taskDeadline');
      const rewardsList = document.getElementById('rewardsList');
      const penaltiesList = document.getElementById('penaltiesList');
      const addReward = document.getElementById('addReward');
      const addPenalty = document.getElementById('addPenalty');
      const createTask = document.getElementById('createTask');
      const pendingTasksList = document.getElementById('pendingTasksList');
      const completedTasksList = document.getElementById('completedTasksList');

      // 加载任务数据
      let tasks = loadData('tasks', { pending: [], completed: [] });

      // 渲染任务列表
      function renderTasks() {
        // 渲染未完成任务
        pendingTasksList.innerHTML = '';
        tasks.pending.forEach(task => {
          const { current, percent } = computeTaskProgress(task, dailyRecords);
          const remaining = computeRemainingDays(task.deadline);
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td class="border px-4 py-2">${task.name}</td>
            <td class="border px-4 py-2">${TASK_TYPES[task.type]}</td>
            <td class="border px-4 py-2">
              <div class="w-full bg-gray-200 rounded h-2">
                <div class="bg-blue-500 h-2 rounded" style="width: ${percent}%"></div>
              </div>
              <span class="text-xs">${current}/${task.targetValue} (${percent}%)</span>
            </td>
            <td class="border px-4 py-2">${remaining ? remaining : '无截止日期'}</td>
            <td class="border px-4 py-2">
              <div class="text-xs">
                奖励: ${task.rewards.map(r => `${ATTR_TYPES[r.type]}+${r.value}`).join(', ')}
                <br>
                惩罚: ${task.penalties.map(p => `${ATTR_TYPES[p.type]}-${p.value}`).join(', ')}
              </div>
            </td>
            <td class="border px-4 py-2">
              <button data-id="${task.id}" class="completeTask text-green-500 text-sm mr-2">完成</button>
              <button data-id="${task.id}" class="deleteTask text-red-500 text-sm">删除</button>
            </td>
          `;
          pendingTasksList.appendChild(tr);
        });

        // 渲染已完成任务
        completedTasksList.innerHTML = '';
        tasks.completed.forEach(task => {
          const tr = document.createElement('tr');
          tr.innerHTML = `
            <td class="border px-4 py-2">${task.name}</td>
            <td class="border px-4 py-2">${TASK_TYPES[task.type]}</td>
            <td class="border px-4 py-2">${task.completedAt}</td>
            <td class="border px-4 py-2">
              ${task.rewards.map(r => `${ATTR_TYPES[r.type]}+${r.value}`).join(', ')}
            </td>
          `;
          completedTasksList.appendChild(tr);
        });

        saveData('tasks', tasks);
      }

      // 添加奖励/惩罚
      addReward.addEventListener('click', () => {
        rewardsList.appendChild(renderRewardPenaltyItem('Reward'));
      });
      addPenalty.addEventListener('click', () => {
        penaltiesList.appendChild(renderRewardPenaltyItem('Penalty'));
      });

      // 删除奖励/惩罚
      rewardsList.addEventListener('click', (e) => {
        if (e.target.classList.contains('deleteReward')) {
          e.target.closest('div').remove();
        }
      });
      penaltiesList.addEventListener('click', (e) => {
        if (e.target.classList.contains('deletePenalty')) {
          e.target.closest('div').remove();
        }
      });

      // 创建任务
      createTask.addEventListener('click', () => {
        if (!taskName.value || !taskType.value || !taskTargetType.value || 
            !taskTargetValue.value || taskTargetValue.value < 1) {
          alert('请填写必填项，目标数值需大于等于1');
          return;
        }

        const rewards = Array.from(rewardsList.children).map(div => ({
          type: div.querySelector('select').value,
          value: Number(div.querySelector('input').value)
        }));

        const penalties = Array.from(penaltiesList.children).map(div => ({
          type: div.querySelector('select').value,
          value: Number(div.querySelector('input').value)
        }));

        const task = {
          id: Date.now(),
          name: taskName.value,
          description: taskDesc.value,
          type: taskType.value,
          period: taskPeriod.value,
          targetType: taskTargetType.value,
          targetValue: Number(taskTargetValue.value),
          deadline: taskDeadline.value,
          rewards,
          penalties,
          createdAt: getToday()
        };

        tasks.pending.push(task);
        renderTasks();

        // 重置表单
        taskName.value = '';
        taskDesc.value = '';
        taskType.value = '';
        taskPeriod.value = '';
        taskTargetType.value = '';
        taskTargetValue.value = '';
        taskDeadline.value = '';
        rewardsList.innerHTML = '';
        penaltiesList.innerHTML = '';
      });

      // 完成任务
      pendingTasksList.addEventListener('click', (e) => {
        if (e.target.classList.contains('completeTask')) {
          const id = Number(e.target.getAttribute('data-id'));
          const taskIndex = tasks.pending.findIndex(t => t.id === id);
          if (taskIndex === -1) return;

          const task = tasks.pending[taskIndex];
          const { current, percent } = computeTaskProgress(task, dailyRecords);
          
          if (percent < 100) {
            if (!confirm(`当前进度为${current}/${task.targetValue} (${percent}%)，确定要标记为完成吗？`)) {
              return;
            }
          }

          // 应用奖励
          applyTaskReward(task);
          
          // 移动到已完成
          task.completedAt = getToday();
          tasks.completed.push(task);
          tasks.pending.splice(taskIndex, 1);
          
          renderTasks();
          refreshStatusOverview();
        }
        else if (e.target.classList.contains('deleteTask')) {
          const id = Number(e.target.getAttribute('data-id'));
          if (!confirm('确定要删除该任务吗？')) return;
          
          tasks.pending = tasks.pending.filter(t => t.id !== id);
          renderTasks();
        }
      });

      // 检查任务截止日期
      function checkTaskDeadlines() {
        const today = new Date();
        tasks.pending.forEach(task => {
          if (task.deadline) {
            const deadline = new Date(task.deadline);
            if (deadline < today) {
              applyTaskPenalty(task);
            }
          }
        });
      }

      // 初始渲染
      renderTasks();
      
      // 每天检查一次截止日期
      setInterval(checkTaskDeadlines, 24 * 60 * 60 * 1000);
      checkTaskDeadlines();
    });
  </script>
</body>
</html>
